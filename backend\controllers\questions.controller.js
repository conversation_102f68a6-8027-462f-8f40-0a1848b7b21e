/**
 * Questions controller
 * Handles HTTP requests for questions-related endpoints
 */
import asyncHand<PERSON> from 'express-async-handler';
import { StatusCodes } from 'http-status-codes';
import QuestionsService from '../services/questionsService.js';
import { questionFiltersSchema } from '../utils/validationSchemas.js';
import Question from '../models/question.model.js';

/**
 * Get all LeetCode questions
 * @route GET /api/v1/leetcode/questions
 * @access Public
 */
const getAllQuestions = asyncHandler(async (req, res) => {
  // Extract query parameters
  const category = req.query.category || 'all-code-essentials';
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.pageSize) || 100;

  // Validate page and pageSize
  if (page < 1) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Page must be greater than or equal to 1');
  }

  if (pageSize < 1 || pageSize > 500) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Page size must be between 1 and 500');
  }

  // Calculate skip based on page and pageSize
  const skip = (page - 1) * pageSize;

  // Extract filters
  const filters = {
    difficulty: req.query.difficulty,
    search: req.query.search
  };

  // Validate filters
  const { error, value: validatedFilters } = questionFiltersSchema.validate(filters);
  if (error) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid filters: ${error.message}`);
  }

  // Fetch questions
  const questions = await QuestionsService.getAllQuestions(
    category,
    skip,
    pageSize,
    validatedFilters
  );

  res.status(StatusCodes.OK).json(questions);
});

/**
 * Get questions by tags for DSA topics
 * @route GET /api/v1/leetcode/questions/by-tags
 * @access Public
 */
const getQuestionsByTags = asyncHandler(async (req, res) => {
  try {
    const { tags, difficulty, limit = 50 } = req.query;

    if (!tags) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Tags parameter is required'
      });
    }

    // Parse tags - can be comma-separated string or array
    let tagArray = [];
    if (typeof tags === 'string') {
      tagArray = tags.split(',').map(tag => tag.trim().toLowerCase());
    } else if (Array.isArray(tags)) {
      tagArray = tags.map(tag => tag.toLowerCase());
    }

    // Build query
    let query = {
      tags: { $in: tagArray }
    };

    // Add difficulty filter if provided
    if (difficulty) {
      query.difficulty = difficulty.toLowerCase();
    }

    // Fetch questions from database
    const questions = await Question.find(query)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 })
      .select('title questionLink difficulty tags createdAt');

    // Transform the data to match the expected format
    const transformedQuestions = questions.map((question, index) => ({
      id: question._id,
      name: question.title,
      title: question.title,
      difficulty: question.difficulty.charAt(0).toUpperCase() + question.difficulty.slice(1),
      link: question.questionLink,
      tags: question.tags,
      // Add default values for compatibility with existing components
      videoSolution: null,
      viewSolution: null,
      solutionLink: null
    }));

    res.status(StatusCodes.OK).json({
      success: true,
      count: transformedQuestions.length,
      questions: transformedQuestions
    });

  } catch (error) {
    console.error('Error fetching questions by tags:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to fetch questions by tags',
      error: error.message
    });
  }
});

export {
  getAllQuestions,
  getQuestionsByTags
};
