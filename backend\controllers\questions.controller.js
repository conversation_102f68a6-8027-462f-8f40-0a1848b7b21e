/**
 * Questions controller
 * Handles HTTP requests for questions-related endpoints
 */
import asyncHand<PERSON> from 'express-async-handler';
import { StatusCodes } from 'http-status-codes';
import QuestionsService from '../services/questionsService.js';
import { questionFiltersSchema } from '../utils/validationSchemas.js';
import Question from '../models/question.model.js';

/**
 * Get all LeetCode questions
 * @route GET /api/v1/leetcode/questions
 * @access Public
 */
const getAllQuestions = asyncHandler(async (req, res) => {
  // Extract query parameters
  const category = req.query.category || 'all-code-essentials';
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.pageSize) || 100;

  // Validate page and pageSize
  if (page < 1) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Page must be greater than or equal to 1');
  }

  if (pageSize < 1 || pageSize > 500) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Page size must be between 1 and 500');
  }

  // Calculate skip based on page and pageSize
  const skip = (page - 1) * pageSize;

  // Extract filters
  const filters = {
    difficulty: req.query.difficulty,
    search: req.query.search
  };

  // Validate filters
  const { error, value: validatedFilters } = questionFiltersSchema.validate(filters);
  if (error) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid filters: ${error.message}`);
  }

  // Fetch questions
  const questions = await QuestionsService.getAllQuestions(
    category,
    skip,
    pageSize,
    validatedFilters
  );

  res.status(StatusCodes.OK).json(questions);
});

/**
 * Get questions by tags for DSA topics
 * @route GET /api/v1/leetcode/questions/by-tags
 * @access Public
 */
const getQuestionsByTags = asyncHandler(async (req, res) => {
  try {
    const { tags, difficulty, limit = 50 } = req.query;

    if (!tags) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Tags parameter is required'
      });
    }

    // Parse tags - can be comma-separated string or array
    let tagArray = [];
    if (typeof tags === 'string') {
      tagArray = tags.split(',').map(tag => tag.trim().toLowerCase());
    } else if (Array.isArray(tags)) {
      tagArray = tags.map(tag => tag.toLowerCase());
    }

    // Build query
    let query = {
      tags: { $in: tagArray }
    };

    // Add difficulty filter if provided
    if (difficulty) {
      query.difficulty = difficulty.toLowerCase();
    }

    // Fetch questions from database
    const questions = await Question.find(query)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 })
      .select('title questionLink difficulty tags createdAt');

    // Transform the data to match the expected format
    const transformedQuestions = questions.map((question, index) => ({
      id: question._id,
      name: question.title,
      title: question.title,
      difficulty: question.difficulty.charAt(0).toUpperCase() + question.difficulty.slice(1),
      link: question.questionLink,
      tags: question.tags,
      // Add default values for compatibility with existing components
      videoSolution: null,
      viewSolution: null,
      solutionLink: null
    }));

    res.status(StatusCodes.OK).json({
      success: true,
      count: transformedQuestions.length,
      questions: transformedQuestions
    });

  } catch (error) {
    console.error('Error fetching questions by tags:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to fetch questions by tags',
      error: error.message
    });
  }
});

/**
 * Get all solutions for a specific problem from Question collection (for topics page leaderboard)
 * @route GET /api/v1/leetcode/questions/problem-solutions/:problemTitle
 * @access Public
 */
const getQuestionProblemSolutions = asyncHandler(async (req, res) => {
  try {
    const { problemTitle } = req.params;

    if (!problemTitle) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Problem title is required"
      });
    }

    // Find the question in Question collection by title
    const question = await Question.findOne({ title: problemTitle });

    if (!question) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: "Question not found in database"
      });
    }

    console.log(`Found question in questions collection with ID: ${question._id}`);

    // Import Solution model
    const Solution = (await import("../models/solution.model.js")).default;

    // Find solutions using the question field (ObjectId) for exact matching
    const solutions = await Solution.find({
      question: question._id
    })
    .populate('user', 'fullName username email')
    .sort({ createdAt: 1 }) // Sort by submission time (first submitted first)
    .lean();

    console.log(`Found ${solutions.length} solutions for problem: ${problemTitle}`);

    // Format solutions for frontend (matching the expected structure for SheetProblemLeaderboardModal)
    const formattedSolutions = solutions.map((solution, index) => ({
      _id: solution._id,
      id: solution._id,
      rank: index + 1,
      user: {
        fullName: solution.user?.fullName || null,
        username: solution.user?.username || null,
        email: solution.user?.email || ''
      },
      language: solution.language,
      code: solution.code,
      approach: solution.approach || '',
      submittedAt: solution.createdAt,
      timestamp: solution.createdAt,
      createdAt: solution.createdAt
    }));

    res.status(StatusCodes.OK).json({
      success: true,
      count: formattedSolutions.length,
      solutions: formattedSolutions,
      problemTitle: question.title,
      difficulty: question.difficulty
    });

  } catch (error) {
    console.error('Error in getQuestionProblemSolutions:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Internal Server Error",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export {
  getAllQuestions,
  getQuestionsByTags,
  getQuestionProblemSolutions
};
