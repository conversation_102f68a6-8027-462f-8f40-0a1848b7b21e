// DSA Topics data with descriptions and sample problems

export const dsaTopics = [
  {
  id: 'arrays',
  title: 'Arrays',
  intro: 'Arrays are a collection of elements identified by index or key, allowing for efficient data storage and manipulation.',
  image: 'https://deen3evddmddt.cloudfront.net/uploads/content-images/array-data-structure.webp',
  description: 'Arrays are a fundamental data structure that store elements of the same type in contiguous memory locations.',
  icon: 'FaListOl',
  keyFeatures: [
    'Stores elements of the same data type',
    'Contiguous memory allocation',
    'Allows constant-time access using index (O(1))',
    'Supports one-dimensional and multi-dimensional structures',
    'Used as the basis for other data structures like stacks, queues, and matrices'
  ],
  importantPoints: [
    'Indexing starts from 0 in most languages',
    'Array size is fixed once declared (in static arrays)',
    'Efficient for read operations, but insertion/deletion in the middle is costly',
    'Commonly used in loops for iterating over data',
    'Available in all major programming languages'
  ],
  problems: [
    {
      id: 1,
      name: 'Two Sum',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/two-sum/',
      videoSolution: 'https://www.youtube.com/watch?v=KLlXCFG5TnA',
      viewSolution: 'https://leetcode.com/problems/two-sum/solutions/'
    },
    {
      id: 2,
      name: 'Best Time to Buy and Sell Stock',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/best-time-to-buy-and-sell-stock/',
      videoSolution: 'https://www.youtube.com/watch?v=1pkOgXD63yU',
      viewSolution: 'https://leetcode.com/problems/best-time-to-buy-and-sell-stock/solutions/'
    },
    {
      id: 3,
      name: 'Contains Duplicate',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/contains-duplicate/',
      videoSolution: 'https://www.youtube.com/watch?v=3OamzN90kPg',
      viewSolution: 'https://leetcode.com/problems/contains-duplicate/solutions/'
    },
    {
      id: 4,
      name: 'Product of Array Except Self',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/product-of-array-except-self/',
      videoSolution: 'https://www.youtube.com/watch?v=bNvIQI2wAjk',
      viewSolution: 'https://leetcode.com/problems/product-of-array-except-self/solutions/'
    },
    {
      id: 5,
      name: 'Maximum Subarray',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/maximum-subarray/',
      videoSolution: 'https://www.youtube.com/watch?v=5WZl3MMT0Eg',
      viewSolution: 'https://leetcode.com/problems/maximum-subarray/solutions/'
    }
  ]
},
  {
  id: 'strings',
  title: 'Strings',
  intro: 'Strings are sequences of characters commonly used to handle and manipulate text in programming.',
  image: 'https://media.geeksforgeeks.org/wp-content/uploads/20221229144929/Strings-in-Cpp.png',
  description: 'Strings are sequences of characters used to store and manipulate text.',
  icon: 'FaCode',
  keyFeatures: [
    'Immutable in most languages like Java, Python, and JavaScript',
    'Stored as a sequence of characters in memory',
    'Supports various built-in methods like concatenation, slicing, and pattern matching',
    'Efficient for handling and processing textual data',
    'Can be iterated like arrays or lists'
  ],
  importantPoints: [
    'In most languages, strings are zero-indexed',
    'Understanding string manipulation is crucial for coding interviews',
    'Common operations include reversing, comparing, trimming, and finding substrings',
    'Regular expressions are often used with strings for pattern matching',
    'Memory usage and performance can be impacted by frequent string concatenation in some languages'
  ],
  problems: [
    {
      id: 1,
      name: 'Valid Anagram',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/valid-anagram/',
      videoSolution: 'https://www.youtube.com/watch?v=9UtInBqnCgA',
      viewSolution: 'https://leetcode.com/problems/valid-anagram/solutions/'
    },
    {
      id: 2,
      name: 'Valid Parentheses',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/valid-parentheses/',
      videoSolution: 'https://www.youtube.com/watch?v=WTzjTskDFMg',
      viewSolution: 'https://leetcode.com/problems/valid-parentheses/solutions/'
    },
    {
      id: 3,
      name: 'Longest Substring Without Repeating Characters',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/longest-substring-without-repeating-characters/',
      videoSolution: 'https://www.youtube.com/watch?v=wiGpQwVHdE0',
      viewSolution: 'https://leetcode.com/problems/longest-substring-without-repeating-characters/solutions/'
    }
  ]
},
  {
  id: 'dynamic-programming',
  title: 'Dynamic Programming',
  intro: 'Dynamic Programming (DP) is a technique to solve problems efficiently by storing solutions to overlapping subproblems.',
  image: 'https://deen3evddmddt.cloudfront.net/uploads/content-images/dynamic-programming.webp',
  description: 'Dynamic Programming is a method for solving complex problems by breaking them down into simpler subproblems.',
  icon: 'FaLayerGroup',
  keyFeatures: [
    'Solves problems by storing solutions of overlapping subproblems',
    'Improves performance by avoiding redundant computations',
    'Uses two main approaches: memoization (top-down) and tabulation (bottom-up)',
    'Widely used for optimization problems and decision-making algorithms',
    'Reduces exponential time complexity to polynomial in many problems'
  ],
  importantPoints: [
    'Check for overlapping subproblems and optimal substructure',
    'Use memoization when recursion is easier to write and understand',
    'Use tabulation when you want an iterative approach with better space efficiency',
    'Common problem types include subsets, paths, sequences, and partitioning',
    'Mastering DP is essential for coding interviews and competitive programming'
  ],
  problems: [
    {
      id: 1,
      name: 'Climbing Stairs',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/climbing-stairs/',
      videoSolution: 'https://www.youtube.com/watch?v=Y0lT9Fck7qI',
      viewSolution: 'https://leetcode.com/problems/climbing-stairs/solutions/'
    },
    {
      id: 2,
      name: 'Coin Change',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/coin-change/',
      videoSolution: 'https://www.youtube.com/watch?v=H9bfqozjoqs',
      viewSolution: 'https://leetcode.com/problems/coin-change/solutions/'
    },
    {
      id: 3,
      name: 'Longest Increasing Subsequence',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/longest-increasing-subsequence/',
      videoSolution: 'https://www.youtube.com/watch?v=cjWnW0hdF1Y',
      viewSolution: 'https://leetcode.com/problems/longest-increasing-subsequence/solutions/'
    },
    {
      id: 4,
      name: 'Word Break',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/word-break/',
      videoSolution: 'https://www.youtube.com/watch?v=Sx9NNgInc3A',
      viewSolution: 'https://leetcode.com/problems/word-break/solutions/'
    },
    {
      id: 5,
      name: 'Combination Sum IV',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/combination-sum-iv/',
      videoSolution: 'https://www.youtube.com/watch?v=dw2nMCxG0ik',
      viewSolution: 'https://leetcode.com/problems/combination-sum-iv/solutions/'
    }
  ]
},
  {
  id: 'graphs',
  title: 'Graphs',
  intro: 'Graphs are powerful data structures used to model pairwise relationships between entities, often visualized as nodes connected by edges.',
  image: 'https://www.simplilearn.com/ice9/free_resources_article_thumb/Graph%20Data%20Structure%20-%20Soni/unweighted-graph-representation-in-data-structure.png',
  description: 'Graphs are non-linear data structures consisting of nodes and edges used to represent relationships between objects.',
  icon: 'FaNetworkWired',
  keyFeatures: [
    'Consist of vertices (nodes) and edges (connections)',
    'Can be directed or undirected, weighted or unweighted',
    'Can be represented using adjacency list or matrix',
    'Suitable for modeling real-world problems like maps, networks, and dependencies',
    'Used in various algorithms such as DFS, BFS, Dijkstra’s, and Topological Sort'
  ],
  importantPoints: [
    'DFS and BFS are fundamental traversal techniques in graphs',
    'Graphs may contain cycles — important to detect in problems',
    'Choose adjacency list for sparse graphs, and matrix for dense graphs',
    'Graph problems often require visited tracking to avoid infinite loops',
    'Understanding connected components, shortest path, and spanning trees is essential'
  ],
  problems: [
    {
      id: 1,
      name: 'Number of Islands',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/number-of-islands/',
      videoSolution: 'https://www.youtube.com/watch?v=pV2kpPD66nE',
      viewSolution: 'https://leetcode.com/problems/number-of-islands/solutions/'
    },
    {
      id: 2,
      name: 'Clone Graph',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/clone-graph/',
      videoSolution: 'https://www.youtube.com/watch?v=mQeF6bN8hMk',
      viewSolution: 'https://leetcode.com/problems/clone-graph/solutions/'
    },
    {
      id: 3,
      name: 'Pacific Atlantic Water Flow',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/pacific-atlantic-water-flow/',
      videoSolution: 'https://www.youtube.com/watch?v=s-VkcjHqkGI',
      viewSolution: 'https://leetcode.com/problems/pacific-atlantic-water-flow/solutions/'
    }
  ]
},
  {
  id: 'trees',
  title: 'Trees',
  intro: 'Trees are hierarchical data structures used to represent relationships between elements in a parent-child format.',
  image: 'https://media.geeksforgeeks.org/wp-content/uploads/20221124153129/Treedatastructure.png',
  description: 'Trees are hierarchical data structures with a root value and subtrees of children with a parent node.',
  icon: 'FaTree',
  keyFeatures: [
    'Consist of nodes connected in a hierarchical manner',
    'Each node has a parent (except the root) and zero or more children',
    'Common types: Binary Tree, Binary Search Tree (BST), AVL Tree, N-ary Tree',
    'Efficient for representing hierarchical data and recursive structures',
    'Widely used in databases, file systems, and compiler design'
  ],
  importantPoints: [
    'Traversal methods include Inorder, Preorder, Postorder, and Level Order',
    'Binary Search Tree (BST) maintains sorted order for fast lookups',
    'Recursive algorithms are naturally suited for tree problems',
    'Depth and height are crucial properties in tree-based problems',
    'Understanding tree traversal is essential for solving many coding questions'
  ],
  problems: [
    {
      id: 1,
      name: 'Maximum Depth of Binary Tree',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/maximum-depth-of-binary-tree/',
      videoSolution: 'https://www.youtube.com/watch?v=hTM3phVI6YQ',
      viewSolution: 'https://leetcode.com/problems/maximum-depth-of-binary-tree/solutions/'
    },
    {
      id: 2,
      name: 'Same Tree',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/same-tree/',
      videoSolution: 'https://www.youtube.com/watch?v=vRbbcKXCxOw',
      viewSolution: 'https://leetcode.com/problems/same-tree/solutions/'
    },
    {
      id: 3,
      name: 'Binary Tree Level Order Traversal',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/binary-tree-level-order-traversal/',
      videoSolution: 'https://www.youtube.com/watch?v=6ZnyEApgFYg',
      viewSolution: 'https://leetcode.com/problems/binary-tree-level-order-traversal/solutions/'
    }
  ]
},
 {
  id: 'backtracking',
  title: 'Backtracking',
  intro: 'Backtracking is a problem-solving technique that builds solutions incrementally and abandons them as soon as it determines they cannot lead to a valid answer.',
  image: 'https://ibpublicimages.s3-us-west-2.amazonaws.com/tutorial/backtracking1.png',
  description: 'Backtracking is an algorithmic technique for solving problems recursively by trying to build a solution incrementally.',
  icon: 'FaRandom',
  keyFeatures: [
    'Explores all possible options and backtracks when a path fails',
    'Works well for problems involving combinations, permutations, and constraints',
    'Often implemented using recursion',
    'Reduces the problem space using pruning techniques',
    'Used in puzzles, games, and constraint satisfaction problems (like Sudoku, N-Queens)'
  ],
  importantPoints: [
    'Ideal for exploring all valid solutions in a search space',
    'Requires a clear base case and a way to undo choices (backtrack)',
    'Backtracking is different from brute force due to pruning invalid paths early',
    'May be optimized further with memoization or heuristics',
    'Practice common patterns like subset generation, permutation building, and combination selection'
  ],
  problems: [
    {
      id: 1,
      name: 'Subsets',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/subsets/',
      videoSolution: 'https://www.youtube.com/watch?v=REOH22Xwdkk',
      viewSolution: 'https://leetcode.com/problems/subsets/solutions/'
    },
    {
      id: 2,
      name: 'Combination Sum',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/combination-sum/',
      videoSolution: 'https://www.youtube.com/watch?v=GBKI9VSKdGg',
      viewSolution: 'https://leetcode.com/problems/combination-sum/solutions/'
    },
    {
      id: 3,
      name: 'Permutations',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/permutations/',
      videoSolution: 'https://www.youtube.com/watch?v=s7AvT7cGdSo',
      viewSolution: 'https://leetcode.com/problems/permutations/solutions/'
    }
  ]
},
{
  id: 'binary-search',
  title: 'Binary Search',
  intro: 'Binary Search is an efficient algorithm for finding an item from a sorted list ofitems by repeatedly dividing the search interval in half.',
  image: 'https://learncodingfast.com/wp-content/uploads/2020/11/binary-search.png',
  description: 'Binary Search is a fast search algorithm that finds the position of a target value within a sorted array.',
  icon: 'FaSearch',
  keyFeatures: [
    'Efficiently finds target values in sorted arrays',
    'Divides the search space in half with each step',
    'Requires a sorted array for optimal performance',
    'Time complexity is O(log n)',
    'Can be implemented iteratively or recursively'
  ],
  importantPoints: [
    'Always ensure the array is sorted before applying binary search',
    'Can be used to find the first or last occurrence of a target value',
    'Useful for problems involving range queries or finding bounds',
    'Binary search can also be applied to other data structures like trees',
    'Understanding edge cases (like duplicates) is crucial for correct implementation'
  ],
  problems: [
    {
      id: 1,
      name: 'Binary Search',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/binary-search/',
      videoSolution: 'https://www.youtube.com/watch?v=P3YID7liBug',
      viewSolution: 'https://leetcode.com/problems/binary-search/solutions/'
    },
    {
      id: 2,
      name: 'Find First and Last Position',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/find-first-and-last-position-of-element-in-sorted-array/',
      videoSolution: 'https://www.youtube.com/watch?v=6K2g1g1g1g1',
      viewSolution: 'https://leetcode.com/problems/find-first-and-last-position-of-element-in-sorted-array/solutions/'
    },
    {
      id: 3,
      name: 'Search in Rotated Sorted Array',
      difficulty: 'Medium',
      link: 'https://leetcode.com/problems/search-in-rotated-sorted-array/',
      videoSolution: 'https://www.youtube.com/watch?v=2o0j1g1g1g1',
      viewSolution: 'https://leetcode.com/problems/search-in-rotated-sorted-array/solutions/'
    }
  ]
},
{
  id: 'linked-lists',
  title: 'Linked Lists',
  intro: 'Linked Lists are linear data structures where each element points to the next, allowing for efficient insertions and deletions.',
  image: 'https://codeforwin.org/wp-content/uploads/2015/09/Singly-linked-list.png',
  description: 'Linked Lists are linear data structures consisting of nodes where each node contains data and a reference to the next node.',
  icon: 'FaLink',
  keyFeatures: [
    'Consist of nodes with data and a pointer to the next node',
    'Can be singly linked (one direction) or doubly linked (two directions)',
    'Dynamic size allows for efficient insertions and deletions',
    'Memory allocation is non-contiguous, unlike arrays',
    'Used in implementing stacks, queues, and other complex data structures'
  ],
  importantPoints: [
    'Linked lists allow for efficient insertions and deletions',
    'Singly linked lists have a single pointer per node, while doubly linked lists have two',
    'Memory usage can be higher than arrays due to node overhead',
    'Linked lists can be more difficult to traverse and manipulate than arrays',
    'Understanding the trade-offs between linked lists and arrays is crucial for choosing the right data structure'
  ],
  problems: [
    {
      id: 1,
      name: 'Reverse Linked List',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/reverse-linked-list/',
      videoSolution: 'https://www.youtube.com/watch?v=O0By4Zy8K9s',
      viewSolution: 'https://leetcode.com/problems/reverse-linked-list/solutions/'
    },
    {
      id: 2,
      name: 'Merge Two Sorted Lists',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/merge-two-sorted-lists/',
      videoSolution: 'https://www.youtube.com/watch?v=Zg2b6a5g5g5',
      viewSolution: 'https://leetcode.com/problems/merge-two-sorted-lists/solutions/'
    },
    {
      id: 3,
      name: 'Linked List Cycle',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/linked-list-cycle/',
      videoSolution: 'https://www.youtube.com/watch?v=6K2g1g1g1g1',
      viewSolution: 'https://leetcode.com/problems/linked-list-cycle/solutions/'
    }
  ]
},
{
  id: 'stacks-and-queues',
  title: 'Stacks and Queues',
  intro: 'Stacks and Queues are abstract data types that allow for efficient data management in specific orders: Last In First Out (LIFO) for stacks and First In First Out (FIFO) for queues.',
  image: 'https://miro.medium.com/v2/resize:fit:1400/1*zKnDkJpL-4GQ36kzrDiODQ.png',
  description: 'Stacks and Queues are linear data structures that follow specific order rules for adding and removing elements.',
  icon: 'FaLayerGroup',
  keyFeatures: [
    'Stacks follow Last In First Out (LIFO) order',
    'Queues follow First In First Out (FIFO) order',
    'Stacks are used for function calls, expression evaluation, and backtracking',
    'Queues are used for scheduling tasks, breadth-first search, and buffering',
    'Both can be implemented using arrays or linked lists'
  ],
  importantPoints: [
    'Stacks are often used to manage function calls and local variables',
    'Queues are commonly used in scenarios like task scheduling and print job management',
    'Understanding the differences between stacks and queues is crucial for selecting the right data structure for a problem',
    'Both data structures can be implemented using arrays or linked lists, each with its own trade-offs',
    'Mastering stacks and queues is essential for solving many algorithmic problems'
  ],
  problems: [
    {
      id: 1,
      name: 'Valid Parentheses',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/valid-parentheses/',
      videoSolution: 'https://www.youtube.com/watch?v=WTzjTskDFMg',
      viewSolution: 'https://leetcode.com/problems/valid-parentheses/solutions/'
    },
    {
      id: 2,
      name: 'Implement Queue using Stacks',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/implement-queue-using-stacks/',
      videoSolution: 'https://www.youtube.com/watch?v=6K2g1g1g1g1',
      viewSolution: 'https://leetcode.com/problems/implement-queue-using-stacks/solutions/'
    },
    {
      id: 3,
      name: 'Min Stack',
      difficulty: 'Easy',
      link: 'https://leetcode.com/problems/min-stack/',
      videoSolution: 'https://www.youtube.com/watch?v=6K2g1g1g1g1',
      viewSolution: 'https://leetcode.com/problems/min-stack/solutions/'
    }
  ]
}
];
