import { Link } from 'react-router-dom';
import LearningCard from './LearningCard';
import { dsaTopics } from '../data/dsaTopics';
import {
  FaCode,
  FaLayerGroup,
  FaNetworkWired,
  FaTree,
  FaListOl,
  FaRandom,
  FaSearch,
  FaLink,
} from 'react-icons/fa';

// Function to get the icon component based on the icon name
const getIconComponent = (iconName) => {
  switch (iconName) {
    case 'FaCode':
      return <FaCode />;
    case 'FaLayerGroup':
      return <FaLayerGroup />;
    case 'FaNetworkWired':
      return <FaNetworkWired />;
    case 'FaTree':
      return <FaTree />;
    case 'FaListOl':
      return <FaListOl />;
    case 'FaRandom':
      return <FaRandom />;
    case 'FaSearch':
      return <FaSearch />;
    case 'FaLink':
      return <FaLink />;
    default:
      return <FaCode />;
  }
};

const SolveAndLearn = () => {
  return (
    <section className="w-full py-16 max-w-7xl">
      <h2 className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">Solve and Learn</h2>
      <div className="w-full">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {dsaTopics.map((topic, index) => (
            <Link key={index} to={`/topics/${topic.id}`} className="no-underline">
              <LearningCard
                title={topic.title}
                description={topic.description}
                icon={getIconComponent(topic.icon)}
              />
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SolveAndLearn;