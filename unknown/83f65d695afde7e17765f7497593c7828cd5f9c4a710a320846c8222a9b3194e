import PropTypes from 'prop-types';
import CountUp from 'react-countup';
import { useInView } from 'react-intersection-observer';

const CommunityCard = ({ title, description, icon, link }) => {
  const { ref, inView } = useInView({ triggerOnce: true });
  const targetNumber = parseInt(title.replace(/[^\d]/g, ""), 10) || 0;

  const handleJoinClick = () => {
    window.open(link, '_blank');
  };

  return (
    <div
      ref={ref}
      className="group relative overflow-hidden rounded-xl p-1 transition-all duration-300 hover:scale-105"
    >
      <div className="relative rounded-lg p-6 flex flex-col items-center justify-center gap-4 min-h-[200px] cursor-pointer">
          <div className="text-gray-100 text-6xl mb-4 transition-transform duration-300 group-hover:scale-110">
            {icon}
          </div>
        <h3 className="text-4xl font-semibold text-purple-600/80 text-center group-hover:text-purple-600 transition-colors duration-300">
          {inView ? <CountUp start={1} end={targetNumber} duration={5} separator="," /> : "0"} +
        </h3>
        <p className="text-gray-500 text-sm text-center group-hover:text-gray-400 transition-colors duration-300">
          {description}
        </p>
        <button
          onClick={handleJoinClick}
          className="mt-4 px-6 py-2 bg-purple-900 text-purple-500 rounded-full shadow-md hover:bg-purple-100 hover:text-purple-500 transition-colors duration-300 cursor-pointer"
        >
          <span className="font-semibold">Join Now</span>
        </button>
      </div>
    </div>
  );
};

CommunityCard.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  icon: PropTypes.node.isRequired,
  link: PropTypes.string,
};

export default CommunityCard;
