import CommunityCard from "./CommunityCard";
import { Link } from "react-router-dom";
import { communityData } from "../data/communityData";
import { FaWhatsapp, FaCodepen, FaStar, FaCode } from "react-icons/fa";

const getIconComponent = (iconName) => {
  switch (iconName) {
    case "FaWhatsapp":
      return <FaWhatsapp />;
    case "FaCodepen":
      return <FaCodepen />;
    case "FaStar":
      return <FaStar />;
    default:
      return <FaCode />;
  }
};

const Community = () => {

  return (
    <section id="community" className="w-full py-16 max-w-7xl">
      <h2 className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">
        Join the Community
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 justify-center">
        {communityData.map((community, index) => (
          <Link
            key={`${community.name}-${index}`}
            className="no-underline"
          >
            <CommunityCard
              title={community.members}
              description={community.description}
              icon={getIconComponent(community.icon)}
                link={community.link}
            />
          </Link>
        ))}
      </div>
    </section>
  );
};

export default Community;
