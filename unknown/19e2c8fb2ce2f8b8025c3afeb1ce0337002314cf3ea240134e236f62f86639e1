import PropTypes from 'prop-types';

const ReviewCard = ({ title, description, icon }) => {
  return (
    <div className="group relative overflow-hidden rounded-xl p-1 transition-all duration-300 hover:scale-105">
      <div className="relative h-full bg-purple-900 backdrop-blur-sm rounded-lg p-6 flex flex-col items-center justify-center gap-4 min-h-[200px] cursor-pointer">
        <div className='
        flex items-center justify-center w-30 h-30 bg-purple-100 rounded-full shadow-lg group-hover:shadow-xl transition-shadow duration-300'>
          <div className="text-purple-600 text-3xl">
            {icon}
          </div>
        </div>
        <h3 className="text-xl font-semibold text-purple-600 text-center group-hover:text-purple-500 transition-colors duration-300">
          {title}
        </h3>
        <p className="text-gray-400 text-sm text-center group-hover:text-gray-200 transition-colors duration-300">
          {description}
        </p>
      </div>
    </div>
  );
};

ReviewCard.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  icon: PropTypes.node.isRequired
};

export default ReviewCard;