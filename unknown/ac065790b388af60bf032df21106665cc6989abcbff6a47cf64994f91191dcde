import { useState } from "react";
import { useParams } from "react-router-dom";
import { dsaTopics } from "../data/dsaTopics";
import { Search, Filter, ExternalLink, Youtube } from "lucide-react";

const DSATopicPage = () => {
  const { topicId } = useParams();
  const [searchQuery, setSearchQuery] = useState("");
  const [difficultyFilter, setDifficultyFilter] = useState("");

  // Find the current topic based on the URL parameter
  const currentTopic = dsaTopics.find((topic) => topic.id === topicId);

  if (!currentTopic) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-white text-center">
          <h2 className="text-2xl font-bold mb-4">Topic Not Found</h2>
          <p>The requested DSA topic could not be found.</p>
        </div>
      </div>
    );
  }

  // Filter problems based on search query and difficulty
  const filteredProblems = currentTopic.problems.filter((problem) => {
    const matchesSearch = problem.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesDifficulty = difficultyFilter
      ? problem.difficulty === difficultyFilter
      : true;
    return matchesSearch && matchesDifficulty;
  });

  return (
    <div className="min-h-screen py-16 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      {/* Topic Header */}
      <div className="w-full mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-center text-white mb-2">
          {currentTopic.title}
        </h1>
        <div className="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-600 mx-auto mb-6"></div>
      </div>

      {/* Topic Description with Image */}
      <div className="relative p-6 rounded-xl bg-gradient-to-br from-purple-900/30 to-transparent backdrop-blur-sm border border-purple-500/20 shadow-xl mb-10">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent rounded-xl blur-[40px]"></div>
        <div className="relative z-10">
          <h2 className="text-2xl font-bold mb-4 text-white">
            What is {currentTopic.title}?
          </h2>
          <p className="text-gray-300">{currentTopic.intro}</p>

          {/* Image Section */}
          {currentTopic.image && (
            <div className="flex justify-center mb-6 mt-6">
              <div className="relative">
                <img
                  src={currentTopic.image}
                  alt={`${currentTopic.title} visualization`}
                  className="max-w-full h-auto max-h-64 object-contain rounded-lg shadow-lg"
                  onError={(e) => {
                    e.target.style.display = "none";
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-purple-500/20 to-transparent rounded-lg pointer-events-none"></div>
              </div>
            </div>
          )}

          <p className="text-gray-300">{currentTopic.description}</p>

          {/* Key Features Section */}
          {currentTopic.keyFeatures && currentTopic.keyFeatures.length > 0 && (
            <div className="mb-6 mt-6">
              <h3 className="text-xl font-semibold text-white mb-2">
                Key Features
              </h3>
              <ul className="list-disc list-inside text-gray-300 space-y-1">
                {currentTopic.keyFeatures.map((feature, idx) => (
                  <li key={idx}>{feature}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Important Points Section */}
          {currentTopic.importantPoints &&
            currentTopic.importantPoints.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  Important Points
                </h3>
                <ul className="list-disc list-inside text-gray-300 space-y-1">
                  {currentTopic.importantPoints.map((point, idx) => (
                    <li key={idx}>{point}</li>
                  ))}
                </ul>
              </div>
            )}

            <button className="mt-6 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg transition-colors duration-300 cursor-pointer">
              <a
                href={currentTopic.link}
              >
                Read More
              </a>
            </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row gap-4 mb-8">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search problems..."
            className="w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-purple-500/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Filter size={18} className="text-gray-400" />
          </div>
          <select
            className="pl-10 pr-8 py-2 bg-gray-800/50 border border-purple-500/20 rounded-lg text-white appearance-none focus:outline-none focus:ring-2 focus:ring-purple-500/50"
            value={difficultyFilter}
            onChange={(e) => setDifficultyFilter(e.target.value)}
          >
            <option value="">All Difficulties</option>
            <option value="Easy">Easy</option>
            <option value="Medium">Medium</option>
            <option value="Hard">Hard</option>
          </select>
        </div>
      </div>

      {/* Problems Table */}
      <div className="relative overflow-x-auto rounded-xl bg-gradient-to-br from-purple-900/30 to-transparent backdrop-blur-sm border border-purple-500/20 shadow-xl">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent rounded-xl blur-[40px]"></div>
        <table className="w-full text-left text-gray-300 relative z-10">
          <thead className="text-white bg-gray-800/50 uppercase text-sm">
            <tr>
              <th className="px-6 py-4">#</th>
              <th className="px-6 py-4">Problem Name</th>
              <th className="px-6 py-4">Difficulty</th>
              <th className="px-6 py-4">View Solution</th>
              <th className="px-6 py-4">YouTube Solution</th>
            </tr>
          </thead>
          <tbody>
            {filteredProblems.length > 0 ? (
              filteredProblems.map((problem, index) => (
                <tr
                  key={problem.id}
                  className="border-b border-gray-700/30 hover:bg-purple-900/20 transition-colors"
                >
                  <td className="px-6 py-4 font-medium">{index + 1}</td>
                  <td className="px-6 py-4">
                    <a
                      href={problem.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-purple-400 hover:text-purple-300 transition-colors"
                    >
                      {problem.name}
                      <ExternalLink size={14} />
                    </a>
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium ${
                        problem.difficulty === "Easy"
                          ? "bg-green-900/30 text-green-400"
                          : problem.difficulty === "Medium"
                          ? "bg-yellow-900/30 text-yellow-400"
                          : "bg-red-900/30 text-red-400"
                      }`}
                    >
                      {problem.difficulty}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <a
                      href={problem.viewSolution}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-400 hover:text-blue-300 transition-colors flex items-center gap-1"
                    >
                      <ExternalLink size={14} />
                      View
                    </a>
                  </td>
                  <td className="px-6 py-4">
                    <a
                      href={problem.videoSolution}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-red-400 hover:text-red-300 transition-colors flex items-center gap-1"
                    >
                      <Youtube size={14} />
                      Watch
                    </a>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="5" className="px-6 py-8 text-center text-gray-400">
                  No problems found matching your criteria.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DSATopicPage;
