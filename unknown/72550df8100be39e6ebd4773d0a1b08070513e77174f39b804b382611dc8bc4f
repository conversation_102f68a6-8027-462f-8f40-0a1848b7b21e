import { useState } from 'react';
import PropTypes from 'prop-types';
import Editor from '@monaco-editor/react';
import { Sun, Moon, Plus, Minus } from 'lucide-react';

const CodeEditorForm = ({ onSubmit, isLoading }) => {
  const [username, setUsername] = useState('');
  const [code, setCode] = useState('// Write your code here');
  const [approach, setApproach] = useState('Approach:\n- \n\nTime Complexity: O()\nSpace Complexity: O()');
  const [language, setLanguage] = useState('python');
  const [error, setError] = useState('');
  const [theme, setTheme] = useState('vs-dark'); // 'vs-dark' or 'light'
  const [fontSize, setFontSize] = useState(14);

  // Map of language IDs to Monaco editor language identifiers
  const languageMap = {
    'python3': 'python',
    'java': 'java',
    'cpp': 'cpp',
    'javascript': 'javascript',
    'c': 'c'
  };

  const toggleTheme = () => {
    setTheme(theme === 'vs-dark' ? 'light' : 'vs-dark');
  };

  const increaseFontSize = () => {
    setFontSize(prev => Math.min(prev + 2, 24)); // Max font size 24
  };

  const decreaseFontSize = () => {
    setFontSize(prev => Math.max(prev - 2, 10)); // Min font size 10
  };

  const handleLanguageChange = (e) => {
    const selectedLanguage = e.target.value;
    setLanguage(selectedLanguage);

    // Set default code template based on language
    switch (selectedLanguage) {
      case 'python3':
        setCode('# Write your Python solution here\n\ndef solution():\n    pass\n');
        break;
      case 'java':
        setCode('// Write your Java solution here\n\nclass Solution {\n    public static void main(String[] args) {\n        // Your solution here\n    }\n}');
        break;
      case 'cpp':
        setCode('// Write your C++ solution here\n\n#include <iostream>\n\nint main() {\n    // Your solution here\n    return 0;\n}');
        break;
      case 'javascript':
        setCode('// Write your JavaScript solution here\n\nfunction solution() {\n    // Your code here\n}\n');
        break;
      case 'c':
        setCode('// Write your C solution here\n\n#include <stdio.h>\n\nint main() {\n    // Your solution here\n    return 0;\n}');
        break;
      default:
        setCode('// Write your code here');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!username.trim()) {
      setError('Please enter your username');
      return;
    }

    if (!code.trim()) {
      setError('Please write some code before submitting');
      return;
    }

    onSubmit({ username, code, language, approach });
  };

  return (
    <form onSubmit={handleSubmit} className="h-full flex flex-col ">
      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-2 rounded-md mb-4">
          {error}
        </div>
      )}

      <div className="flex flex-row gap-4 px-4 py-2 items-end">
        <div className="w-1/4">
          <label htmlFor="username" className="block text-gray-300 mb-1">
            Your Name
          </label>
          <input
            type="text"
            id="username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            className="w-full bg-gray-800 border border-gray-700 rounded-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            placeholder="Enter your name"
            required
          />
        </div>

        <div className="w-1/4">
          <label htmlFor="language" className="block text-gray-300 mb-1">
            Language
          </label>
          <select
            id="language"
            value={language}
            onChange={handleLanguageChange}
            className="w-full bg-gray-800 border border-gray-700 rounded-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="python3">Python</option>
            <option value="java">Java</option>
            <option value="cpp">C++</option>
            <option value="javascript">JavaScript</option>
            <option value="c">C</option>
          </select>
        </div>

        <div className="flex items-end gap-2">
          <div>
            <label className="block text-gray-300 mb-1">Theme</label>
            <button
              type="button"
              onClick={toggleTheme}
              className="bg-gray-800 border border-gray-700 rounded-md p-2 text-white hover:bg-gray-700 transition-colors"
              aria-label={theme === 'vs-dark' ? 'Switch to light theme' : 'Switch to dark theme'}
            >
              {theme === 'vs-dark' ? <Sun size={20} /> : <Moon size={20} />}
            </button>
          </div>

          <div>
            <label className="block text-gray-300 mb-1">Font Size: {fontSize}px</label>
            <div className="flex">
              <button
                type="button"
                onClick={decreaseFontSize}
                className="bg-gray-800 border border-gray-700 border-r-0 rounded-l-md p-2 text-white hover:bg-gray-700 transition-colors"
                aria-label="Decrease font size"
              >
                <Minus size={20} />
              </button>
              <button
                type="button"
                onClick={increaseFontSize}
                className="bg-gray-800 border border-gray-700 rounded-r-md p-2 text-white hover:bg-gray-700 transition-colors"
                aria-label="Increase font size"
              >
                <Plus size={20} />
              </button>
            </div>
          </div>
        </div>

        <div className="flex-grow flex justify-end">
          <button
            type="submit"
            disabled={isLoading}
            className={`bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-2 rounded-md font-medium transition-all duration-200 ${
              isLoading
                ? 'opacity-70 cursor-not-allowed'
                : 'hover:from-purple-700 hover:to-pink-700 hover:shadow-lg'
            }`}
          >
            {isLoading ? 'Submitting...' : 'Submit Solution'}
          </button>
        </div>
      </div>

      <div className="flex-grow grid grid-cols-4 gap-4 px-4 py-2">
        <div className="col-span-1">
          <label htmlFor="approach" className="block text-gray-300 mb-1">
            Your Approach
          </label>
          <textarea
            id="approach"
            value={approach}
            onChange={(e) => setApproach(e.target.value)}
            className={`w-full h-[calc(100vh-180px)] bg-gray-800 border border-gray-700 rounded-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono resize-none`}
            style={{ fontSize: `${fontSize}px` }}
            placeholder="Describe your approach, time and space complexity"
          />
        </div>

        <div className="col-span-3">
          <label htmlFor="code-editor" className="block text-gray-300 mb-1">
            Write Your Solution
          </label>
          <div className="h-[calc(100vh-180px)] border border-gray-700 rounded-md overflow-hidden">
            <Editor
              height="100%"
              defaultLanguage="python"
              language={languageMap[language] || 'plaintext'}
              value={code}
              onChange={setCode}
              theme={theme}
              options={{
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                fontSize: fontSize,
                tabSize: 2,
                automaticLayout: true,
              }}
            />
          </div>
        </div>
      </div>


    </form>
  );
};

CodeEditorForm.propTypes = {
  onSubmit: PropTypes.func.isRequired,
  isLoading: PropTypes.bool
};

CodeEditorForm.defaultProps = {
  isLoading: false
};

export default CodeEditorForm;
