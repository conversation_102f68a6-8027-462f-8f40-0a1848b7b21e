import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import "./DailyChallengeCard.css";
import CountdownTimer from "./CountdownTimer";
import Modal from "./Modal";
import CodeEditorForm from "./CodeEditorForm";
import LeaderboardCard from "./LeaderboardCard";
import { Code } from "lucide-react";
import { useAuth } from "../contexts/AuthContext";

const DailyChallengeCard = ({ challengeData }) => {
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [acceptedSolutions, setAcceptedSolutions] = useState([]);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showCoinAnimation, setShowCoinAnimation] = useState(false);
  const { isAuthenticated, user, refreshUser, updateUserData } = useAuth();

  // Load accepted solutions from database on component mount
  useEffect(() => {
    const fetchSolutions = async () => {
      try {
        // Use questionTitle instead of questionId to fetch solutions
        const response = await fetch(
          `${
            import.meta.env.VITE_API_URL
          }/api/v1/solutions/problem/${encodeURIComponent(
            challengeData.questionTitle
          )}`,
          {
            headers: {
              Accept: "application/json",
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          setAcceptedSolutions(data.solutions || []);
          // console.log(
          //   "Fetched solutions for",
          //   challengeData.questionTitle,
          //   ":",
          //   data.solutions?.length || 0
          // );
        } else {
          console.error("Failed to fetch solutions:", response.status);
          setAcceptedSolutions([]);
        }
      } catch (error) {
        console.error("Error fetching solutions:", error);
        setAcceptedSolutions([]);
      }
    };

    if (challengeData.questionTitle) {
      fetchSolutions();
    }
  }, [challengeData.questionTitle]);
  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case "easy":
        return "text-green-500";
      case "medium":
        return "text-yellow-500";
      case "hard":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const handleUploadClick = () => {
    // Check if user is authenticated
    if (!isAuthenticated()) {
      setShowLoginPrompt(true);
      return;
    }

    setIsUploadModalOpen(true);
    setSubmissionResult(null);
  };

  const handleCloseModal = () => {
    // Clear any existing polling timeouts when closing the modal
    if (window.submissionPollingTimeout) {
      clearTimeout(window.submissionPollingTimeout);
      window.submissionPollingTimeout = null;
    }

    setIsUploadModalOpen(false);
    setSubmissionResult(null);
  };

  // Function to close only the submission result modal (keeping code editor open)
  const handleCloseSubmissionResult = () => {
    // Clear any existing polling timeouts
    if (window.submissionPollingTimeout) {
      clearTimeout(window.submissionPollingTimeout);
      window.submissionPollingTimeout = null;
    }

    // Only clear the submission result, keep the code editor modal open
    setSubmissionResult(null);
  };

  // Conditional close handler for the modal
  // When showing submission result: close result and return to code editor
  // When showing code editor: close the entire modal
  const handleModalClose = () => {
    if (submissionResult) {
      // If showing submission result, just close that and go back to code editor
      // This preserves the user's code and form data
      handleCloseSubmissionResult();
    } else {
      // If showing code editor, close the entire modal
      handleCloseModal();
    }
  };

  // Function to refresh solutions from database
  const refreshSolutions = async () => {
    try {
      const response = await fetch(
        `${
          import.meta.env.VITE_API_URL
        }/api/v1/solutions/problem/${encodeURIComponent(
          challengeData.questionTitle
        )}`,
        {
          headers: {
            Accept: "application/json",
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setAcceptedSolutions(data.solutions || []);
        // console.log(
        //   "Refreshed solutions for",
        //   challengeData.questionTitle,
        //   ":",
        //   data.solutions?.length || 0
        // );
      }
    } catch (error) {
      console.error("Error refreshing solutions:", error);
    }
  };

  // Function to save solution to database
  const saveSolutionToDatabase = async ({
    submissionId,
    username,
    code,
    language,
    approach,
    data,
    challengeData,
  }) => {
    try {
      const titleSlug = challengeData.questionLink
        .split("/problems/")[1]
        ?.split("/")[0];

      const solutionData = {
        problemTitle: challengeData.questionTitle,
        problemSlug: titleSlug,
        difficulty: challengeData.difficulty,
        code,
        language,
        approach: approach || "",
        status: data.status_msg || "Accepted",
        topicTags: challengeData.topicTags || [], // Include topicTags from challenge data
        userId: user?._id, // Include userId as fallback for authentication
      };

      // console.log('Sending solution data:', solutionData);
      // console.log('User object:', user);
      // console.log('User ID:', user?._id);

      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/v1/leetcode/aftersubmit`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          credentials: "include", // Include cookies for authentication
          body: JSON.stringify(solutionData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to save solution");
      }

      const result = await response.json();
      // console.log("Solution saved to database:", result);

      // Check if the solution was marked as duplicate
      if (result.isDuplicate) {
        // Show warning but still treat as success
        return {
          ...result,
          warningMessage:
            result.warning ||
            "This solution appears to match an existing submission. Please try solving with your own unique approach for better learning.",
        };
      }

      return result;
    } catch (error) {
      console.error("Error saving solution to database:", error);
      throw error;
    }
  };

  const handleSubmitSolution = async ({
    username,
    code,
    language,
    approach,
  }) => {
    // Clear any existing polling timeouts
    if (window.submissionPollingTimeout) {
      clearTimeout(window.submissionPollingTimeout);
      window.submissionPollingTimeout = null;
    }

    setIsSubmitting(true);
    setSubmissionResult({
      loading: true,
      message: "Submitting solution...",
      details: "Please wait while we submit your solution to LeetCode.",
    });

    try {
      // Extract the title slug from the question link
      const titleSlug = challengeData.questionLink
        .split("/problems/")[1]
        ?.split("/")[0];

      if (!titleSlug) {
        throw new Error("Could not determine problem slug from question link");
      }

      // Prepare the submission payload
      const payload = {
        lang: language,
        question_id: challengeData.questionId,
        typed_code: code,
      };

      // Submit solution to LeetCode
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/v1/leetcode/submit/${titleSlug}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to submit solution (${response.status})`);
      }

      const data = await response.json();
      
      // Extract submission ID from response
      const submissionId = data.submission_id || 
                          data.submission?.id || 
                          response.headers.get("x-submission-id");

      if (!submissionId) {
        throw new Error("No submission ID received from server");
      }

      // Start checking submission status
      setSubmissionResult({
        loading: true,
        message: "Checking submission status...",
      });

      checkSubmissionStatus(submissionId, username, code, language, approach);
      
    } catch (error) {
      console.error("Error submitting solution:", error);
      setSubmissionResult({
        success: false,
        message: error.message || "An error occurred while submitting your solution",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const checkSubmissionStatus = async (
    submissionId,
    username,
    code,
    language,
    approach,
    attempt = 1
  ) => {
    const maxAttempts = 10;
    const pollInterval = 2000;

    try {
      // Update loading state
      setSubmissionResult({
        loading: true,
        message: `Checking submission status... (attempt ${attempt}/${maxAttempts})`,
        details: "Please wait while we check your solution.",
      });

      // Check if we've exceeded max attempts
      if (attempt > maxAttempts) {
        setSubmissionResult({
          success: false,
          message: "Submission timeout",
          details: "The submission is taking longer than expected. Please check your LeetCode account for the result.",
        });
        return;
      }

      // Check submission status
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/v1/leetcode/submissions/${submissionId}/check`,
        {
          headers: { Accept: "application/json" },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to check submission status (${response.status})`);
      }

      const data = await response.json();

      // Handle completed submission
      if (data.state === "SUCCESS") {
        clearTimeout(window.submissionPollingTimeout);

        const isAccepted = data.status_code === 10 || 
                          (data.status_msg === "Accepted" && data.run_success === true);

        if (isAccepted) {
          // Format success result
          const resultDetails = [
            `Status: ${data.status_msg || "Accepted"}`,
            `Runtime: ${data.status_runtime || "N/A"}`,
            `Memory: ${data.status_memory || "N/A"}`,
            `Runtime Percentile: ${data.runtime_percentile?.toFixed(2) || "N/A"}%`,
            `Memory Percentile: ${data.memory_percentile?.toFixed(2) || "N/A"}%`,
            `Test Cases: ${data.total_correct || "N/A"}/${data.total_testcases || "N/A"}`,
          ].join("\n");

          setSubmissionResult({
            success: true,
            message: "Solution accepted! Your solution has been added to the community solutions.",
            details: resultDetails,
            fullData: data,
          });

          // Trigger coin animation for successful submission
          setShowCoinAnimation(true);
          setTimeout(() => setShowCoinAnimation(false), 3000);

          // Save to database if authenticated
          if (isAuthenticated()) {
            try {
              const saveResult = await saveSolutionToDatabase({
                submissionId, username, code, language, approach, data, challengeData,
              });

              if (saveResult.warningMessage) {
                setSubmissionResult(prev => ({
                  ...prev,
                  details: `${resultDetails}\n\n⚠️ ${saveResult.warningMessage}`,
                  warning: saveResult.warningMessage,
                }));
              }

              await refreshSolutions();

              if (saveResult.updatedUser) {
                updateUserData(saveResult.updatedUser);
              }
              if (refreshUser) {
                await refreshUser();
              }
            } catch (error) {
              console.error("Error saving solution:", error);
            }
          }
        } else {
          // Format rejection result
          let errorDetails = `Error: ${data.status_msg}`;
          if (data.compile_error) errorDetails += `\n\nCompile Error:\n${data.compile_error}`;
          if (data.runtime_error) errorDetails += `\n\nRuntime Error:\n${data.runtime_error}`;
          if (data.last_testcase) errorDetails += `\n\nFailed Test Case:\n${data.last_testcase}`;
          if (data.total_correct && data.total_testcases) {
            errorDetails += `\n\nTest Cases: ${data.total_correct}/${data.total_testcases}`;
          }

          setSubmissionResult({
            success: false,
            message: "Solution rejected",
            details: errorDetails,
            fullData: data,
          });
        }
        return;
      }

      // Handle still processing states
      if (data.state === "PENDING" || data.state === "STARTED") {
        const stateMessages = {
          PENDING: "Solution is waiting to be evaluated...",
          STARTED: "Solution evaluation has started...",
        };

        setSubmissionResult({
          loading: true,
          message: stateMessages[data.state] || "Solution is being evaluated...",
          details: "Your solution is being processed by the judge.",
        });

        // Continue polling
        window.submissionPollingTimeout = setTimeout(
          () => checkSubmissionStatus(submissionId, username, code, language, approach, attempt + 1),
          pollInterval
        );
        return;
      }

      // Handle error state
      if (data.state === "ERROR") {
        setSubmissionResult({
          success: false,
          message: "Error evaluating solution",
          details: data.status_msg || "There was an error evaluating your solution.",
        });
        return;
      }

      // Handle unknown states - continue polling for minimal response objects
      if (Object.keys(data).length <= 2 || !data.state) {
        setSubmissionResult({
          loading: true,
          message: "Waiting for evaluation to begin...",
          details: "Your solution has been submitted and is waiting to be processed.",
        });

        window.submissionPollingTimeout = setTimeout(
          () => checkSubmissionStatus(submissionId, username, code, language, approach, attempt + 1),
          pollInterval
        );
        return;
      }

      // Handle completely unknown states
      setSubmissionResult({
        success: false,
        message: "Unknown submission state",
        details: "The submission returned an unknown state. Please check your LeetCode account for the result.",
        fullData: data,
      });

    } catch (error) {
      console.error("Error checking submission status:", error);

      // Retry on error up to max attempts
      if (attempt < maxAttempts) {
        window.submissionPollingTimeout = setTimeout(
          () => checkSubmissionStatus(submissionId, username, code, language, approach, attempt + 1),
          2000
        );
      } else {
        setSubmissionResult({
          success: false,
          message: "Error checking submission",
          details: error.message || "An error occurred while checking your submission",
        });
      }
    }
  };

  return (
    <div className="flex flex-col items-stretch w-full p-3 xs:p-4 md:p-6 space-y-4 xs:space-y-6">
      <div className="flex flex-col lg:flex-row gap-6 xs:gap-8 lg:gap-16 max-w-6xl mx-auto items-start">
        {/* Left side - Challenge Card with Timer */}
        <div className="w-full lg:w-[55%] flex flex-col">
          <div className="flex justify-start mb-1 xs:mb-2 ml-2 xs:ml-4 h-[38px] items-start">
            <CountdownTimer />
          </div>
          <div
            className={`daily-challenge-card p-4 xs:p-6 md:p-8 bg-gray-900/40 backdrop-blur-sm rounded-xl border border-purple-500/20 shadow-xl w-full flex flex-col ${
              acceptedSolutions.length > 0 ? "h-[350px] xs:h-[380px] sm:h-[400px]" : ""
            }`}
          >
            <div className="card-header flex justify-between items-center mb-2 xs:mb-4">
              <span className="date text-gray-300 text-xs xs:text-sm">
                {formatDate(challengeData.date)}
              </span>
              <span
                className={`difficulty px-2 xs:px-3 py-0.5 xs:py-1 rounded-full text-xs xs:text-sm font-medium ${getDifficultyColor(
                  challengeData.difficulty
                )}`}
              >
                {challengeData.difficulty}
              </span>
            </div>

            <h2 className="title text-lg xs:text-xl sm:text-2xl font-bold mb-2 xs:mb-4 text-white">
              <span className="question-id text-gray-400 mr-1 xs:mr-2 text-sm xs:text-base">
                #{challengeData.questionFrontendId}
              </span>
              {challengeData.questionTitle}
            </h2>

            <div className="tags-container flex flex-wrap gap-1 xs:gap-2 mb-2 xs:mb-4">
              {challengeData.topicTags.map((tag, index) => (
                <span
                  key={index}
                  className="tag bg-purple-900/30 text-purple-200 px-2 xs:px-3 py-0.5 xs:py-1 rounded-full text-xs xs:text-sm"
                >
                  {tag.name}
                </span>
              ))}
            </div>

            <div
              className={`stats-container flex items-center gap-2 xs:gap-4 ${
                acceptedSolutions.length > 0 ? "mb-auto" : "mb-4 xs:mb-6"
              }`}
            >
              <div className="stat flex items-center gap-1 text-green-400 text-sm xs:text-base">
                <span className="stat-icon">👍</span>
                <span className="stat-value">{challengeData.likes}</span>
              </div>
              <div className="stat flex items-center gap-1 text-red-400 text-sm xs:text-base">
                <span className="stat-icon">👎</span>
                <span className="stat-value">{challengeData.dislikes}</span>
              </div>
              {challengeData.isPaidOnly && (
                <div className="premium-badge flex items-center gap-1 bg-yellow-500/20 text-yellow-300 px-2 xs:px-3 py-0.5 xs:py-1 rounded-full text-xs xs:text-sm">
                  <span className="premium-icon">💎</span>
                  Premium
                </div>
              )}
            </div>

            <div className="flex flex-col xs:flex-row gap-2 xs:gap-4 mt-auto">
              <a
                href={challengeData.questionLink}
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 inline-block bg-purple-600 text-white text-center px-3 xs:px-4 sm:px-6 py-2 xs:py-2.5 sm:py-3 rounded-lg hover:bg-purple-700 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg text-sm xs:text-base"
              >
                Go to Problem
              </a>

              <button
                onClick={handleUploadClick}
                className="flex-1 inline-flex items-center justify-center gap-1 xs:gap-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-3 xs:px-4 sm:px-6 py-2 xs:py-2.5 sm:py-3 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg cursor-pointer text-sm xs:text-base"
              >
                <Code size={16} className="hidden xs:block" />
                <Code size={14} className="xs:hidden" />
                Write Solution
              </button>
            </div>
          </div>
        </div>

        {/* Right side - Leaderboard */}
        <div className="w-full lg:w-[45%] flex">
          <div className="w-full flex flex-col">
            {/* Empty div to match the timer height */}
            <div className="mb-5 xs:mb-7 h-[40px] flex items-start"></div>
            {acceptedSolutions.length > 0 ? (
              <div className="w-full h-full flex">
                <LeaderboardCard solutions={acceptedSolutions} />
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center px-2 xs:px-4">
                <div className="flex flex-col md:flex-row items-center gap-3 xs:gap-5 text-center md:text-left">
                  
                  <div className="flex-shrink-0">
                    <img
                      src="/thinking.png"
                      alt="No solutions yet"
                      className="w-40 h-40 xs:w-50 xs:h-50 sm:w-70 sm:h-70 opacity-90"
                    />
                  </div>

                  <div>
                    <h3 className="text-xl xs:text-2xl sm:text-3xl font-bold text-white mb-1 xs:mb-2 md:mb-3">
                      No Solutions
                    </h3>
                    <p className="text-gray-400 text-sm xs:text-base md:text-lg">
                      Be the first to solve
                      <br />
                      today&apos;s challenge!
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Upload Solution Modal */}
      <Modal
        isOpen={isUploadModalOpen}
        onClose={handleModalClose}
        title="Write Your Solution"
      >
        {submissionResult ? (
          <div className="h-full flex flex-col">
            <div
              className={`flex-grow flex flex-col items-center justify-center p-8 ${
                submissionResult.loading
                  ? "text-blue-200"
                  : submissionResult.success
                  ? "text-green-200"
                  : "text-red-200"
              }`}
            >
              {submissionResult.loading && (
                <div className="flex flex-col items-center justify-center w-full max-w-2xl">
                  <div className="flex items-center mb-6">
                    <div className="w-8 h-8 border-3 border-t-transparent border-blue-500 rounded-full animate-spin mr-3"></div>
                    <span className="text-xl font-medium">
                      {submissionResult.message}
                    </span>
                  </div>
                  {submissionResult.details && (
                    <div className="w-full p-4 bg-gray-800/50 border border-gray-700 rounded-md">
                      <p className="text-gray-300">
                        {submissionResult.details}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {!submissionResult.loading && (
                <div className="w-full max-w-3xl">
                  <div
                    className={`p-6 rounded-lg shadow-lg mb-8 ${
                      submissionResult.success
                        ? "bg-green-900/30 border border-green-500"
                        : "bg-red-900/30 border border-red-500"
                    }`}
                  >
                    <h3 className="text-2xl font-bold mb-6 text-center">
                      {submissionResult.message}
                    </h3>

                    {submissionResult.details && (
                      <div className="bg-gray-800/50 rounded-md p-6 font-mono text-base">
                        <pre className="whitespace-pre-wrap">
                          {submissionResult.details}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {!submissionResult.loading && (
              <div className="px-8 py-4 border-t border-gray-800">
                <button
                  onClick={handleCloseSubmissionResult}
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-3 rounded-md font-medium transition-colors"
                >
                  Close
                </button>
              </div>
            )}
          </div>
        ) : (
          <CodeEditorForm
            onSubmit={handleSubmitSolution}
            isLoading={isSubmitting}
            user={user}
            challengeData={challengeData}
          />
        )}
      </Modal>

      {/* Login Prompt Modal */}
      <Modal isOpen={showLoginPrompt} onClose={() => setShowLoginPrompt(false)}>
        <div className="bg-gray-900 rounded-lg p-8 max-w-md mx-auto">
          <div className="text-center">
            <div className="mb-6">
              <div className="mx-auto w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mb-4">
                <Code size={32} className="text-white" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">
                Login Required
              </h3>
              <p className="text-gray-300">
                You need to be logged in to submit solutions and participate in
                the daily challenge leaderboard.
              </p>
            </div>

            <div className="space-y-4">
              <a
                href="/signin"
                className="block w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-3 px-6 rounded-lg font-medium transition-colors"
              >
                Sign In
              </a>
              <a
                href="/signup"
                className="block w-full bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
              >
                Create Account
              </a>
              <button
                onClick={() => setShowLoginPrompt(false)}
                className="block w-full text-gray-400 hover:text-white py-2 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </Modal>

      {/* Coin Animation for XP Reward */}
      {showCoinAnimation && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center pointer-events-none">
          <div className="relative">
            {/* Coin Animation */}
            <div className="coin-animation">
              <div className="coin">
                <div className="coin-inner">
                  <div className="coin-front">
                    <div className="coin-symbol">⭐</div>
                  </div>
                  <div className="coin-back">
                    <div className="coin-symbol">⭐</div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* XP Text Animation */}
            <div className="xp-text-animation">
              <div className="xp-text">
                <span className="xp-plus">+</span>
                <span className="xp-number">10</span>
                <span className="xp-label">XP</span>
              </div>
            </div>
            
            {/* Sparkle Effects */}
            <div className="sparkles">
              {[...Array(8)].map((_, i) => (
                <div key={i} className={`sparkle sparkle-${i + 1}`}>✨</div>
              ))}
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .coin-animation {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          animation: coinDrop 3s ease-out forwards;
        }

        .coin {
          width: 80px;
          height: 80px;
          position: relative;
          transform-style: preserve-3d;
          animation: coinSpin 3s linear infinite;
        }

        .coin-inner {
          position: absolute;
          width: 100%;
          height: 100%;
          transform-style: preserve-3d;
        }

        .coin-front,
        .coin-back {
          position: absolute;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          background: linear-gradient(145deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%);
          border: 3px solid #b8860b;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .coin-front {
          transform: rotateY(0deg) translateZ(2px);
        }

        .coin-back {
          transform: rotateY(180deg) translateZ(2px);
        }

        .coin-symbol {
          font-size: 32px;
          color: #b8860b;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .xp-text-animation {
          position: absolute;
          top: -60px;
          left: 50%;
          transform: translateX(-50%);
          animation: xpTextRise 3s ease-out forwards;
        }

        .xp-text {
          display: flex;
          align-items: center;
          gap: 2px;
          font-weight: bold;
          color: #00ff88;
          text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
          font-size: 24px;
        }

        .xp-plus {
          animation: xpPlusGlow 0.5s ease-out 1s forwards;
        }

        .xp-number {
          animation: xpNumberGlow 0.5s ease-out 1.2s forwards;
        }

        .xp-label {
          animation: xpLabelGlow 0.5s ease-out 1.4s forwards;
        }

        .sparkles {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 200px;
          height: 200px;
          pointer-events: none;
        }

        .sparkle {
          position: absolute;
          font-size: 16px;
          opacity: 0;
          animation: sparkleAnimation 2s ease-out forwards;
        }

        .sparkle-1 { top: 20%; left: 20%; animation-delay: 0.5s; }
        .sparkle-2 { top: 20%; right: 20%; animation-delay: 0.7s; }
        .sparkle-3 { bottom: 20%; left: 20%; animation-delay: 0.9s; }
        .sparkle-4 { bottom: 20%; right: 20%; animation-delay: 1.1s; }
        .sparkle-5 { top: 10%; left: 50%; animation-delay: 0.6s; }
        .sparkle-6 { bottom: 10%; left: 50%; animation-delay: 1.0s; }
        .sparkle-7 { top: 50%; left: 10%; animation-delay: 0.8s; }
        .sparkle-8 { top: 50%; right: 10%; animation-delay: 1.2s; }

        @keyframes coinDrop {
          0% {
            transform: translateY(-100px) scale(0.5);
            opacity: 0;
          }
          20% {
            transform: translateY(0px) scale(1.1);
            opacity: 1;
          }
          30% {
            transform: translateY(-10px) scale(1);
          }
          40% {
            transform: translateY(0px) scale(1);
          }
          100% {
            transform: translateY(0px) scale(1);
            opacity: 1;
          }
        }

        @keyframes coinSpin {
          0% { transform: rotateY(0deg); }
          100% { transform: rotateY(360deg); }
        }

        @keyframes xpTextRise {
          0% {
            transform: translateX(-50%) translateY(20px);
            opacity: 0;
          }
          20% {
            transform: translateX(-50%) translateY(0px);
            opacity: 1;
          }
          80% {
            transform: translateX(-50%) translateY(-10px);
            opacity: 1;
          }
          100% {
            transform: translateX(-50%) translateY(-30px);
            opacity: 0;
          }
        }

        @keyframes xpPlusGlow {
          0% { transform: scale(1); }
          50% { transform: scale(1.3); color: #00ff88; }
          100% { transform: scale(1); }
        }

        @keyframes xpNumberGlow {
          0% { transform: scale(1); }
          50% { transform: scale(1.3); color: #00ff88; }
          100% { transform: scale(1); }
        }

        @keyframes xpLabelGlow {
          0% { transform: scale(1); }
          50% { transform: scale(1.3); color: #00ff88; }
          100% { transform: scale(1); }
        }

        @keyframes sparkleAnimation {
          0% {
            transform: scale(0) rotate(0deg);
            opacity: 0;
          }
          50% {
            transform: scale(1) rotate(180deg);
            opacity: 1;
          }
          100% {
            transform: scale(0) rotate(360deg);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
};

DailyChallengeCard.propTypes = {
  challengeData: PropTypes.shape({
    date: PropTypes.string.isRequired,
    questionLink: PropTypes.string.isRequired,
    questionId: PropTypes.string.isRequired,
    questionFrontendId: PropTypes.string.isRequired,
    questionTitle: PropTypes.string.isRequired,
    difficulty: PropTypes.string.isRequired,
    isPaidOnly: PropTypes.bool.isRequired,
    topicTags: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string.isRequired,
        slug: PropTypes.string.isRequired,
      })
    ).isRequired,
    likes: PropTypes.number.isRequired,
    dislikes: PropTypes.number.isRequired,
  }).isRequired,
};

export default DailyChallengeCard;
