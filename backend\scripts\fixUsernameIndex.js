import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const fixUsernameIndex = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Get the users collection
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');

    // Check current indexes
    console.log('Current indexes:');
    const indexes = await usersCollection.indexes();
    console.log(indexes);

    // Drop the username index if it exists
    try {
      await usersCollection.dropIndex('username_1');
      console.log('Successfully dropped username_1 index');
    } catch (error) {
      if (error.code === 27) {
        console.log('username_1 index does not exist');
      } else {
        console.error('Error dropping username_1 index:', error);
      }
    }

    // Create a new sparse unique index on username
    try {
      await usersCollection.createIndex(
        { username: 1 }, 
        { 
          unique: true, 
          sparse: true,
          name: 'username_1_sparse'
        }
      );
      console.log('Successfully created sparse unique index on username');
    } catch (error) {
      console.error('Error creating sparse username index:', error);
    }

    // Check indexes after changes
    console.log('Indexes after changes:');
    const indexesAfter = await usersCollection.indexes();
    console.log(indexesAfter);

    console.log('Username index fix completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error fixing username index:', error);
    process.exit(1);
  }
};

fixUsernameIndex();
