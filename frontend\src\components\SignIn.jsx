import { useState } from 'react';
import { Link, useNavigate, useLocation, Route } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Eye, EyeOff } from 'lucide-react';

const SignIn = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();

  // Get the intended destination from location state, default to home
  const from = location.state?.from?.pathname || '/';

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(formData.email, formData.password);

    if (result.success) {
      // Redirect to the intended destination or home page
      navigate(from, { replace: true });
    } else {
      setError(result.error);
    }

    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-blue-900 flex flex-col overflow-auto">
      {/* Welcome Header */}
      <div className="flex justify-center pt-6 sm:pt-8 pb-2 sm:pb-4">
        <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-white to-purple-200 text-transparent bg-clip-text px-4 text-center">
          Welcome to BIG(O)
        </h1>
      </div>

      {/* Main Container */}
      <div className="flex items-center justify-center w-5xl mt-15 mx-auto px-4 sm:px-6 md:px-8 lg:px-16 py-6 gap-20 lg:gap-16">
        {/* Left Side - Illustration */}
        <div className="hidden lg:flex items-center justify-center">
          <div className="w-80 md:w-96 h-80 md:h-96 flex items-center justify-center">
            <img
              src="/side-hero.svg"
              alt="Security Illustration"
              className="w-full h-full object-contain"
            />
          </div>
        </div>

        {/* Right Side - Sign In Form */}
        <div className="w-full max-w-3xl px-4 sm:px-0">
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-4 sm:p-6 shadow-2xl border border-white/20">
            <div className="text-center mb-4 sm:mb-6">
              <h2 className="text-xl sm:text-2xl font-bold text-white mb-2">Sign in</h2>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-4 p-2 sm:p-3 rounded-lg bg-red-500/20 border border-red-500/30 text-red-300 text-sm">
                {error}
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-3 sm:space-y-4">
              {/* Username/Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-3 sm:px-4 py-2 sm:py-2.5 rounded-lg bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-200"
                  placeholder=""
                />
              </div>

              {/* Password Field */}
              <div>
                <div className='flex justify-between items-center mb-1'>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-300">
                  Password
                </label>
                
                </div>
                
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                    className="w-full px-3 sm:px-4 py-2 sm:py-2.5 pr-10 sm:pr-12 rounded-lg bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-200"
                    placeholder="••••••••"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-2 sm:right-3 top-1/2 transform -translate-y-1/2 text-gray-300 hover:text-white transition-colors duration-200"
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </div>

              {/* Forgot Password Link */}
              <div className="text-right mb-2 sm:mb-4">
                <Link
                  to="/forgot-password"
                  className="text-xs sm:text-sm text-gray-300 hover:text-white transition-colors duration-200 underline"
                >
                  Forgot Password?
                </Link>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-gray-800 to-gray-900 text-white py-2 sm:py-2.5 px-4 sm:px-6 rounded-lg font-medium hover:from-gray-700 hover:to-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Signing In...
                  </div>
                ) : (
                  'Submit'
                )}
              </button>
            </form>

            {/* Sign Up Link */}
            <div className="mt-3 sm:mt-4 text-center">
              <p className="text-gray-300 text-xs sm:text-sm">
                Don't have an account?{' '}
                <Link
                  to="/signup"
                  className="text-white hover:text-gray-200 font-medium transition-colors duration-200 underline"
                >
                  Sign up here
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignIn;
