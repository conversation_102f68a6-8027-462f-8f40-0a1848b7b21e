/**
 * LeetCode routes
 * Defines routes for LeetCode-related endpoints
 */
import express from 'express';
import { getDailyChallenge, getProblemDetails } from '../controllers/leetcode.controller.js';

const router = express.Router();

/**
 * @route   GET /api/v1/leetcode/daily
 * @desc    Get daily LeetCode challenge
 * @access  Public
 */
router.get('/daily', getDailyChallenge);

/**
 * @route   GET /api/v1/leetcode/problem/:titleSlug
 * @desc    Get LeetCode problem details by title slug
 * @access  Public
 */
router.get('/problem/:titleSlug', getProblemDetails);

export default router;
