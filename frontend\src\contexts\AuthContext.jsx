import { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState(null);

  // Check and update daily streak
  const checkDailyStreak = async () => {
    if (token && user) {
      try {
        const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/check-daily-streak`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({ userId: user.id || user._id })
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.user) {
            // Update user data if streak was modified
            if (data.streakUpdated) {
              setUser(data.user);
              localStorage.setItem('user', JSON.stringify(data.user));
              console.log('Daily streak checked and updated:', data.user.streak);
            }
            return data.user;
          }
        }
      } catch (error) {
        console.error('Error checking daily streak:', error);
      }
    }
    return null;
  };

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedUser = localStorage.getItem('user');
        const storedToken = localStorage.getItem('token');
        
        if (storedUser && storedToken) {
          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);
          setToken(storedToken);
          
          // Automatically check and update daily streak when user visits the site
          try {
            await checkDailyStreak();
          } catch (error) {
            console.error('Error checking daily streak on init:', error);
            // Don't fail initialization if streak check fails
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        // Clear invalid data
        localStorage.removeItem('user');
        localStorage.removeItem('token');
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Periodically check streak every 30 minutes when user is active
  useEffect(() => {
    let streakCheckInterval;
    if (user && token) {
      streakCheckInterval = setInterval(() => {
        checkDailyStreak();
      }, 10*60000); // Check every 10 minutes
    }

    return () => {
      if (streakCheckInterval) {
        clearInterval(streakCheckInterval);
      }
    };
  }, [user, token]);

  // Check streak when user returns to the tab/window
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && user && token) {
        // User returned to the tab, check streak
        checkDailyStreak();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
    };
  }, [user, token]);

  // Login function
  const login = async (email, password) => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }

      // Extract token from cookie or response if available
      const extractedToken = data.token || getCookieToken() || 'auth-token';
      
      // Store user data and token
      setUser(data.user);
      setToken(extractedToken);
      localStorage.setItem('user', JSON.stringify(data.user));
      localStorage.setItem('token', extractedToken);

      // Check daily streak after successful login
      try {
        await checkDailyStreak();
      } catch (error) {
        console.error('Error checking daily streak after login:', error);
      }

      return { success: true, user: data.user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };
  
  // Helper function to extract token from cookies
  const getCookieToken = () => {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.startsWith('token=')) {
        return cookie.substring('token='.length);
      }
    }
    return null;
  };

  // Register function
  const register = async (fullName, username, email, password) => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ fullName, username, email, password })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Registration failed');
      }

      // Extract token from response payload
      const extractedToken = data.token || getCookieToken() || 'auth-token';
      
      // Store user data and token
      setUser(data.user);
      setToken(extractedToken);
      localStorage.setItem('user', JSON.stringify(data.user));
      localStorage.setItem('token', extractedToken);

      return { success: true, user: data.user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Call logout endpoint
      await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/logout`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local state regardless of API call success
      setUser(null);
      setToken(null);
      localStorage.removeItem('user');
      localStorage.removeItem('token');
    }
  };

  // Get user profile
  const getProfile = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/me`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to get profile');
      }

      setUser(data.user);
      localStorage.setItem('user', JSON.stringify(data.user));
      
      return { success: true, user: data.user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    return !!(user && token);
  };

  // Refresh user data (useful after actions that might change user data like solving problems)
  const refreshUser = async () => {
    if (token) {
      try {
        const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/me`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.user) {
            setUser(data.user);
            localStorage.setItem('user', JSON.stringify(data.user));
            console.log('User data refreshed:', data.user);
            return data.user;
          }
        }
      } catch (error) {
        console.error('Error refreshing user data:', error);
      }
    }
    return null;
  };

  // Update user data directly (useful when backend provides updated user data)
  const updateUserData = (userData) => {
    if (userData) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
      console.log('User data updated directly:', updatedUser);
      return updatedUser;
    }
    return null;
  };

  const value = {
    user,
    token,
    loading,
    login,
    register,
    logout,
    getProfile,
    isAuthenticated,
    refreshUser,
    updateUserData,
    checkDailyStreak
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
