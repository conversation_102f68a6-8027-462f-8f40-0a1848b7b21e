import CommunityCard from "./CommunityCard";
import { communityData } from "../data/communityData";
import { FaWhatsapp, FaCodepen, FaStar, FaCode, FaRocket, FaUsers } from "react-icons/fa";
import { useInView } from "react-intersection-observer";

const getIconComponent = (iconName) => {
  switch (iconName) {
    case "FaWhatsapp":
      return <FaWhatsapp />;
    case "FaCodepen":
      return <FaCodepen />;
    case "FaStar":
      return <FaStar />;
    default:
      return <FaCode />;
  }
};

const Community = () => {
  const { ref, inView } = useInView({ 
    triggerOnce: true, 
    threshold: 0.1 
  });

  return (
    <section 
      id="community" 
      className="w-full py-24 max-w-7xl relative"
      ref={ref}
    >
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-900/20 to-pink-900/20 rounded-3xl blur-3xl -z-10"></div>
      
      {/* Floating elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-purple-500/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-pink-500/10 rounded-full blur-xl animate-pulse animation-delay-1000"></div>
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-blue-500/10 rounded-full blur-xl animate-pulse animation-delay-2000"></div>

      {/* Header Section */}
      <div className={`text-center mb-16 transition-all duration-1000 transform ${
        inView ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
      }`}>
        <h2 className="text-4xl font-bold mb-6 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 text-transparent bg-clip-text leading-tight">
          About Community
        </h2>
        
        <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
          Be part of our thriving ecosystem of developers, problem solvers, and tech enthusiasts
        </p>
      </div>

      {/* Stats Grid */}
      <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto transition-all duration-1000 transform delay-300 ${
        inView ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
      }`}>
        {communityData.map((community, index) => (
          <div
            key={`${community.name}-${index}`}
            className={`transition-all duration-700 transform ${
              inView ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
            }`}
            style={{ transitionDelay: `${400 + index * 200}ms` }}
          >
            <CommunityCard
              title={community.members}
              description={community.description}
              icon={getIconComponent(community.icon)}
              index={index}
            />
          </div>
        ))}
      </div>

      <style jsx>{`
        @keyframes shine {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        
        .animate-shine {
          animation: shine 2s ease-in-out infinite;
        }
        
        .animation-delay-1000 {
          animation-delay: 1000ms;
        }
        
        .animation-delay-2000 {
          animation-delay: 2000ms;
        }
      `}</style>
    </section>
  );
};

export default Community;
