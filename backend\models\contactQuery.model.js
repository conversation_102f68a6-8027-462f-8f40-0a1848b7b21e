// models/contactModel.js
import mongoose, { Schema, model } from "mongoose"
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import crypto from 'crypto'

const contactSchema = new mongoose.Schema({
  name: {
    type:String,
    required:[true,"Please enter your full Name"],
    trim: true,
    maxlength: [50, 'Full name cannot exceed 50 characters'],
    minlength: [5, 'Full name must be at least 5 characters long']
  },
  email: {
    type: String,
    required: [true, 'Please enter your email'],
    trim: true,
    unique: true,
    lowercase: true,
    match: [
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        'Please fill in a valid email address',
    ]
  },
  message: {
    type: String,
    required: [true, 'Write your Query']
  },
  date: { type: Date, default: Date.now }
});

const Query = model('Query', contactSchema)
export default Query