import User from "../models/user.model.js";
import AppError from "../utils/error.util.js"
import jwt from 'jsonwebtoken'

const isLoggedin = async (req, res, next) => {
    // Get token from cookies
    const cookieToken = req.cookies.token;
    
    // Get token from Authorization header (for deployed environment where cookies might not work)
    const authHeader = req.headers.authorization;
    let headerToken = null;
    if (authHeader && authHeader.startsWith('Bearer ')) {
        headerToken = authHeader.split(' ')[1];
    }
    
    // Get token from custom X-Auth-Token header (additional fallback)
    const customAuthToken = req.headers['x-auth-token'];
    
    // Get user ID from X-User-ID header (for user lookup fallback)
    const customUserId = req.headers['x-user-id'];
    
    // Debug logging
    console.log('Auth middleware - cookies:', req.cookies);
    console.log('Auth middleware - cookie token:', cookieToken);
    console.log('Auth middleware - auth header:', authHeader);
    console.log('Auth middleware - header token:', headerToken);
    console.log('Auth middleware - custom auth token:', customAuthToken);
    console.log('Auth middleware - custom user ID:', customUserId);
    console.log('Auth middleware - all headers:', req.headers);
    console.log('Auth middleware - request body:', req.body);
    
    // Use any available token source
    const token = cookieToken || headerToken || customAuthToken;
    
    if (!token) {
        console.log('No token found in cookies or headers');
        
        // Fallback 1: Check if userId is provided in request body
        if (req.body && req.body.userId) {
            console.log('Using userId from body for authentication:', req.body.userId);
            try {
                // Verify the user exists in database
                const user = await User.findById(req.body.userId);
                if (user) {
                    req.user = { id: user._id, email: user.email, username: user.username };
                    console.log('Auth fallback successful for user from body:', req.user);
                    return next();
                } else {
                    console.log('User not found with provided userId from body:', req.body.userId);
                }
            } catch (error) {
                console.log('Error in userId fallback from body:', error.message);
            }
        }
        
        // Fallback 2: Check if userId is provided in custom header
        if (customUserId) {
            console.log('Using userId from custom header for authentication:', customUserId);
            try {
                // Verify the user exists in database
                const user = await User.findById(customUserId);
                if (user) {
                    req.user = { id: user._id, email: user.email, username: user.username };
                    console.log('Auth fallback successful for user from custom header:', req.user);
                    return next();
                } else {
                    console.log('User not found with provided userId from custom header:', customUserId);
                }
            } catch (error) {
                console.log('Error in userId fallback from custom header:', error.message);
            }
        }
        
        return next(new AppError('You are not logged in / Unauthenticated', 401))
    }
    
    try {      
        const userDetails = await jwt.verify(token, process.env.JWT_SECRET)
        console.log('Auth middleware - verified user:', userDetails);
        req.user = userDetails
        next()
    } catch (err) {
        console.log('Auth middleware - JWT verification error:', err.message);
        return next(new AppError('You are not logged in / Unauthenticated', 401))
    }
}

// const authorizedRoles = (...roles) => async (req, res, next) =>{        
//     const curresntUserRole = req.user.role
//     if(!roles.includes(curresntUserRole)){
//         return next(new AppError('You are not authorized to access this route', 403))
//     }
//     next()
// }

export {
    isLoggedin,
    // authorizedRoles
}