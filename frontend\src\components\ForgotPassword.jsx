import { useState } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Mail } from 'lucide-react';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/forgot`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage(data.message);
        setIsSuccess(true);
      } else {
        setError(data.message || 'Failed to send reset email');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }

    setLoading(false);
  };

  return (
    <div className="h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-blue-900 flex flex-col overflow-hidden">
      {/* Welcome Header */}
      <div className="flex justify-center pt-8 pb-4">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-purple-200 text-transparent bg-clip-text">
          Reset Your Password
        </h1>
      </div>

      {/* Main Container */}
      <div className="flex items-center justify-center w-5xl mt-15 mx-auto px-8 gap-16">
        {/* Left Side - Illustration */}
        <div className="hidden lg:flex flex-1 items-center justify-center">
          <div className="w-96 h-96 flex items-center justify-center">
            <img
              src="/side-hero.svg"
              alt="Reset Password Illustration"
              className="w-full h-full object-contain"
            />
          </div>
        </div>

        {/* Right Side - Forgot Password Form */}
        <div className="flex-1 max-w-md">
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 shadow-2xl border border-white/20">
            {/* Back to Sign In Link */}
            <Link
              to="/signin"
              className="flex items-center text-gray-300 hover:text-white transition-colors duration-200 mb-6"
            >
              <ArrowLeft size={20} className="mr-2" />
              Back to Sign In
            </Link>

            <div className="text-center mb-6">
              <div className="flex justify-center mb-4">
                <div className="bg-white/20 p-3 rounded-full">
                  <Mail size={32} className="text-white" />
                </div>
              </div>
              <h2 className="text-2xl font-bold text-white mb-2">Forgot Password?</h2>
              <p className="text-gray-300 text-sm">
                Enter your email address and we'll send you a link to reset your password.
              </p>
            </div>

            {/* Success Message */}
            {isSuccess && (
              <div className="mb-4 p-4 rounded-lg bg-green-500/20 border border-green-500/30 text-green-300 text-sm">
                <div className="flex items-center">
                  <Mail size={16} className="mr-2 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Email sent successfully!</p>
                    <p className="mt-1">{message}</p>
                    <p className="mt-2 text-xs">Check your email inbox and click the reset link.</p>
                  </div>
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="mb-4 p-3 rounded-lg bg-red-500/20 border border-red-500/30 text-red-300 text-sm">
                {error}
              </div>
            )}

            {/* Form */}
            {!isSuccess && (
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Email Field */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="w-full px-4 py-2.5 rounded-lg bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-200"
                    placeholder="Enter your email address"
                  />
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-gray-800 to-gray-900 text-white py-2.5 px-6 rounded-lg font-medium hover:from-gray-700 hover:to-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Sending Email...
                    </div>
                  ) : (
                    'Send Reset Link'
                  )}
                </button>
              </form>
            )}

            {/* Try Again Button for Success State */}
            {isSuccess && (
              <div className="space-y-4">
                <button
                  onClick={() => {
                    setIsSuccess(false);
                    setMessage('');
                    setEmail('');
                  }}
                  className="w-full bg-gradient-to-r from-gray-800 to-gray-900 text-white py-2.5 px-6 rounded-lg font-medium hover:from-gray-700 hover:to-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200"
                >
                  Send Another Email
                </button>
              </div>
            )}

            {/* Sign Up Link */}
            <div className="mt-6 text-center">
              <p className="text-gray-300">
                Don't have an account?{' '}
                <Link
                  to="/signup"
                  className="text-white hover:text-gray-200 font-medium transition-colors duration-200 underline"
                >
                  Sign up here
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
