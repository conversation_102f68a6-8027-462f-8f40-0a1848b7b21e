import express from 'express';
import { register, login, logout, profile, forgotPassword, resetPassword, changePassword, updateUser, getUserStats, testAuth, getUserStatsByUserId, updateUserXPAndStreak, getGlobalLeaderboard, calculateUserXPFromSolutions, checkAndUpdateDailyStreak } from '../controllers/user.controller.js';
import { isLoggedin } from '../middlewares/auth.middleware.js';
import upload from '../middlewares/multer.middleware.js';

const router = express.Router();

router.post('/register', upload.single('avatar'), register);  //admin only
router.post('/login', login);
router.get('/logout', logout);
router.get('/me', isLoggedin, profile);
router.get('/stats', getUserStats);
router.get('/stats-auth', isLoggedin, getUserStats);
router.get('/stats/:userId', getUserStatsByUserId);
router.get('/test-auth', isLoggedin, testAuth);
router.post('/forgot', forgotPassword);
router.post('/reset/:token', resetPassword); //smtp---> env
router.post('/change-password', isLoggedin, changePassword); //smtp---> env
router.put('/update/:id', isLoggedin, upload.single('avatar'), updateUser);

// New routes for XP, streak, and leaderboard
router.post('/update-xp-streak', updateUserXPAndStreak);
router.get('/leaderboard', getGlobalLeaderboard);
router.get('/calculate-xp/:userId', calculateUserXPFromSolutions);
router.post('/check-daily-streak', checkAndUpdateDailyStreak);

export default router;
