/**
 * LeetCode controller
 * Handles HTTP requests for LeetCode-related endpoints
 */
import asyncHandler from 'express-async-handler';
import { StatusCodes } from 'http-status-codes';
import { LeetCodeService, ProblemService } from '../services/leetcodeService.js';

/**
 * Get daily LeetCode challenge
 * @route GET /api/v1/leetcode/daily
 * @access Public
 */
const getDailyChallenge = asyncHandler(async (req, res) => {
  const dailyChallenge = await LeetCodeService.fetchDailyChallenge();
  res.status(StatusCodes.OK).json(dailyChallenge);
});

/**
 * Get LeetCode problem details by title slug
 * @route GET /api/v1/leetcode/problem/:titleSlug
 * @access Public
 */
const getProblemDetails = asyncHandler(async (req, res) => {
  const { titleSlug } = req.params;
  const problemDetails = await ProblemService.fetchProblemByTitleSlug(titleSlug);
  res.status(StatusCodes.OK).json(problemDetails);
});

export {
  getDailyChallenge,
  getProblemDetails
};
