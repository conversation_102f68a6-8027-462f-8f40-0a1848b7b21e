import "./index.css";
import { useState, useEffect } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
} from "react-router-dom";
import Navbar from "./components/Navbar";
import DailyChallengeCard from "./components/DailyChallengeCard";
import SolveAndLearn from "./components/SolveAndLearn";
import RoadmapScroll from "./components/RoadmapScroll";
import DSATopicPage from "./components/DSATopicPage";
import Footer from "./components/Footer";
import Faqs from "./components/Faqs";
import SignIn from "./components/SignIn";
import SignUp from "./components/SignUp";
import ForgotPassword from "./components/ForgotPassword";
import ResetPassword from "./components/ResetPassword";
import Profile from "./components/Profile";
import Reviews from "./components/Reviews";
import Community from "./components/Community";
import Sheets from "./components/Sheets";
import OnCampus from "./components/OnCampus";
import OffCampus from "./components/OffCampus";
import ProtectedRoute from "./components/ProtectedRoute";
import GlobalLeaderboardBanner from "./components/GlobalLeaderboardBanner";
import { AuthProvider } from "./contexts/AuthContext";
import BigoImage from "./assets/images/bigo.png";
import { Typewriter } from "react-simple-typewriter";
import ScrollToTop from "./components/ScrollToTop";

import {
  FaBolt,
  FaChartLine,
  FaUsers,
  FaBookOpen,
  FaRocket,
  FaChevronLeft,
  FaChevronRight,
} from "react-icons/fa";

const carouselItems = [
  {
    icon: <FaBolt size={60} />,
    text: "Daily DSA problem-solving challenges",
  },
  {
    icon: <FaChartLine size={60} />,
    text: "Leaderboard to track consistency",
  },
  {
    icon: <FaUsers size={60} />,
    text: "Community-driven discussions and support",
  },
  {
    icon: <FaBookOpen size={60} />,
    text: "Resources for placement preparation",
  },
  {
    icon: <FaRocket size={60} />,
    text: "Beginner to advanced level content",
  },
];

// Component to conditionally render Navbar
const ConditionalNavbar = () => {
  const location = useLocation();
  const hideNavbarPaths = [
    "/signin",
    "/signup",
    "/forgot-password",
    "/reset-password",
  ];

  if (hideNavbarPaths.some((path) => location.pathname.startsWith(path))) {
    return null;
  }

  return <Navbar />;
};

// Component to conditionally render Footer
const ConditionalFooter = () => {
  const location = useLocation();
  const hideFooterPaths = [
    "/signin",
    "/signup",
    "/forgot-password",
    "/reset-password",
    "/profile", // Hide footer on profile page (includes UserSolutions)
  ];

  if (hideFooterPaths.some((path) => location.pathname.startsWith(path))) {
    return null;
  }

  return <Footer />;
};

const App = () => {
  const [challengeData, setChallengeData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // console.log("api url is ", import.meta.env.VITE_DAILY_API);

  useEffect(() => {
    const fetchDailyChallenge = async () => {
      try {
        const response = await fetch(
          `${import.meta.env.VITE_DAILY_API}/leetcode/daily`,
          {
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
            },
          }
        );
        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error:", errorText);
          throw new Error(
            `Failed to fetch daily challenge: ${response.status}`
          );
        }
        const contentType = response.headers.get("content-type");
        if (!contentType || !contentType.includes("application/json")) {
          throw new Error("Invalid response format: Expected JSON");
        }
        const data = await response.json();
        setChallengeData(data);

        // Automatically save the daily question to database
        try {
          const titleSlug =
            data.questionLink?.split("/problems/")[1]?.split("/")[0] || "";

          const questionData = {
            questionTitle: data.questionTitle,
            problemSlug: titleSlug,
            difficulty: data.difficulty,
            topicTags: data.topicTags || [],
            questionLink: data.questionLink,
          };

          const saveResponse = await fetch(
            `${import.meta.env.VITE_API_URL}/api/v1/leetcode/handle-question`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
              body: JSON.stringify(questionData),
            }
          );

          if (saveResponse.ok) {
            const saveResult = await saveResponse.json();
            console.log(
              "Daily question saved to database:",
              saveResult.message
            );
          } else {
            console.warn(
              "Failed to save daily question to database:",
              saveResponse.status
            );
          }
        } catch (saveError) {
          console.error("Error saving daily question to database:", saveError);
          // Don't throw here - this shouldn't break the main functionality
        }
      } catch (err) {
        console.error("Fetch error:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchDailyChallenge();
  }, []);

  // Handle hash-based navigation (e.g., /#community)
  useEffect(() => {
    const handleHashNavigation = () => {
      const hash = window.location.hash.substring(1); // Remove the '#'
      if (hash === "community") {
        setTimeout(() => {
          const communitySection = document.getElementById("community");
          if (communitySection) {
            communitySection.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        }, 100); // Small delay to ensure the page is fully loaded
      }
    };

    // Check hash on initial load
    handleHashNavigation();

    // Listen for hash changes
    window.addEventListener("hashchange", handleHashNavigation);

    return () => {
      window.removeEventListener("hashchange", handleHashNavigation);
    };
  }, []);

  {
    /* Carousel state management */
  }
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % carouselItems.length);
  };

  const handlePrev = () => {
    setCurrentIndex((prev) =>
      prev === 0 ? carouselItems.length - 1 : prev - 1
    );
  };

  useEffect(() => {
    const interval = setInterval(() => {
      handleNext();
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gradient-to-r from-[#54206A] to-[#241641] overflow-x-hidden">
          <ConditionalNavbar />
          <ScrollToTop />
          <Routes>
            <Route
              path="/"
              element={
                <>
                  <GlobalLeaderboardBanner />
                  <main className="w-full px-4 xs:px-6 sm:px-10 md:px-20 lg:px-27 pt-0 pb-4 flex flex-col items-center gap-8 sm:gap-12 md:gap-16">
                    <div className="flex flex-col lg:flex-row items-center justify-between gap-4 lg:gap-4 w-full max-w-6xl px-2 xs:px-4">
                      <div className="max-w-5xl text-start lg:text-left">
                        <h1 className="leading-tight text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-2 sm:mb-4">
                          "Your Daily Dose of DSA
                          <br />
                          One Problem at a Time."
                        </h1>
                        <p className="bg-gradient-to-r from-purple-300 to-pink-300 text-transparent bg-clip-text mb-4 xs:mb-6 sm:mb-8 text-xs xs:text-sm sm:text-lg md:text-xl font-medium tracking-wide">
                          Stay consistent, sharpen your skills daily, and turn
                          problem-solving into a habit.
                        </p>
                        <button className="w-150 text-start text-white rounded-lg text-2xl font-bold xs:text-base">
                          Consistency + Practice ={" "}
                          <span className="text-white font-semibold">
                            <Typewriter
                              words={[
                                "Focus",
                                "Discipline",
                                "Patience",
                                "Effort",
                                "Mastery",
                              ]}
                              loop={0} // 0 = infinite loop
                              cursor
                              cursorStyle="|"
                              typeSpeed={80}
                              deleteSpeed={50}
                              delaySpeed={1000}
                            />
                          </span>
                        </button>
                      </div>
                      <div className="relative w-full max-w-[300px] xs:max-w-[350px] sm:max-w-[400px] md:max-w-[500px] lg:w-[600px] aspect-square flex items-center justify-center lg:justify-end mt-4 sm:mt-0">
                        <img
                          src="/image.png"
                          alt="Clock Icon"
                          className="w-4/5 h-4/5 object-contain z-10 relative opacity-95 filter brightness-150"
                        />
                        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/80 via-purple-500/60 to-purple-400/30 rounded-full blur-[100px]"></div>
                        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/40 to-purple-400/20 rounded-full blur-[80px] animate-pulse"></div>
                        <div className="absolute inset-0 bg-gradient-to-tr from-purple-600/30 to-transparent rounded-full blur-[120px]"></div>
                      </div>
                    </div>

                    <div className="w-full max-w-4xl mx-auto px-4">
                      <h2 className="text-2xl xs:text-3xl sm:text-4xl font-bold text-center mb-4 xs:mb-6 sm:mb-8 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">
                        TODAY&apos;S DAILY CHALLENGE
                      </h2>
                      {loading ? (
                        <div className="flex flex-col items-center justify-center py-16">
                          {/* Main Loading Container */}
                          <div className="relative w-[60%] max-w-sm mx-auto">
                            {/* Glowing Background Effect */}
                            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-500/20 to-purple-600/20 rounded-2xl blur-xl animate-loading-pulse"></div>

                            {/* Loading Card */}
                            <div className="relative bg-gray-900/60 backdrop-blur-sm rounded-2xl p-8 border border-purple-500/20">
                              {/* Animated Progress Bar */}
                              <div className="w-full mb-8">
                                <div className="relative h-3 bg-gray-800/80 rounded-full overflow-hidden">
                                  {/* Base gradient bar */}
                                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-pink-500 to-purple-600 rounded-full animate-gradient"></div>
                                  {/* Shimmer overlay */}
                                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent rounded-full animate-shimmer"></div>
                                </div>
                              </div>

                              {/* Loading Text */}
                              <div className="text-center mb-6">
                                <h3 className="text-2xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 text-transparent bg-clip-text mb-3 animate-gradient">
                                  Loading Today's Challenge
                                </h3>
                                <p className="text-gray-300 text-sm animate-pulse">
                                  Fetching the latest problem from LeetCode...
                                </p>
                              </div>

                              {/* Animated Dots */}
                              <div className="flex justify-center space-x-3">
                                <div
                                  className="w-3 h-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full animate-bounce shadow-lg shadow-purple-500/50"
                                  style={{ animationDelay: "0ms" }}
                                ></div>
                                <div
                                  className="w-3 h-3 bg-gradient-to-r from-pink-500 to-pink-600 rounded-full animate-bounce shadow-lg shadow-pink-500/50"
                                  style={{ animationDelay: "200ms" }}
                                ></div>
                                <div
                                  className="w-3 h-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full animate-bounce shadow-lg shadow-purple-500/50"
                                  style={{ animationDelay: "400ms" }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : error ? (
                        <div className="text-red-500 text-center">{error}</div>
                      ) : challengeData ? (
                        <DailyChallengeCard challengeData={challengeData} />
                      ) : null}
                    </div>

                    <SolveAndLearn />

                    <div className="w-full max-w-6xl mx-auto px-4 ">
                      <h2 className="text-2xl xs:text-3xl sm:text-4xl font-bold text-center mb-8 xs:mb-12 sm:mb-16 md:mb-20 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">
                        What is BIG (O)?
                      </h2>

                      <div className="flex flex-col md:flex-row items-center justify-between gap-6 md:gap-12 overflow-hidden">
                        {/* Gradient Box on the Left */}
                        <div className="w-full md:w-2/5 h-60 xs:h-64 sm:h-75 rounded-xl bg-gradient-to-br from-purple-600 via-pink-200 to-indigo-700 shadow-xl animate-pulse">
                          <img
                            src={BigoImage}
                            alt="BIG O"
                            className="w-full h-full object-cover rounded-xl"
                          />
                        </div>

                        {/* Main Description */}
                        <div className="w-full md:w-2/5 mt-6 md:mt-0">
                          <p className="text-gray-300 text-sm xs:text-base sm:text-lg md:text-xl font-semibold leading-relaxed text-center md:text-justify mb-4 sm:mb-6">
                            BIG O is a vibrant community of passionate problem
                            solvers who come together to tackle challenging
                            coding problems from LeetCode and other platforms.
                            Here, developers of all skill levels collaborate
                            daily, sharing their solutions, discussing different
                            approaches, and learning from each other. Join us in
                            this journey of continuous improvement and
                            algorithmic mastery!
                          </p>
                        </div>
                      </div>

                      {/* Carousel Section */}
                      <div className="relative flex justify-center w-full max-w-4xl mx-auto mt-8 xs:mt-10 sm:mt-12 px-2 xs:px-4">
                        {/* Card Container */}
                        <div className="overflow-hidden w-full max-w-2xl rounded-xl p-4 sm:p-6 md:p-8 text-white text-center">
                          <div className="flex flex-col items-center space-y-4 transition-all duration-500 ease-in-out">
                            <div>{carouselItems[currentIndex].icon}</div>
                            <p className="text-base xs:text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold px-2">
                              {carouselItems[currentIndex].text}
                            </p>
                          </div>
                        </div>

                        {/* Navigation Arrows */}
                        {/*  <button
                          onClick={handlePrev}
                          className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white text-purple-700 hover:bg-purple-100 p-2 rounded-full shadow-lg"
                        >
                          <FaChevronLeft />
                        </button>
                        <button
                          onClick={handleNext}
                          className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white text-purple-700 hover:bg-purple-100 p-2 rounded-full shadow-lg"
                        >
                          <FaChevronRight />
                        </button>*/}
                      </div>
                    </div>

                    <div
                      id="dsa-roadmap"
                      className="w-full max-w-7xl mx-auto overflow-hidden px-4 py-8 sm:py-12"
                    >
                      <h2 className="text-3xl sm:text-4xl font-bold text-center mb-8 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">
                        DSA Learning Path
                      </h2>
                      <div className="relative rounded-2xl overflow-hidden mb-4 ">
                        <RoadmapScroll />
                      </div>

                      <div className="mt-16 sm:mt-24">
                        <Community />
                      </div>

                      <Reviews />

                      <Faqs />
                    </div>
                  </main>
                </>
              }
            />

            {/* Authentication Pages */}
            <Route path="/signin" element={<SignIn />} />
            <Route path="/signup" element={<SignUp />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password/:token" element={<ResetPassword />} />
            <Route
              path="/profile"
              element={
                <ProtectedRoute>
                  <Profile />
                </ProtectedRoute>
              }
            />

            {/* Sheets Pages */}
            <Route path="/sheets" element={<Sheets />} />
            <Route path="/sheets/oncampus" element={<OnCampus />} />
            <Route path="/sheets/offcampus" element={<OffCampus />} />

            {/* DSA Topic Pages */}
            <Route path="/topics/:topicId" element={<DSATopicPage />} />
          </Routes>

          <ConditionalFooter />
        </div>
      </Router>
    </AuthProvider>
  );
};

export default App;
