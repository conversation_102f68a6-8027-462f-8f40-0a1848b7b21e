/**
 * Sheet controller
 * Handles HTTP requests for sheet-related endpoints
 */
import async<PERSON>and<PERSON> from "express-async-handler";
import { StatusCodes } from "http-status-codes";
import SheetQuestion from "../models/sheetQuestionModal.js";
import User from "../models/user.model.js";
import jwt from 'jsonwebtoken';

const solveProblem = asyncHandler(async (req, res) => {
  try {
    const { userId, title, questionLink, difficulty, tags } = req.body;
    
    console.log('solveProblem called - req.user:', req.user);
    console.log('solveProblem called - userId from body:', userId);
    console.log('solveProblem called - cookies:', req.cookies);
    
    // Check authentication - middleware should have set req.user
    let finalUserId, user;
    
    if (req.user && req.user.id) {
      // Authenticated user from middleware
      finalUserId = req.user.id;
      user = await User.findById(finalUserId);
      console.log('Using authenticated user from middleware:', finalUserId);
    } else if (userId) {
      // Fallback to userId from request body (for backward compatibility)
      finalUserId = userId;
      user = await User.findById(finalUserId);
      console.log('Using userId from request body as fallback:', finalUserId);
    } else {
      console.log('No authentication found - req.user:', req.user, 'userId:', userId);
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: "Please login to use this feature",
      });
    }
    
    if (!user) {
      console.log('User not found in database for ID:', finalUserId);
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: "User not found",
      });
    }

    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: "User not found",
      });
    }

    // Validate required fields
    if (!title || !questionLink || !difficulty) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Title, questionLink, and difficulty are required",
      });
    }

    // Normalize difficulty to match schema enum values
    const difficultyLower = difficulty.toLowerCase();
    if (!['easy', 'medium', 'hard'].includes(difficultyLower)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Difficulty must be 'easy', 'medium', or 'hard'",
      });
    }
    
    // Convert to proper case for schema enum
    const normalizedDifficulty = difficultyLower === 'easy' ? 'Easy' :
                                 difficultyLower === 'medium' ? 'Medium' :
                                 'Hard';

    // Normalize and filter tags to match schema enum values
    const allowedTags = [
      'array', 'string', 'linked list', 'tree', 'graph', 'dynamic programming', 
      'sorting', 'searching', 'hashing', 'greedy', 'backtracking', 'math',
      'bit manipulation', 'recursion', 'binary search', 'two pointers',
      'sliding window', 'stack', 'queue', 'heap', 'trie', 'union find',
      'binary tree', 'binary search tree', 'depth-first search', 
      'breadth-first search', 'design', 'simulation', 'geometry',
      'brainteaser', 'interactive', 'database', 'shell', 'concurrency'
    ];

    let normalizedTags = [];
    if (tags && Array.isArray(tags) && tags.length > 0) {
      normalizedTags = tags
        .map(tag => {
          if (typeof tag === 'string') {
            const lowerTag = tag.toLowerCase();
            // Map common variations to schema values
            const tagMapping = {
              'arrays': 'array',
              'strings': 'string',
              'dp': 'dynamic programming',
              'dfs': 'depth-first search',
              'bfs': 'breadth-first search',
              'hash table': 'hashing',
              'hash map': 'hashing',
              'binary trees': 'binary tree',
              'linked lists': 'linked list'
            };
            
            const mappedTag = tagMapping[lowerTag] || lowerTag;
            return allowedTags.includes(mappedTag) ? mappedTag : null;
          }
          return null;
        })
        .filter(tag => tag !== null);
    }

    // If no valid tags, use basic tags based on difficulty
    if (normalizedTags.length === 0) {
      normalizedTags = difficultyLower === 'easy' ? ['array'] :
                      difficultyLower === 'medium' ? ['array', 'string'] :
                      ['array', 'dynamic programming'];
    }

    console.log('Normalized tags:', normalizedTags);

    // Find existing question
    let question = await SheetQuestion.findOne({ title });

    // If question doesn't exist, create it and add user
    if (!question) {
      question = await SheetQuestion.create({
        title,
        questionLink,
        difficulty: normalizedDifficulty,
        tags: normalizedTags,
        userList: [finalUserId],
      });

      console.log(`Created new sheet question: ${title} and marked as solved by user: ${user.fullName}`);

      return res.status(StatusCodes.CREATED).json({
        success: true,
        message: "Question marked as solved",
        solved: true,
        question: {
          id: question._id,
          title: question.title,
          difficulty: question.difficulty,
          userCount: 1
        }
      });
    }

    // If question exists, check if user already marked it as solved
    const userIndex = question.userList.findIndex(id => id.toString() === finalUserId.toString());

    if (userIndex > -1) {
      // Already marked as solved → unmark it
      question.userList.splice(userIndex, 1);
      await question.save();

      console.log(`User ${user.fullName} unmarked question: ${title}`);

      return res.status(StatusCodes.OK).json({
        success: true,
        message: "Question unmarked",
        solved: false,
        question: {
          id: question._id,
          title: question.title,
          difficulty: question.difficulty,
          userCount: question.userList.length
        }
      });
    } else {
      // Not marked yet → mark it as solved
      question.userList.push(finalUserId);
      await question.save();

      console.log(`User ${user.fullName} marked question as solved: ${title}`);

      return res.status(StatusCodes.OK).json({
        success: true,
        message: "Question marked as solved",
        solved: true,
        question: {
          id: question._id,
          title: question.title,
          difficulty: question.difficulty,
          userCount: question.userList.length
        }
      });
    }
  } catch (error) {
    console.error('Error in solveProblem:', error);
    
    // Handle specific validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Validation failed",
        errors: validationErrors,
        details: "Some of the provided data doesn't match the required format"
      });
    }
    
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Internal Server Error",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get user's sheet question status for a specific question
const getQuestionStatus = asyncHandler(async (req, res) => {
  try {
    const { title } = req.params;
    
    // Get user from auth middleware
    if (!req.user || !req.user.id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: "Please login to check question status",
      });
    }

    const finalUserId = req.user.id;
    const question = await SheetQuestion.findOne({ title });

    if (!question) {
      return res.status(StatusCodes.OK).json({
        success: true,
        solved: false,
        message: "Question not found in sheet database"
      });
    }

    const isSolved = question.userList.some(id => id.toString() === finalUserId.toString());

    return res.status(StatusCodes.OK).json({
      success: true,
      solved: isSolved,
      question: {
        id: question._id,
        title: question.title,
        difficulty: question.difficulty,
        userCount: question.userList.length
      }
    });
  } catch (error) {
    console.error('Error in getQuestionStatus:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Internal Server Error",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get all sheet questions solved by user
const getUserSheetQuestions = asyncHandler(async (req, res) => {
  try {
    // Get user from auth middleware
    if (!req.user || !req.user.id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: "Please login to view your sheet questions",
      });
    }

    const finalUserId = req.user.id;
    const questions = await SheetQuestion.find({ userList: finalUserId });

    return res.status(StatusCodes.OK).json({
      success: true,
      questions: questions.map(q => ({
        id: q._id,
        title: q.title,
        questionLink: q.questionLink,
        difficulty: q.difficulty,
        tags: q.tags,
        userCount: q.userList.length,
        createdAt: q.createdAt
      })),
      totalSolved: questions.length
    });
  } catch (error) {
    console.error('Error in getUserSheetQuestions:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Internal Server Error",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get all sheet questions organized by topic (for OnCampus sheet)
const getSheetQuestionsByTopic = asyncHandler(async (req, res) => {
  try {
    // Get user from auth middleware (optional for this endpoint)
    let finalUserId = null;
    if (req.user && req.user.id) {
      finalUserId = req.user.id;
    }

    // Fetch all sheet questions from database
    const questions = await SheetQuestion.find({}).sort({ createdAt: 1 });

    // Group questions by topic
    const questionsByTopic = {};
    
    questions.forEach(question => {
      const topic = question.topic || 'Other';
      if (!questionsByTopic[topic]) {
        questionsByTopic[topic] = [];
      }
      
      // Create a problem object matching the frontend format
      const problem = {
        id: question._id.toString(),
        title: question.title,
        difficulty: question.difficulty,
        link: question.questionLink,
        solutionLink: question.solutionLink || null, // Include YouTube solution link
        tags: question.tags || [],
        completed: finalUserId ? question.userList.includes(finalUserId) : false,
        userCount: question.userList.length,
        createdAt: question.createdAt
      };
      
      questionsByTopic[topic].push(problem);
    });

    // Convert to the format expected by frontend
    const topics = Object.keys(questionsByTopic).map(topicName => ({
      id: topicName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
      title: topicName,
      totalProblems: questionsByTopic[topicName].length,
      completedProblems: finalUserId ? 
        questionsByTopic[topicName].filter(p => p.completed).length : 0,
      problems: questionsByTopic[topicName]
    }));

    // Calculate overall stats
    const totalProblems = questions.length;
    const completedProblems = finalUserId ? 
      questions.filter(q => q.userList.includes(finalUserId)).length : 0;

    const sheetData = {
      title: "On Campus - DSA Preparation",
      description: "Essential problems for campus placement interviews. These questions are curated from our database and organized by topic.",
      totalProblems,
      completedProblems,
      topics
    };

    return res.status(StatusCodes.OK).json({
      success: true,
      data: sheetData,
      message: "Sheet questions fetched successfully"
    });
  } catch (error) {
    console.error('Error in getSheetQuestionsByTopic:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Internal Server Error",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get all solutions for a specific sheet problem (for leaderboard)
const getProblemSolutions = asyncHandler(async (req, res) => {
  try {
    const { problemTitle } = req.params;
    
    if (!problemTitle) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Problem title is required"
      });
    }

    // First, find the question in SheetQuestion collection to get its _id
    const question = await SheetQuestion.findOne({ title: problemTitle });
    
    if (!question) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: "Question not found in sheet database"
      });
    }

    console.log(`Found sheet question: ${question.title} with ID: ${question._id}`);

    // Import Question model to search in questions collection
    const Question = (await import("../models/question.model.js")).default;
    
    // Search for the question in questions collection by title
    const questionInQuestionsCollection = await Question.findOne({ 
      $or: [
        { title: problemTitle },
        { title: { $regex: new RegExp(`^${problemTitle}$`, 'i') } }
      ]
    });

    // Import Solution model
    const Solution = (await import("../models/solution.model.js")).default;
    
    let solutions = [];

    if (questionInQuestionsCollection) {
      console.log(`Found question in questions collection with ID: ${questionInQuestionsCollection._id}`);
      
      // Find solutions using the question field (ObjectId) for exact matching
      solutions = await Solution.find({ 
        question: questionInQuestionsCollection._id
      })
      .populate('user', 'fullName username email')
      .sort({ createdAt: 1 }) // Sort by submission time (first submitted first)
      .lean();
    }

    console.log(`Found ${solutions.length} solutions for problem: ${problemTitle}`);
    
    // If no solutions found in Solution collection, check users who marked this question as solved
    // in the SheetQuestion collection and create mock solution entries
    if (solutions.length === 0) {
      console.log(`No solutions found in Solution collection. Checking users who marked question as solved...`);
      
      // Get users who marked this question as solved
      const questionWithUsers = await SheetQuestion.findById(question._id).populate('userList', 'fullName username email');
      
      if (questionWithUsers && questionWithUsers.userList.length > 0) {
        // Create mock solution entries for users who marked it as solved
        const mockSolutions = questionWithUsers.userList.map((user, index) => ({
          _id: `mock_${question._id}_${user._id}`,
          userId: user,
          problemTitle: question.title,
          language: 'Not Available',
          code: 'Solution not submitted through the platform.\nThis user marked the problem as solved.',
          approach: 'Approach not available.\nThis solution was marked as completed manually.',
          createdAt: questionWithUsers.createdAt || new Date(),
          isSheetQuestion: true
        }));
        
        console.log(`Created ${mockSolutions.length} mock solutions from users who marked question as solved`);
        
        return res.status(StatusCodes.OK).json({
          success: true,
          solutions: mockSolutions,
          count: mockSolutions.length,
          message: "Solutions fetched successfully (from users who marked as solved)",
          note: "Some solutions may not have code/approach as they were marked as solved without submitting through the platform"
        });
      }
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      solutions,
      count: solutions.length,
      message: "Problem solutions fetched successfully"
    });
  } catch (error) {
    console.error('Error in getProblemSolutions:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Internal Server Error",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export {
  solveProblem,
  getQuestionStatus,
  getUserSheetQuestions,
  getSheetQuestionsByTopic,
  getProblemSolutions
};
