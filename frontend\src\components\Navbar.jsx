import { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Flame } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import UserAvatar from './UserAvatar';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, logout, isAuthenticated } = useAuth();
  const location = useLocation();
  const menuRef = useRef(null);
  
  // State to hold user's current streak  
  const [userStreak, setUserStreak] = useState(0);
  
  // Use streak data directly from user object
  const streakCount = user?.streak || 0;

  // Update streak when user data changes
  useEffect(() => {
    if (user && user.streak !== undefined) {
      setUserStreak(user.streak);
    } else {
      setUserStreak(0);
    }
  }, [user]);

  // <PERSON>le click outside to close menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsMenuOpen(false);
      }
    };

    // Add event listener when menu is open
    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Cleanup event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  const handleLogout = async () => {
    await logout();
    setIsMenuOpen(false);
  };

  const scrollToCommunity = () => {
    // If we're not on the home page, navigate to home first
    if (location.pathname !== '/') {
      window.location.href = '/#community';
      return;
    }

    // If we're on the home page, scroll to the community section
    const communitySection = document.getElementById('community');
    if (communitySection) {
      communitySection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
    setIsMenuOpen(false);
  };

  const scrollToSolveAndLearn = () => {
    // If we're not on the home page, navigate to home first
    if (location.pathname !== '/') {
      window.location.href = '/#solve-and-learn';
      return;
    }

    // If we're on the home page, scroll to the solve and learn section
    const solveAndLearnSection = document.getElementById('solve-and-learn');
    if (solveAndLearnSection) {
      solveAndLearnSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
    setIsMenuOpen(false);
  };

  const scrollToRoadmap = () => {
    // If we're not on the home page, navigate to home first
    if (location.pathname !== '/') {
      window.location.href = '/#dsa-roadmap';
      return;
    }

    // If we're on the home page, scroll to the DSA roadmap section
    const roadmapSection = document.getElementById('dsa-roadmap');
    if (roadmapSection) {
      roadmapSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
    setIsMenuOpen(false);
  };

  const scrollToFaq = () => {
    // If we're not on the home page, navigate to home first
    if (location.pathname !== '/') {
      window.location.href = '/#faq';
      return;
    }

    // If we're on the home page, scroll to the FAQ section
    const faqSection = document.getElementById('faq');
    if (faqSection) {
      faqSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
    setIsMenuOpen(false);
  };

  return (
    <nav className="relative px-3 xs:px-4 sm:px-6 md:px-10 py-3 xs:py-4 z-50">
      <div className="absolute inset-x-2 xs:inset-x-3 sm:inset-x-4 md:inset-x-8 top-1/2 transform -translate-y-1/2 h-14 xs:h-16 bg-purple-950/90 rounded-full backdrop-blur-sm"></div>
      
      {/* Desktop Navigation */}
      <div className="hidden lg:flex items-center justify-between relative z-10 max-w-7xl mx-auto h-14">
        {/* Left Navigation - Fixed Width Container */}
        <div className="flex items-center gap-6 flex-1">
          <Link to="/" className="text-white hover:text-purple-400 px-5 py-2.5 rounded-full transition-all duration-200 hover:bg-purple-800/50 cursor-pointer flex items-center">Home</Link>
          <button
            onClick={scrollToSolveAndLearn}
            className="text-white hover:text-purple-400 px-5 py-2.5 rounded-full transition-all duration-200 hover:bg-purple-800/50 cursor-pointer flex items-center"
          >
            Solve and Learn
          </button>
          <button
            onClick={scrollToRoadmap}
            className="text-white hover:text-purple-400 px-5 py-2.5 rounded-full transition-all duration-200 hover:bg-purple-800/50 cursor-pointer flex items-center"
          >
            Road Map
          </button>
          <Link to="/sheets" className="text-white hover:text-purple-400 px-5 py-2.5 rounded-full transition-all duration-200 hover:bg-purple-800/50 cursor-pointer flex items-center">Sheets</Link>
        </div>

        {/* Centered Logo - Absolutely positioned to stay centered */}
        <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center">
          <Link to="/" className="flex items-center justify-center">
            <div className="absolute w-16 h-16 bg-white rounded-full"></div>
            <img src="/logo.svg" alt="Logo" className="h-14 w-14 relative z-10" />
          </Link>
        </div>

        {/* Right Navigation - Fixed Width Container */}
        <div className="flex items-center gap-6 flex-1 justify-end">
          <button
            onClick={scrollToCommunity}
            className="text-white hover:text-purple-400 px-5 py-2.5 rounded-full transition-all duration-200 hover:bg-purple-800/50 cursor-pointer flex items-center"
          >
            Community
          </button>
          <button
            onClick={scrollToFaq}
            className="text-white hover:text-purple-400 px-5 py-2.5 rounded-full transition-all duration-200 hover:bg-purple-800/50 cursor-pointer flex items-center"
          >
            FAQ
          </button>

          {/* Streak Display - always show, default 0 for non-authenticated users */}
          <div className="flex items-center gap-2 bg-orange-500/20 backdrop-blur-sm px-4 py-2 rounded-full border border-orange-500/30">
            <Flame size={20} className="text-orange-400" />
            <span className="text-orange-200 font-semibold text-sm">
              {isAuthenticated() ? streakCount : 0}
            </span>
          </div>

          {isAuthenticated() ? (
            <div className="cursor-pointer">
              <UserAvatar user={user} onLogout={handleLogout} />
            </div>
          ) : (
            <div className="ml-4">
              <Link
                to="/signin"
                className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2.5 rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 cursor-pointer flex items-center justify-center"
              >
                Login
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden flex items-center justify-between relative z-10 max-w-7xl mx-auto h-12 xs:h-14">
        {/* Left side - Logo (centered in mobile) */}
        <div className="flex-1 flex justify-start items-center">
          <Link to="/" className="flex items-center">
            <div className="absolute w-10 h-10 xs:w-12 xs:h-12 sm:w-14 sm:h-14 bg-white rounded-full"></div>
            <img src="/logo.svg" alt="Logo" className="h-8 w-8 xs:h-10 xs:w-10 sm:h-12 sm:w-12 relative z-10" />
          </Link>
        </div>

        {/* Right side - User info and menu */}
        <div className="flex items-center gap-1 xs:gap-2 sm:gap-3">
          {/* Streak Display for Mobile - always show, default 0 for non-authenticated users */}
          <div className="flex items-center gap-1 bg-orange-500/20 backdrop-blur-sm px-2 xs:px-3 py-1 xs:py-1.5 rounded-full border border-orange-500/30">
            <Flame size={14} className="text-orange-400 xs:hidden" />
            <Flame size={16} className="text-orange-400 hidden xs:block" />
            <span className="text-orange-200 font-semibold text-[10px] xs:text-xs">
              {isAuthenticated() ? streakCount : 0}
            </span>
          </div>

          {isAuthenticated() ? (
            <div className="cursor-pointer flex items-center">
              <UserAvatar user={user} onLogout={handleLogout} />
            </div>
          ) : (
            <div className="ml-1 xs:ml-2 flex items-center">
              <Link
                to="/signin"
                className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-2 xs:px-3 sm:px-4 py-1.5 xs:py-2 rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-200 font-medium text-[10px] xs:text-xs sm:text-sm cursor-pointer flex items-center justify-center"
              >
                Login
              </Link>
            </div>
          )}

          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="text-white p-1 xs:p-1.5 sm:p-2 focus:outline-none focus:ring-2 focus:ring-purple-400 rounded-lg cursor-pointer flex items-center justify-center"
          >
            <svg
              className="w-5 h-5 xs:w-5 xs:h-5 sm:w-6 sm:h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              {isMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div 
          ref={menuRef}
          className="lg:hidden absolute top-16 xs:top-18 sm:top-20 left-0 right-0 bg-purple-900/95 backdrop-blur-lg rounded-2xl mx-2 xs:mx-3 sm:mx-4 p-3 xs:p-4 shadow-xl z-50 transform transition-all duration-200 ease-in-out"
        >
          <div className="flex flex-col space-y-2 xs:space-y-3">
            <Link 
              to="/" 
              onClick={() => setIsMenuOpen(false)}
              className="text-white hover:text-purple-400 px-3 xs:px-4 py-1.5 xs:py-2 rounded-lg transition-all duration-200 hover:bg-purple-800/50 cursor-pointer text-sm xs:text-base flex items-center"
            >
              Home
            </Link>
            <button
              onClick={scrollToSolveAndLearn}
              className="text-white hover:text-purple-400 px-3 xs:px-4 py-1.5 xs:py-2 rounded-lg transition-all duration-200 hover:bg-purple-800/50 text-left cursor-pointer text-sm xs:text-base flex items-center"
            >
              Solve and Learn
            </button>
            <button
              onClick={scrollToRoadmap}
              className="text-white hover:text-purple-400 px-3 xs:px-4 py-1.5 xs:py-2 rounded-lg transition-all duration-200 hover:bg-purple-800/50 text-left cursor-pointer text-sm xs:text-base flex items-center"
            >
              Road Map
            </button>
            <Link 
              to="/sheets" 
              onClick={() => setIsMenuOpen(false)}
              className="text-white hover:text-purple-400 px-3 xs:px-4 py-1.5 xs:py-2 rounded-lg transition-all duration-200 hover:bg-purple-800/50 cursor-pointer text-sm xs:text-base flex items-center"
            >
              Sheets
            </Link>
            <button
              onClick={scrollToCommunity}
              className="text-white hover:text-purple-400 px-3 xs:px-4 py-1.5 xs:py-2 rounded-lg transition-all duration-200 hover:bg-purple-800/50 text-left cursor-pointer text-sm xs:text-base flex items-center"
            >
              Community
            </button>
            <button
              onClick={scrollToFaq}
              className="text-white hover:text-purple-400 px-3 xs:px-4 py-1.5 xs:py-2 rounded-lg transition-all duration-200 hover:bg-purple-800/50 text-left cursor-pointer text-sm xs:text-base flex items-center"
            >
              FAQ
            </button>

          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;