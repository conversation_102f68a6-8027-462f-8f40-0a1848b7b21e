import React, { useState, useRef, useEffect } from "react";
import { ChevronDown } from "lucide-react";

const Faqs = () => {
  const faqs = [
    {
      question: "What is special about Big(O)?",
      answer:
        "It's a community dedicated to trace the record of daily problem solvers and rank them on the basis of their consistency along with simplified solutions",
    },
    {
      question: "Is it helpful for placement Preparation?",
      answer:
        "Yes it is helpful, we provide the resource sheet for on-campus placement preparation and help to solve the doubts of DSA through whatsapp community"
    },
    {
      question: "Will it improve my coding?",
      answer:
        "Yes definitely, it will improve only when you are consistent towards solving the problem :)",
    },
    {
      question: "How will I get to know my placement strategy ? ",
      answer:
        "Ping us on our respective mail, we will provide you the resource sheet for placement preparation and help you to solve the doubts of DSA through whatsapp community",
    }
  ];

  const [openIndex, setOpenIndex] = useState(null);

  const toggleFaq = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="faq" className="w-full py-10 md:py-20 px-4 sm:px-6 md:px-10 lg:px-20">
      <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-center mb-6 md:mb-10 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text animate-pulse">
        Frequently Asked Question's
      </h2>
      <div className="max-w-5xl mx-auto space-y-2 px-2 sm:px-4">
        {faqs.map((faq, index) => {
          const isOpen = openIndex === index;
          return (
            <div
              key={index}
              className="relative p-[2px] rounded-lg cursor-pointer transition-all duration-300 hover:scale-[1.01] sm:hover:scale-[1.02]"
              onClick={() => toggleFaq(index)}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg"></div>

              <div className="relative z-10 bg-gray-900 p-3 sm:p-4 md:p-5 rounded-lg shadow-lg">
                <div className="flex justify-between items-center">
                  <span className="text-white font-bold text-base sm:text-lg pr-2">
                    {faq.question}
                  </span>
                  <ChevronDown
                    className={`flex-shrink-0 transform transition-transform duration-300 text-white ${
                      isOpen ? "rotate-180" : ""
                    }`}
                    size={18}
                  />
                </div>

                {/* Animated Answer Section */}
                <div
                  className={`transition-all duration-400 overflow-hidden`}
                  style={{
                    maxHeight: isOpen ? "300px" : "0px",
                    opacity: isOpen ? 1 : 0,
                    marginTop: isOpen ? "0.5rem" : "0rem"
                  }}
                >
                  <p className="text-gray-300 text-sm sm:text-base">{faq.answer}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </section>
  );
};

export default Faqs;
