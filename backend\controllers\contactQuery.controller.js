// controllers/contactQuery.controller.js
import nodemailer from "nodemailer";

export const sendQuery = async (req, res) => {
  const { name, email, query } = req.body;

  // Validate required fields
  if (!name || !email || !query) {
    return res.status(400).json({
      success: false,
      error: 'All fields (name, email, query) are required'
    });
  }

  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({
      success: false,
      error: 'Please provide a valid email address'
    });
  }

  try {
    // Create transporter with Gmail SMTP
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_EMAIL,
        pass: process.env.SMTP_PASSWORD
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Verify transporter configuration
    await transporter.verify();

    // Email content for the admin
    const adminMailOptions = {
      from: `"Big-O Contact Form" <${process.env.SMTP_EMAIL}>`,
      to: process.env.SMTP_EMAIL, // Send to the same email for now
      subject: `New Contact Query from ${name}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
          <h2 style="color: #8B5CF6; text-align: center;">New Contact Query - Big-O Website</h2>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">Contact Details:</h3>
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
          </div>
          <div style="background-color: #fff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
            <h3 style="color: #333; margin-top: 0;">Query:</h3>
            <p style="line-height: 1.6; color: #555;">${query}</p>
          </div>
          <div style="text-align: center; margin-top: 20px; padding: 15px; background-color: #f1f3f4; border-radius: 8px;">
            <p style="margin: 0; color: #666; font-size: 14px;">
              This email was sent from the Big-O website contact form.
            </p>
          </div>
        </div>
      `,
      text: `
        New Contact Query - Big-O Website

        Name: ${name}
        Email: ${email}
        Submitted: ${new Date().toLocaleString()}

        Query:
        ${query}

        This email was sent from the Big-O website contact form.
      `
    };

    // Send email
    await transporter.sendMail(adminMailOptions);

    res.status(200).json({
      success: true,
      message: 'Query sent successfully! We will get back to you soon.'
    });

  } catch (error) {
    console.error('SMTP Error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send message. Please try again later.'
    });
  }
};