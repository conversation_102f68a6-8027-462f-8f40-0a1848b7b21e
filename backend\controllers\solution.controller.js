/**
 * Solution controller
 * Handles HTTP requests for solution-related endpoints
 */
import asyncHandler from 'express-async-handler';
import { StatusCodes } from 'http-status-codes';
import Solution from '../models/solution.model.js';
import User from '../models/user.model.js';
import Question from '../models/question.model.js';


/**
 * Get all solutions for a specific problem (leaderboard)
 * @route GET /api/v1/solutions/problem/:problemId
 * @access Public
 */
const getSolutionsByProblem = asyncHandler(async (req, res) => {
  const { problemId } = req.params;
  const { limit = 50, page = 1 } = req.query;

  // Try to find the question by title first, then by other possible identifiers
  let question = await Question.findOne({ title: problemId });

  // If not found by title, try to find by questionLink containing the problemId
  if (!question) {
    question = await Question.findOne({
      questionLink: { $regex: problemId, $options: 'i' }
    });
  }

  // If still not found, try to find by title containing the problemId
  if (!question) {
    question = await Question.findOne({
      title: { $regex: problemId, $options: 'i' }
    });
  }

  if (!question) {
    return res.status(StatusCodes.OK).json({
      success: true,
      count: 0,
      solutions: []
    });
  }

  // Get all solutions for this question, sorted by submission time (first to solve gets rank 1)
  const solutions = await Solution.find({
    question: question._id
  })
  .populate('user', 'fullName username')
  .populate('question', 'title difficulty')
  .sort({
    createdAt: 1 // Earlier submissions first (first to solve gets rank 1)
  })
  .limit(parseInt(limit))
  .skip((parseInt(page) - 1) * parseInt(limit));

  // Format solutions for frontend (ranked by submission time - first to solve gets rank 1)
  const formattedSolutions = solutions.map((solution, index) => ({
    id: solution._id,
    username: solution.user?.username || 'Unknown User',
    fullName: solution.user?.fullName || solution.user?.username || 'Unknown User',
    code: solution.code,
    language: solution.language,
    approach: solution.approach || '',
    timestamp: solution.createdAt,
    problemId: problemId, // Keep original problemId for frontend compatibility
    problemTitle: solution.question?.title || problemId,
    stats: {
      runtime: 'N/A', // SolutionTest doesn't have runtime stats
      memory: 'N/A',
      runtimePercentile: null,
      memoryPercentile: null
    },
    rank: index + 1 // Rank based on submission order (first to solve = rank 1)
  }));

  res.status(StatusCodes.OK).json({
    success: true,
    count: formattedSolutions.length,
    solutions: formattedSolutions
  });
});

/**
 * Get all solutions by a specific user using the User.sol relationship
 * @route GET /api/v1/solutions/user/:userId
 * @access Public
 */
const getSolutionsByUser = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { limit = 20, page = 1 } = req.query;

  // Find user and populate their solutions from the sol Map
  const user = await User.findById(userId);

  if (!user) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('User not found');
  }

  // Get all solution IDs from the user's sol Map
  const allSolutionIds = [];
  if (user.sol) {
    for (const [, solutionIds] of user.sol) {
      allSolutionIds.push(...solutionIds);
    }
  }

  if (allSolutionIds.length === 0) {
    return res.status(StatusCodes.OK).json({
      success: true,
      count: 0,
      solutions: []
    });
  }

  // Get solutions with pagination
  const solutions = await Solution.find({
    _id: { $in: allSolutionIds }
  })
    .populate('user', 'fullName username')
    .populate('question', 'title difficulty questionLink')
    .sort({ createdAt: -1 })
    .limit(parseInt(limit))
    .skip((parseInt(page) - 1) * parseInt(limit));

  const formattedSolutions = solutions.map(solution => ({
    id: solution._id,
    problemTitle: solution.question?.title || 'Unknown Problem',
    problemLink: solution.question?.questionLink || '',
    difficulty: solution.question?.difficulty || 'unknown',
    language: solution.language,
    code: solution.code,
    approach: solution.approach || '',
    timestamp: solution.createdAt
  }));

  res.status(StatusCodes.OK).json({
    success: true,
    count: formattedSolutions.length,
    solutions: formattedSolutions
  });
});

/**
 * Get solution statistics
 * @route GET /api/v1/solutions/stats
 * @access Public
 */
const getSolutionStats = asyncHandler(async (req, res) => {
  const totalSolutions = await Solution.countDocuments();
  const uniqueUsers = await Solution.distinct('user').length;
  const uniqueProblems = await Solution.distinct('question').length;

  // Get language distribution
  const languageStats = await Solution.aggregate([
    { $group: { _id: '$language', count: { $sum: 1 } } },
    { $sort: { count: -1 } }
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    stats: {
      totalSolutions,
      acceptedSolutions: totalSolutions, // All solutions in SolutionTest are accepted
      uniqueUsers,
      uniqueProblems,
      acceptanceRate: '100.00', // All solutions in SolutionTest are accepted
      languageDistribution: languageStats
    }
  });
});

export {
  getSolutionsByProblem,
  getSolutionsByUser,
  getSolutionStats
};
