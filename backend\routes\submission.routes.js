/**
 * Submission routes
 * Defines routes for submission-related endpoints
 */
import express from 'express';
import { 
  runCode, 
  submitSolution, 
  checkSubmission, 
  checkRunResult, 
  handleQuestionEntry,
  afterSubmission,
  getLatestSubmissionId 
} from '../controllers/submission.controller.js';
import { isLoggedin } from '../middlewares/auth.middleware.js';

const router = express.Router();

/**
 * @route   POST /api/v1/leetcode/run-code/:questionTitleSlug
 * @desc    Run code for a problem
 * @access  Public
 */
router.post('/run-code/:questionTitleSlug', runCode);

/**
 * @route   POST /api/v1/leetcode/submit/:questionTitleSlug
 * @desc    Submit code for a problem
 * @access  Public
 */
router.post('/submit/:questionTitleSlug', submitSolution);

/**
 * @route   GET /api/v1/leetcode/submissions/:submissionId/check
 * @desc    Check submission status
 * @access  Public
 */
router.get('/submissions/:submissionId/check', checkSubmission);

/**
 * @route   GET /api/v1/leetcode/run-code/check/:interpretId
 * @desc    Check run code result
 * @access  Public
 */
router.get('/run-code/check/:interpretId', checkRunResult);

/**
 * @route   POST /api/v1/leetcode/handle-question
 * @desc    Create or update question document for daily questions
 * @access  Public
 */
router.post('/handle-question', handleQuestionEntry);

/**
 * @route   GET /api/v1/leetcode/submissions/latest
 * @desc    Get latest submission ID
 * @access  Public
 */
router.get('/submissions/latest', getLatestSubmissionId);

/**
 * @route   POST /api/v1/leetcode/aftersubmit
 * @desc    Save solution after submission (replaces the solutions endpoint)
 * @access  Private (requires authentication)
 */
router.post('/aftersubmit', isLoggedin, afterSubmission);

export default router;
