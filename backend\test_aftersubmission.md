# Testing the AfterSubmission Endpoint

## Endpoint

`POST /api/v1/leetcode/aftersubmit`

## Test Cases

### 1. Basic Valid Submission

```bash
curl -X POST http://localhost:3000/api/v1/leetcode/aftersubmit \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "USER_ID_HERE",
    "questionTitle": "Two Sum",
    "problemSlug": "two-sum",
    "difficulty": "Easy",
    "code": "class Solution {\n    public int[] twoSum(int[] nums, int target) {\n        // solution code here\n    }\n}",
    "language": "java",
    "approach": "Hash table approach",
    "status": "Accepted",
    "topicTags": ["Array", "Hash Table"]
  }'
```

### 2. Authenticated User (with cookies)

```bash
curl -X POST http://localhost:3000/api/v1/leetcode/aftersubmit \
  -H "Content-Type: application/json" \
  -H "Cookie: token=YOUR_JWT_TOKEN" \
  -d '{
    "questionTitle": "Two Sum",
    "problemSlug": "two-sum",
    "difficulty": "Easy",
    "code": "def twoSum(nums, target):\n    # Python solution\n    pass",
    "language": "python",
    "approach": "Dictionary approach",
    "status": "Accepted"
  }'
```

### 3. Error Cases

- Missing userId and no authentication: Should return 401
- Non-existent userId: Should return 404
- Non-accepted status: Should return 400
- Duplicate code: Should return success with warning

## Expected Response Format

```json
{
  "success": true,
  "message": "Solution saved successfully",
  "isDuplicate": false,
  "warning": null,
  "solution": {
    "id": "solution_id",
    "questionTitle": "Two Sum",
    "language": "java",
    "createdAt": "2025-07-06T...",
    "updatedAt": "2025-07-06T...",
    "isUpdate": false,
    "userXP": 10,
    "userStreak": 1
  }
}
```

## Migration Notes

- Frontend now calls `/api/v1/leetcode/aftersubmit` instead of `/api/v1/solutions`
- All logic from `saveSolution` has been preserved in `afterSubmission`
- Includes XP and streak calculation
- Handles duplicate code detection
- Supports both authenticated and userId-based requests
