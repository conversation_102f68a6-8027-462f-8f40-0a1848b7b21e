const { MongoClient } = require('mongodb');

// Direct MongoDB connection to fix the index issue
const fixPhoneIndex = async () => {
  // Replace this with your actual MongoDB connection string
  const uri = 'mongodb://localhost:27017/bigO_database'; // Update this to match your database
  
  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db('bigO_database');
    const collection = db.collection('users');

    // List all indexes
    console.log('Current indexes:');
    const indexes = await collection.listIndexes().toArray();
    console.log(indexes.map(idx => ({ name: idx.name, key: idx.key })));

    // Drop the phone_1 index
    try {
      const result = await collection.dropIndex('phone_1');
      console.log('Successfully dropped phone_1 index:', result);
    } catch (error) {
      if (error.codeName === 'IndexNotFound') {
        console.log('phone_1 index was not found (already removed or never existed)');
      } else {
        console.error('Error dropping index:', error.message);
      }
    }

    // List indexes after removal
    console.log('\nIndexes after removal:');
    const indexesAfter = await collection.listIndexes().toArray();
    console.log(indexesAfter.map(idx => ({ name: idx.name, key: idx.key })));

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
};

fixPhoneIndex();
