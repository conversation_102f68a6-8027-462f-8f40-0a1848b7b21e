# Merged BigO Backend API Documentation

This merged backend provides a comprehensive set of APIs that include both BigO website functionality and LeetCode integration services. The backend runs on localhost:5000 and provides APIs for user management, daily challenges, problem solving, and submission tracking.

## Base URL
```
http://localhost:5000
```

## Table of Contents

- [BigO Website Routes](#bigo-website-routes)
- [LeetCode Authentication](#leetcode-authentication)
- [Daily Challenge](#daily-challenge)
- [Problem-Specific Routes](#problem-specific-routes)
- [Submission Status](#submission-status)
- [Questions](#questions)

## BigO Website Routes

### Health Check
```
GET /health
```

**Description:** Check if the merged backend is running properly.

**Response:**
```json
{
  "status": "healthy",
  "service": "BigO Backend with LeetCode API"
}
```

### Basic Ping
```
GET /ping
```

**Description:** Basic connectivity test.

**Response:**
```
pong
```

### User Routes
```
/api/v1/user/*
```

**Description:** All existing BigO user management functionality (preserved from original backend).

## LeetCode Authentication

### Check Authentication Status

```
GET /api/v1/leetcode/auth-check
```

**Description:** Checks if the user is authenticated with LeetCode.

**Headers Required:**
- `cookie` (optional): LeetCode session cookies
- `x-csrftoken` (optional): CSRF token from LeetCode

**Response:**
```json
{
  "isAuthenticated": true|false,
  "username": "username",
  "cookies": { /* cookie data if available */ },
  "timestamp": "2023-01-01T00:00:00.000Z"
}
```

### Fetch Cookies from LeetCode

```
GET /api/v1/leetcode/fetch-cookies
```

**Description:** Special endpoint to directly fetch cookies from LeetCode, useful for browsers with strict CORS policies.

**Response:**
```json
{
  "isAuthenticated": true|false,
  "username": "username",
  "cookies": { /* cookie data */ },
  "csrfToken": "token",
  "sessionCookie": "cookie",
  "timestamp": "2023-01-01T00:00:00.000Z"
}
```

## Daily Challenge

### Get Daily Challenge

```
GET /api/v1/leetcode/daily
```

**Description:** Fetches the current LeetCode daily challenge.

**Response:**
```json
{
  "titleSlug": "problem-title-slug",
  /* other problem details */
}
```

### Submit Daily Challenge Solution

```
POST leetcode/daily/submit
```

**Description:** Submits a solution for the daily challenge.

**Headers Required:**
- `cookie`: LeetCode session cookies
- `x-csrftoken`: CSRF token from LeetCode
- `content-type`: application/json

**Request Body:**
```json
{
  "lang": "python3",
  "question_id": "123",
  "typed_code": "def solution(nums):\n    return sum(nums)"
}
```

**Response:**
```json
{
  "submission_id": "123456789"
}
```

### Submit Daily Challenge Solution (Proxy Method)

```
POST /api/v1/leetcode/problems/submit-as-proxy
```

**Description:** Alternative method to submit a solution for the daily challenge using a proxy approach.

**Request Body:**
```json
{
  "leetcodePayload": {
    "lang": "python3",
    "question_id": "123",
    "typed_code": "def solution(nums):\n    return sum(nums)"
  },
  "headers": {
    "Cookie": "LEETCODE_SESSION=abc123; csrftoken=xyz789",
    "x-csrftoken": "xyz789",
    "User-Agent": "Mozilla/5.0...",
    "Origin": "https://leetcode.com",
    "Referer": "https://leetcode.com/problems/..."
  }
}
```

**Response:**
```json
{
  "submission_id": "123456789"
}
```

## Problem-Specific Routes

### Get Problem by Title Slug

```
GET /api/v1/leetcode/problems/:titleSlug
```

**Description:** Fetches a specific LeetCode problem by its title slug.

**URL Parameters:**
- `titleSlug`: The title slug of the problem (e.g., "two-sum")

**Response:**
```json
{
  "titleSlug": "two-sum",
  /* problem details */
}
```

### Submit Problem Solution

```
POST /api/v1/leetcode/problems/submit
```

**Description:** Submits a solution for any LeetCode problem.

**Headers Required:**
- `cookie`: LeetCode session cookies
- `x-csrftoken`: CSRF token from LeetCode
- `content-type`: application/json

**Request Body:**
```json
{
  "titleSlug": "two-sum",
  "lang": "python3",
  "question_id": "1",
  "typed_code": "def twoSum(nums, target):\n    # solution code"
}
```

**Response:**
```json
{
  "submission_id": "123456789"
}
```

### Submit Problem Solution (Proxy Method)

```
POST /api/v1/leetcode/problems/submit-as-proxy
```

**Description:** Alternative method to submit a solution for any LeetCode problem using a proxy approach.

**Request Body:**
```json
{
  "leetcodePayload": {
    "titleSlug": "two-sum",
    "lang": "python3",
    "question_id": "1",
    "typed_code": "def twoSum(nums, target):\n    # solution code"
  },
  "headers": {
    "Cookie": "LEETCODE_SESSION=abc123; csrftoken=xyz789",
    "x-csrftoken": "xyz789",
    "User-Agent": "Mozilla/5.0...",
    "Origin": "https://leetcode.com",
    "Referer": "https://leetcode.com/problems/two-sum/"
  }
}
```

**Response:**
```json
{
  "submission_id": "123456789"
}
```

## Submission Status

### Check Submission Status

```
GET /api/v1/leetcode/submissions/:submissionId/check
```

**Description:** Checks the status of a submission.

**URL Parameters:**
- `submissionId`: The ID of the submission to check

**Headers Required:**
- `cookie`: LeetCode session cookies
- `x-csrftoken`: CSRF token from LeetCode

**Response:**
```json
{
  "state": "SUCCESS",
  "status_code": 10,
  "lang": "python3",
  "runtime": "36 ms",
  "memory": "14.2 MB",
  "total_correct": "All test cases passed",
  "total_testcases": "57",
  "runtime_percentile": "95.21",
  "memory_percentile": "92.16",
  "status_runtime": "36 ms",
  "status_memory": "14.2 MB"
}
```

### Check Submission Status (Proxy Method)

```
POST /api/v1/leetcode/submissions/check-as-proxy
```

**Description:** Alternative method to check the status of a submission using a proxy approach.

**Request Body:**
```json
{
  "submissionId": "123456789",
  "headers": {
    "Cookie": "LEETCODE_SESSION=abc123; csrftoken=xyz789",
    "x-csrftoken": "xyz789",
    "User-Agent": "Mozilla/5.0...",
    "Origin": "https://leetcode.com",
    "Referer": "https://leetcode.com/problems/..."
  }
}
```

**Response:**
```json
{
  "state": "SUCCESS",
  "status_code": 10,
  "lang": "python3",
  "runtime": "36 ms",
  "memory": "14.2 MB",
  "total_correct": "All test cases passed",
  "total_testcases": "57",
  "runtime_percentile": "95.21",
  "memory_percentile": "92.16",
  "status_runtime": "36 ms",
  "status_memory": "14.2 MB"
}
```

## Questions

### Get All Questions

```
GET /api/v1/leetcode/questions
```

**Description:** Fetch all LeetCode questions with pagination and filtering.

**Query Parameters:**
- `category` (optional): Question category (default: "all-code-essentials")
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of questions per page (default: 100, max: 500)
- `difficulty` (optional): Filter by difficulty ("EASY", "MEDIUM", "HARD")
- `search` (optional): Search by title or ID

**Response:**
```json
{
  "questions": [
    {
      "questionId": "1",
      "questionFrontendId": "1",
      "title": "Two Sum",
      "titleSlug": "two-sum",
      "difficulty": "EASY",
      "status": null,
      "isPaidOnly": false,
      "topicTags": [
        {"name": "Array", "slug": "array"},
        {"name": "Hash Table", "slug": "hash-table"}
      ],
      "acRate": 0.4781
    }
  ],
  "total": 100,
  "page": 1,
  "pageSize": 100
}
```

## Additional Routes

### Run Code (Without Submitting)

```
POST /api/v1/leetcode/run-code/:questionTitleSlug
```

**Description:** Run code for a problem without submitting it.

### Submit Code

```
POST /api/v1/leetcode/submit/:questionTitleSlug
```

**Description:** Submit code for evaluation.

### Check Run Result

```
GET /api/v1/leetcode/run-code/check/:interpretId
```

**Description:** Check the result of a code run.

### Get Latest Submission

```
GET /api/v1/leetcode/submissions/latest
```

**Description:** Get the latest submission ID (currently returns 404 - not implemented).

## Error Handling

All endpoints return appropriate HTTP status codes:

- `200 OK`: Request successful
- `400 Bad Request`: Missing required parameters
- `403 Forbidden`: Authentication issues
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server-side errors

Error responses follow this format:

```json
{
  "error": "Error message",
  "details": { /* Additional error details if available */ }
}
```

## Authentication Notes

Most endpoints require LeetCode authentication. You need to:

1. Be logged into LeetCode in your browser
2. Pass the necessary cookies and CSRF token in your requests
3. For proxy methods, extract and pass these headers from your browser

The proxy methods are particularly useful for browsers with strict CORS policies or when direct cookie sharing is problematic.