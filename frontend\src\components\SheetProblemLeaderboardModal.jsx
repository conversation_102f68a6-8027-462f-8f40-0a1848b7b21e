import { useState } from 'react';
import PropTypes from 'prop-types';
import { Code, Clock, HardDrive, Trophy, Medal, Award, X } from 'lucide-react';
import FullScreenModal from './FullScreenModal';

const SheetProblemLeaderboardModal = ({ isOpen, onClose, problemTitle, solutions }) => {
  const [selectedSolution, setSelectedSolution] = useState(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  const openViewModal = (solution) => {
    setSelectedSolution(solution);
    setIsViewModalOpen(true);
  };

  const closeViewModal = () => {
    setIsViewModalOpen(false);
    setSelectedSolution(null);
  };

  const getLanguageDisplay = (lang) => {
    const langMap = {
      'python3': 'Python',
      'javascript': 'JavaScript',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C'
    };
    return langMap[lang] || lang;
  };

  const getMedalIcon = (index) => {
    switch (index) {
      case 0:
        return <Trophy size={18} className="text-yellow-400" />;
      case 1:
        return <Medal size={18} className="text-gray-300" />;
      case 2:
        return <Award size={18} className="text-amber-600" />;
      default:
        return null;
    }
  };

  const getRowBackground = (index) => {
    switch (index) {
      case 0:
        return 'bg-yellow-400/10 border-l-4 border-yellow-400';
      case 1:
        return 'bg-gray-400/10 border-l-4 border-gray-300';
      case 2:
        return 'bg-amber-400/10 border-l-4 border-amber-500';
      default:
        return 'hover:bg-purple-500/8 border-l-4 border-transparent';
    }
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'N/A';
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    const mins = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    if (mins < 1) return 'Just now';
    if (mins < 60) return `${mins}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 z-[200] bg-black/90 backdrop-blur-sm flex items-center justify-center px-2">
        <div className="relative w-[95%] sm:w-[90%] md:w-[60%] max-w-3xl">
          <div className="bg-gray-900 rounded-lg border border-gray-700 shadow-lg flex flex-col max-h-[90vh]">
            <div className="flex items-center justify-between px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600">
              <h3 className="text-sm sm:text-base font-bold text-white flex items-center gap-2 truncate">
                <Trophy size={16} className="text-yellow-300" />
                {problemTitle} - Leaderboard
              </h3>
              <button
                onClick={onClose}
                className="text-white bg-white/10 hover:bg-white/20 p-1 rounded-md"
              >
                <X size={16} />
              </button>
            </div>

            <div className="flex-1 p-2 sm:p-4 overflow-y-auto">
              <div className="bg-gray-800/50 rounded-lg border border-gray-700/50 overflow-x-auto">
                <table className="w-full min-w-[500px] text-left">
                  <thead className="bg-gray-800/50 text-gray-300 text-xs sm:text-sm">
                    <tr>
                      <th className="py-2 px-3 text-center">#</th>
                      <th className="py-2 px-3">Username</th>
                      <th className="py-2 px-3 text-center">Language</th>
                      <th className="py-2 px-3 text-center">Submitted</th>
                      <th className="py-2 px-3 text-center">View</th>
                    </tr>
                  </thead>
                  <tbody className="text-gray-300 divide-y divide-gray-700 text-xs sm:text-sm">
                    {solutions.length === 0 ? (
                      <tr>
                        <td colSpan="5" className="text-center py-6 text-gray-400">
                          <div className="flex flex-col items-center gap-2">
                            <Trophy size={32} className="text-gray-500" />
                            <p>No solutions submitted yet. Be the first!</p>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      solutions.map((solution, index) => (
                        <tr
                          key={solution._id}
                          className={`${getRowBackground(index)} transition-all duration-200`}
                        >
                          <td className="px-3 py-2 text-center">
                            <div className="flex items-center justify-center gap-1">
                              {getMedalIcon(index)}
                              <span>{index + 1}</span>
                            </div>
                          </td>
                          <td className="px-3 py-2 truncate">{solution.user?.fullName || solution.user?.username || 'Unknown'}</td>
                          <td className="px-3 py-2 text-center">
                            <span className="bg-purple-500/20 text-purple-300 px-2 py-0.5 rounded">
                              {getLanguageDisplay(solution.language)}
                            </span>
                          </td>
                          <td className="px-3 py-2 text-center" title={new Date(solution.createdAt).toLocaleString()}>
                            {formatTimestamp(solution.createdAt)}
                          </td>
                          <td className="px-3 py-2 text-center">
                            <button
                              onClick={() => openViewModal(solution)}
                              className="text-purple-400 hover:text-purple-300 p-1 rounded hover:bg-purple-500/20"
                            >
                              <Code size={16} />
                            </button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* View Solution Modal */}
      {selectedSolution && (
        <FullScreenModal
          isOpen={isViewModalOpen}
          onClose={closeViewModal}
          title={`${selectedSolution.user?.fullName || selectedSolution.user?.username}'s Solution (${getLanguageDisplay(selectedSolution.language)})`}
        >
          <div className="space-y-6 sm:space-y-8">
            {/* Stats */}
            {selectedSolution.stats && (selectedSolution.stats.runtime || selectedSolution.stats.memory) && (
              <div className="bg-gray-800/50 p-4 sm:p-6 rounded-xl border border-purple-500/20">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <div className="bg-purple-500/20 p-2 rounded-full">
                      <Clock size={20} className="text-purple-400" />
                    </div>
                    <div>
                      <div className="text-gray-400 text-xs">Runtime</div>
                      <div className="text-white text-base font-medium">{selectedSolution.stats.runtime}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="bg-purple-500/20 p-2 rounded-full">
                      <HardDrive size={20} className="text-purple-400" />
                    </div>
                    <div>
                      <div className="text-gray-400 text-xs">Memory</div>
                      <div className="text-white text-base font-medium">{selectedSolution.stats.memory}</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Approach */}
            {selectedSolution.approach && (
              <div className="bg-gray-800/50 p-4 sm:p-6 rounded-xl border border-purple-500/20">
                <h3 className="flex items-center gap-2 text-white text-lg font-semibold mb-2">
                  <Trophy size={20} className="text-purple-400" />
                  Approach
                </h3>
                <pre className="bg-gray-900/70 text-gray-300 text-sm whitespace-pre-wrap p-4 rounded-lg">
                  {selectedSolution.approach}
                </pre>
              </div>
            )}

            {/* Code */}
            <div className="bg-gray-800/50 p-4 sm:p-6 rounded-xl border border-purple-500/20">
              <h3 className="flex items-center gap-2 text-white text-lg font-semibold mb-2">
                <Code size={20} className="text-purple-400" />
                Solution Code
              </h3>
              <pre className="bg-gray-900/70 text-gray-300 text-xs sm:text-sm whitespace-pre-wrap p-4 rounded-lg overflow-x-auto">
                {selectedSolution.code}
              </pre>
            </div>

            {/* Submission Info */}
            <div className="bg-gray-800/50 p-4 sm:p-6 rounded-xl border border-purple-500/20">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 text-center">
                <div>
                  <div className="text-gray-400 text-xs">Language</div>
                  <div className="text-white font-medium">{getLanguageDisplay(selectedSolution.language)}</div>
                </div>
                <div>
                  <div className="text-gray-400 text-xs">Submitted</div>
                  <div className="text-white font-medium">{formatTimestamp(selectedSolution.createdAt)}</div>
                </div>
                <div>
                  <div className="text-gray-400 text-xs">User</div>
                  <div className="text-white font-medium">{selectedSolution.user?.fullName || selectedSolution.user?.username || 'Unknown'}</div>
                </div>
              </div>
            </div>
          </div>
        </FullScreenModal>
      )}
    </>
  );
};

SheetProblemLeaderboardModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  problemTitle: PropTypes.string.isRequired,
  solutions: PropTypes.arrayOf(PropTypes.shape({
    _id: PropTypes.string.isRequired,
    user: PropTypes.shape({
      fullName: PropTypes.string,
      username: PropTypes.string,
      email: PropTypes.string
    }),
    language: PropTypes.string,
    code: PropTypes.string,
    approach: PropTypes.string,
    createdAt: PropTypes.string,
    stats: PropTypes.shape({
      runtime: PropTypes.string,
      memory: PropTypes.string
    })
  })).isRequired
};

export default SheetProblemLeaderboardModal;
