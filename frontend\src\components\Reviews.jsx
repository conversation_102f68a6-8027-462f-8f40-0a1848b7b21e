import { useEffect, useState, useRef } from 'react';
import ReviewCard from './ReviewCard';
import { reviewData } from '../data/reviewData';
import {
  FaCode, FaLayerGroup, FaNetworkWired, FaTree, FaListOl, FaRandom,
  FaChevronLeft, FaChevronRight
} from 'react-icons/fa';

const getIconComponent = (iconName) => {
  switch (iconName) {
    case 'FaCode': return <FaCode />;
    case 'FaLayerGroup': return <FaLayerGroup />;
    case 'FaNetworkWired': return <FaNetworkWired />;
    case 'FaTree': return <FaTree />;
    case 'FaListOl': return <FaListOl />;
    case 'FaRandom': return <FaRandom />;
    default: return <FaCode />;
  }
};

const Reviews = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const autoSlideRef = useRef(null);
  const total = reviewData.length;

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % total);
  };

  const handlePrev = () => {
    setCurrentIndex((prev) => (prev - 1 + total) % total);
  };

  useEffect(() => {
    autoSlideRef.current = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % total);
    }, 6000);
    return () => clearInterval(autoSlideRef.current);
  }, [total]);

  const getCardClass = (index) => {
    if (index === currentIndex) return 'z-30 scale-100 opacity-100';
    if (index === (currentIndex - 1 + total) % total) return 'z-20 scale-75 sm:scale-80 opacity-50 -translate-x-16 sm:-translate-x-90';
    if (index === (currentIndex + 1) % total) return 'z-20 scale-75 sm:scale-80 opacity-50 translate-x-16 sm:translate-x-90';
    return 'hidden';
  };

  return (
    <section className="w-full py-10 sm:py-16 max-w-7xl mx-auto relative overflow-hidden px-4">
      <h2 className="text-3xl sm:text-4xl font-bold text-center mb-3 sm:mb-5 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">
        Reviews
      </h2>

      <p className="text-center text-xl sm:text-2xl font-bold mb-6 sm:mb-8 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">
        Listen to our Learner's
      </p>

      {/* Navigation Arrows */}
      <button
        onClick={handlePrev}
        className="absolute left-1 xs:left-2 sm:left-5 top-1/2 transform -translate-y-1/2 z-50 bg-white p-1.5 xs:p-2 rounded-full shadow text-purple-600 hover:bg-purple-100 cursor-pointer"
      >
        <FaChevronLeft className="text-xs xs:text-sm sm:text-base" />
      </button>
      <button
        onClick={handleNext}
        className="absolute right-1 xs:right-2 sm:right-5 top-1/2 transform -translate-y-1/2 z-50 bg-white p-1.5 xs:p-2 rounded-full shadow text-purple-600 hover:bg-purple-100 cursor-pointer"
      >
        <FaChevronRight className="text-xs xs:text-sm sm:text-base" />
      </button>

      {/* Card Carousel */}
      <div className="relative flex justify-center items-center h-[300px] xs:h-[350px] sm:h-[400px] overflow-hidden">
        {reviewData.map((item, index) => (
          <div
            key={item.id}
            className={`absolute transition-all duration-700 ease-in-out transform ${getCardClass(index)}`}
          >
              <ReviewCard
                title={item.name}
                description={item.review}
                image={item.image}
              />
            
          </div>
        ))}
      </div>

      {/* Dots */}
     {/* <div className="flex justify-center mt-6 gap-2">
        {reviewData.map((_, i) => (
          <button
            key={i}
            onClick={() => setCurrentIndex(i)}
            className={`w-3 h-3 rounded-full ${
              i === currentIndex ? 'bg-purple-600' : 'bg-gray-300'
            }`}
          />
        ))}
      </div>*/}
    </section>
  );
};

export default Reviews;
