import { Link } from 'react-router-dom';
import ReviewCard from './ReviewCard';
import { reviewData } from '../data/reviewData';
import {
  FaCode,
  FaLayerGroup,
  FaNetworkWired,
  FaTree,
  FaListOl,
  FaRandom
} from 'react-icons/fa';

// Function to get the icon component based on the icon name
const getIconComponent = (iconName) => {
  switch (iconName) {
    case 'FaCode':
      return <FaCode />;
    case 'FaLayerGroup':
      return <FaLayerGroup />;
    case 'FaNetworkWired':
      return <FaNetworkWired />;
    case 'FaTree':
      return <FaTree />;
    case 'FaListOl':
      return <FaListOl />;
    case 'FaRandom':
      return <FaRandom />;
    default:
      return <FaCode />;
  }
};

const Reviews = () => {
  const duplicatedTopics = [...reviewData, ...reviewData];

  return (
    <section className="w-full py-16 max-w-7xl">
      <h2 className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">
        Reviews
      </h2>
      
      {/* Constrained scrolling container */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-900/20 to-transparent rounded-xl border border-purple-500/20 p-6">
        <div className="absolute left-0 top-0 w-20 h-full bg-gradient-to-r from-gray-900 to-transparent z-10 pointer-events-none"></div>
        <div className="absolute right-0 top-0 w-20 h-full bg-gradient-to-l from-gray-900 to-transparent z-10 pointer-events-none"></div>

        <p className="pl-5 text-2xl font-bold mb-8 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">Listen to our Learner's</p>

        <div className="flex gap-6 animate-scroll-right">
          {duplicatedTopics.map((topic, index) => (
            <Link 
              key={`${topic.id}-${index}`} 
              to={`/topics/${topic.id}`} 
              className="no-underline flex-shrink-0 w-80"
            >
              <ReviewCard
                title={topic.name}
                description={topic.review}
                icon={getIconComponent(topic.icon)}
              />
            </Link>
          ))}
        </div>
      </div>
      
      {/* CSS Animation Styles */}
      <style jsx>{`
        @keyframes scroll-right {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        
        .animate-scroll-right {
          animation: scroll-right 10s linear infinite;
        }
        
        .animate-scroll-right:hover {
          animation-play-state: paused;
        }
      `}</style>
    </section>
  );
};

export default Reviews;

