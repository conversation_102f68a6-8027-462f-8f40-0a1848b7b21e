import express from 'express';
import { isLoggedin } from '../middlewares/auth.middleware.js';
import { solveProblem, getQuestionStatus, getUserSheetQuestions, getSheetQuestionsByTopic, getProblemSolutions } from '../controllers/sheet.controller.js';

const router = express.Router();

// Get all sheet questions organized by topic (public endpoint)
router.get('/questions-by-topic', getSheetQuestionsByTopic);

// Get all solutions for a specific problem (public endpoint for leaderboard)
router.get('/problem-solutions/:problemTitle', getProblemSolutions);

// Toggle question solved status (requires authentication)
router.post('/toggleq', isLoggedin, solveProblem);

// Get specific question status for authenticated user
router.get('/status/:title', isLoggedin, getQuestionStatus);

// Get all sheet questions solved by authenticated user
router.get('/user-questions', isLoggedin, getUserSheetQuestions);

export default router;