/**
 * LeetCode Security middleware
 * Provides security-related middleware for LeetCode API endpoints
 */
import cors from 'cors';
import helmet from 'helmet';

/**
 * Configure CORS middleware for LeetCode API
 * @returns {Function} CORS middleware
 */
const configureLeetcodeCors = () => {
  return cors({
    origin: [
      'http://localhost:5173',
      'http://localhost:3000',
      'https://big-o-web-staging.onrender.com',
      'https://big-o-web-staging.onrender.com/',
      process.env.FRONTEND_URL
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-csrftoken', 'cookie'],
    exposedHeaders: ['Content-Type', 'Authorization']
  });
};

/**
 * Configure Helmet middleware for LeetCode API
 * @returns {Function} Helmet middleware
 */
const configureLeetcodeHelmet = () => {
  return helmet({
    contentSecurityPolicy: false, // Disable CSP for now
    crossOriginEmbedderPolicy: false // Disable COEP for now
  });
};

export {
  configureLeetcodeCors,
  configureLeetcodeHelmet
};
