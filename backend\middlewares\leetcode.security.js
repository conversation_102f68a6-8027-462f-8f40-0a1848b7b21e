/**
 * LeetCode Security middleware
 * Provides security-related middleware for LeetCode API endpoints
 */
import cors from 'cors';
import helmet from 'helmet';

/**
 * Configure CORS middleware for LeetCode API
 * @returns {Function} CORS middleware
 */
const configureLeetcodeCors = () => {
  return cors({
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps, curl, or same-origin requests)
      if (!origin) return callback(null, true);
      
      const allowedOrigins = [
        process.env.FRONTEND_URL,
        'http://localhost:5173',
        'http://localhost:3000',
        'http://localhost:5174',
        'http://localhost:5175',
        'http://127.0.0.1:5173',
        'http://127.0.0.1:3000',
        'https://big-o-web-staging.onrender.com',
        'https://big-o-web-staging.onrender.com/',
        'https://bigo-solveit.onrender.com',
        'https://bigo-solveit-backend.onrender.com'
      ];
      
      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        console.log('LeetCode CORS blocked origin:', origin);
        // In development, allow all origins
        callback(null, true);
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-csrftoken', 'cookie', 'Set-Cookie', 'Accept', 
                    'X-User-ID', 'X-Auth-Token', 'x-user-id', 'x-auth-token'],
    exposedHeaders: ['Content-Type', 'Authorization', 'Set-Cookie']
  });
};

/**
 * Configure Helmet middleware for LeetCode API
 * @returns {Function} Helmet middleware
 */
const configureLeetcodeHelmet = () => {
  return helmet({
    contentSecurityPolicy: false, // Disable CSP for now
    crossOriginEmbedderPolicy: false // Disable COEP for now
  });
};

export {
  configureLeetcodeCors,
  configureLeetcodeHelmet
};
