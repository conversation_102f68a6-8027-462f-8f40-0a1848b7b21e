import PropTypes from 'prop-types';

const LearningCard = ({ title, description, icon }) => {
  return (
    <div className="relative group overflow-hidden rounded-xl p-[2px]">
      <div className="absolute inset-0 z-0 rounded-xl border-animation" />
      <div className="relative z-10 h-full bg-[#1a0b2e]/90 backdrop-blur-sm rounded-xl p-6 flex flex-col items-center justify-center gap-4 min-h-[200px] cursor-pointer">
        <div className="text-4xl text-purple-400 group-hover:text-purple-300 transition-colors duration-300">
          {icon}
        </div>
        <h3 className="text-xl font-semibold text-white text-center group-hover:text-purple-200 transition-colors duration-300">
          {title}
        </h3>
        <p className="text-gray-400 text-sm text-center group-hover:text-gray-300 transition-colors duration-300">
          {description}
        </p>
      </div>

      {/* Glow animation styles */}
      <style jsx>{`
        .border-animation {
          background: linear-gradient(
            270deg,
            #d946ef,
            #9333ea,
            #6366f1,
            #9333ea,
            #d946ef
          );
          background-size: 400% 400%;
          animation: glow-border 6s linear infinite;
        }

        @keyframes glow-border {
          0% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
          100% {
            background-position: 0% 50%;
          }
        }
      `}</style>
    </div>
  );
};

LearningCard.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  icon: PropTypes.node.isRequired
};

export default LearningCard;
