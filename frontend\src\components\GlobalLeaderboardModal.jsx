import { useState, useEffect } from 'react';
import { X, <PERSON>, Flame, Award } from 'lucide-react';

const GlobalLeaderboardModal = ({ isOpen, onClose }) => {
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch leaderboard data when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchLeaderboard();
    }
  }, [isOpen]);

  const fetchLeaderboard = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/leaderboard`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      const data = await response.json();
      
      if (data.success) {
        setLeaderboard(data.leaderboard || []);
      } else {
        setError(data.message || 'Failed to fetch leaderboard');
      }
    } catch (err) {
      console.error('Error fetching leaderboard:', err);
      setError('Failed to load leaderboard data');
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (position) => {
    switch (position) {
      case 1:
        return <Trophy className="text-yellow-400" size={24} />;
      case 2:
        return <Award className="text-gray-300" size={24} />;
      case 3:
        return <Award className="text-amber-600" size={24} />;
      default:
        return <span className="text-gray-400 font-bold text-lg">#{position}</span>;
    }
  };

  const getRankBadgeColor = (position) => {
    switch (position) {
      case 1:
        return 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-yellow-900';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-400 text-gray-900';
      case 3:
        return 'bg-gradient-to-r from-amber-500 to-amber-600 text-amber-900';
      default:
        return 'bg-gradient-to-r from-purple-600 to-purple-700 text-white';
    }
  };

  // When modal is open, prevent body scrolling
  useEffect(() => {
    if (isOpen) {
      // Save the current scroll position
      const scrollY = window.scrollY;
      
      // Add styles to body to prevent scrolling and maintain position
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
    } else {
      // Restore scrolling and position
      const scrollY = document.body.style.top;
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      
      // Scroll back to the original position
      window.scrollTo(0, parseInt(scrollY || '0') * -1);
    }
    
    // Cleanup function to restore scrolling when component unmounts
    return () => {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center pt-20" style={{ zIndex: 99999 }}>
      <div className="bg-gray-900/95 backdrop-blur-sm rounded-xl xs:rounded-2xl border border-purple-500/20 shadow-2xl w-[60%] max-w-[700px] min-w-[320px] mx-4 overflow-hidden" style={{ zIndex: 100000 }}>
        {/* Header */}
        <div className="flex items-center justify-between p-3 xs:p-4 sm:p-6 border-b border-purple-500/20">
          <div className="flex items-center gap-2 xs:gap-3">
            <div className="p-1.5 xs:p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
              <Trophy size={22} className="text-yellow-900" />
            </div>
            <div>
              <h2 className="text-lg xs:text-xl sm:text-2xl font-bold text-white">Global Leaderboard</h2>
              <p className="text-gray-400 text-xs xs:text-sm">Top performers ranked by XP and streak</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-1.5 xs:p-2 hover:bg-gray-800 rounded-lg transition-colors"
          >
            <X size={18} className="text-gray-400 hover:text-white" />
          </button>
        </div>

        {/* Content */}
        <div className="p-3 xs:p-4 sm:p-6 overflow-y-auto max-h-[calc(85vh-120px)]">
          {loading ? (
            <div className="flex items-center justify-center py-6 xs:py-8 sm:py-12">
              <div className="flex flex-col items-center gap-2 xs:gap-4">
                <div className="w-8 h-8 xs:w-10 sm:w-12 xs:h-10 sm:h-12 border-3 xs:border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin"></div>
                <p className="text-gray-400 text-sm xs:text-base">Loading leaderboard...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center py-6 xs:py-8 sm:py-12">
              <div className="text-center">
                <div className="text-red-400 text-lg xs:text-xl mb-1 xs:mb-2">⚠️</div>
                <p className="text-red-400 text-sm xs:text-base mb-3 xs:mb-4">{error}</p>
                <button
                  onClick={fetchLeaderboard}
                  className="px-3 xs:px-4 py-1.5 xs:py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm xs:text-base rounded-lg transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : leaderboard.length === 0 ? (
            <div className="flex items-center justify-center py-6 xs:py-8 sm:py-12">
              <div className="text-center">
                <Trophy className="mx-auto mb-2 xs:mb-4 text-gray-600" size={32} />
                <p className="text-gray-400 text-sm xs:text-base">No data available yet</p>
                <p className="text-gray-500 text-xs xs:text-sm">Start solving problems to appear on the leaderboard!</p>
              </div>
            </div>
          ) : (
            <div className="space-y-2 xs:space-y-3">
              {/* Header Row */}
              <div className="grid grid-cols-12 gap-2 xs:gap-4 px-2 xs:px-4 py-2 xs:py-3 bg-gray-800/30 rounded-lg border border-purple-500/10">
                <div className="col-span-1 text-gray-400 font-semibold text-xs xs:text-sm">Rank</div>
                <div className="col-span-5 text-gray-400 font-semibold text-xs xs:text-sm">Name</div>
                <div className="col-span-3 text-gray-400 font-semibold text-xs xs:text-sm">XP</div>
                <div className="col-span-3 text-gray-400 font-semibold text-xs xs:text-sm">Streak</div>
              </div>

              {/* Leaderboard Rows */}
              {leaderboard.map((user, index) => (
                <div
                  key={user.username || index}
                  className={`grid grid-cols-12 gap-2 xs:gap-4 px-2 xs:px-4 py-2 xs:py-3 sm:py-4 rounded-lg border transition-all duration-200 hover:scale-[1.02] ${
                    user.position <= 3
                      ? 'bg-gradient-to-r from-purple-900/20 to-purple-800/20 border-purple-500/30 shadow-lg'
                      : 'bg-gray-800/20 border-gray-700/30 hover:bg-gray-800/30'
                  }`}
                >
                  {/* Rank */}
                  <div className="col-span-1 flex items-center">
                    <div className={`flex items-center justify-center w-8 h-8 xs:w-10 xs:h-10 rounded-full ${getRankBadgeColor(user.position)}`}>
                      {user.position <= 3 ? (
                        <>
                          {user.position === 1 ? <Trophy className="text-yellow-400" size={20} /> :
                           user.position === 2 ? <Award className="text-gray-300" size={20} /> :
                           <Award className="text-amber-600" size={20} />}
                        </>
                      ) : (
                        <span className="font-bold text-xs xs:text-sm">#{user.position}</span>
                      )}
                    </div>
                  </div>

                  {/* Name */}
                  <div className="col-span-5 flex items-center">
                    <div>
                      <p className="text-white font-semibold truncate text-xs xs:text-sm sm:text-base">
                        {user.fullName || user.username || 'Anonymous'}
                      </p>
                      {user.username && user.username !== user.fullName && (
                        <p className="text-gray-400 text-xs xs:text-sm truncate">
                          @{user.username}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* XP */}
                  <div className="col-span-3 flex items-center">
                    <div className="flex items-center gap-1 xs:gap-2">
                      <div className="w-2 h-2 xs:w-3 xs:h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-blue-300 font-bold text-sm xs:text-base sm:text-lg">
                        {user.xp || 0}
                      </span>
                      <span className="text-gray-500 text-xs xs:text-sm">XP</span>
                    </div>
                  </div>

                  {/* Streak */}
                  <div className="col-span-3 flex items-center">
                    <div className="flex items-center gap-1 xs:gap-2">
                      <Flame size={16} className="text-orange-400" />
                      <span className="text-orange-300 font-bold text-sm xs:text-base sm:text-lg">
                        {user.streak || 0}
                      </span>
                      <span className="text-gray-500 text-xs xs:text-sm">day{user.streak !== 1 ? 's' : ''}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GlobalLeaderboardModal;
