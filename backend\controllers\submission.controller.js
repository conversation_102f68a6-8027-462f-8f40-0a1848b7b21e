/**
 * Submission controller
 * Handles HTTP requests for submission-related endpoints
 */
import asyncHandler from "express-async-handler";
import { StatusCodes } from "http-status-codes";
import SubmissionService from "../services/submissionService.js";
import {
  headersSchema,
  submissionRequestSchema,
} from "../utils/validationSchemas.js";
import User from "../models/user.model.js";
import Question from "../models/question.model.js";
import Solution from "../models/solution.model.js";
import SheetQuestion from "../models/sheetQuestionModal.js";

// import
/**
 * Run code for a problem
 * @route POST /api/v1/leetcode/run-code/:questionTitleSlug
 * @access Public
 */
const runCode = asyncHandler(async (req, res) => {
  const { questionTitleSlug } = req.params;

  // Validate submission data
  const { error, value: submissionData } = submissionRequestSchema.validate(
    req.body
  );
  if (error) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid submission data: ${error.message}`);
  }

  // Extract headers from request
  const headers = {
    cookie: req.headers.cookie,
    csrfToken: req.headers["x-csrftoken"],
    userAgent: req.headers["user-agent"],
    origin: req.headers.origin,
    referer: req.headers.referer,
  };

  // Validate headers
  const { error: headersError } = headersSchema.validate(headers);
  if (headersError) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid headers: ${headersError.message}`);
  }

  // Run the code
  const runResult = await SubmissionService.runCode(
    questionTitleSlug,
    submissionData,
    headers
  );

  res.status(StatusCodes.OK).json(runResult);
});

/**
 * Submit code for a problem
 * @route POST /api/v1/leetcode/submit/:questionTitleSlug
 * @access Public
 */
const submitSolution = asyncHandler(async (req, res) => {
  const { questionTitleSlug } = req.params;

  // Validate submission data
  const { error, value: submissionData } = submissionRequestSchema.validate(
    req.body
  );
  if (error) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid submission data: ${error.message}`);
  }

  // Extract headers from request
  const headers = {
    cookie: req.headers.cookie,
    csrfToken: req.headers["x-csrftoken"],
    userAgent: req.headers["user-agent"],
    origin: req.headers.origin,
    referer: req.headers.referer,
  };

  // Validate headers
  const { error: headersError } = headersSchema.validate(headers);
  if (headersError) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid headers: ${headersError.message}`);
  }

  try {
    // Submit the solution and get the submission ID
    const submissionId = await SubmissionService.submitSolutionAndGetId(
      questionTitleSlug,
      submissionData,
      headers
    );

    // Set the submission ID in the response headers
    res.setHeader("X-Submission-ID", submissionId);

    // Return the submission ID to the client
    res.status(StatusCodes.OK).json({
      submission_id: submissionId,
      state: "PENDING",
    });
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR);
    throw new Error(`Failed to submit solution: ${error.message}`);
  }
});

/**
 * Check submission status
 * @route GET /api/v1/leetcode/submissions/:submissionId/check
 * @access Public
 */
const checkSubmission = asyncHandler(async (req, res) => {
  const { submissionId } = req.params;

  // Extract headers from request
  const headers = {
    cookie: req.headers.cookie,
    csrfToken: req.headers["x-csrftoken"],
    userAgent: req.headers["user-agent"],
    origin: req.headers.origin,
    referer: req.headers.referer,
  };

  // Validate headers
  const { error: headersError } = headersSchema.validate(headers);
  if (headersError) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid headers: ${headersError.message}`);
  }

  // Check the submission
  const submissionStatus = await SubmissionService.checkSubmission(
    submissionId,
    headers
  );

  // Set the submission ID in the response headers
  res.setHeader("X-Submission-ID", submissionId);

  // Log the submission status
  console.log(
    `Submission status for ID ${submissionId}:`,
    JSON.stringify(submissionStatus)
  );

  res.status(StatusCodes.OK).json(submissionStatus);
});

/**
 * Handle question entry - Create or update question document
 * This ensures daily questions are saved to database regardless of user submissions
 * @route POST /api/v1/leetcode/handle-question
 * @access Public
 */
const handleQuestionEntry = asyncHandler(async (req, res) => {
  try {
    const { questionTitle, problemSlug, difficulty, topicTags, questionLink } =
      req.body;

    console.log("handleQuestionEntry called with:", {
      questionTitle,
      difficulty,
      topicTags,
    });

    // Validation
    if (!questionTitle) {
      res.status(StatusCodes.BAD_REQUEST);
      throw new Error("Question title is required");
    }

    // Check if question already exists
    let question = await Question.findOne({ title: questionTitle });

    if (question) {
      console.log(`Question already exists: ${questionTitle}`);
      return res.status(StatusCodes.OK).json({
        success: true,
        message: "Question already exists",
        question: {
          id: question._id,
          title: question.title,
          difficulty: question.difficulty,
          tags: question.tags,
          questionLink: question.questionLink,
          userCount: question.userList?.length || 0,
        },
        isNew: false,
      });
    }

    // Create question with tags from topicTags or fallback to basic tags
    let questionTags = [];
    if (topicTags && Array.isArray(topicTags) && topicTags.length > 0) {
      // Map LeetCode topic tags to our schema's allowed tags
      const allowedTags = [
        "array",
        "string",
        "linked list",
        "tree",
        "graph",
        "dynamic programming",
        "sorting",
        "searching",
        "hashing",
        "greedy",
        "backtracking",
        "math",
        "bit manipulation",
        "recursion",
        "binary search",
        "two pointers",
        "sliding window",
        "stack",
        "queue",
        "heap",
        "trie",
        "union find",
        "binary tree",
        "binary search tree",
        "depth-first search",
        "breadth-first search",
        "design",
        "simulation",
        "geometry",
        "brainteaser",
        "interactive",
        "database",
        "shell",
        "concurrency",
      ];

      questionTags = topicTags
        .map((tag) => {
          const tagName =
            typeof tag === "string"
              ? tag.toLowerCase()
              : tag.name?.toLowerCase() || "";
          // Map common LeetCode tags to our schema
          const tagMapping = {
            "hash table": "hashing",
            "divide and conquer": "recursion",
            "heap (priority queue)": "heap",
            matrix: "array",
            "prefix sum": "array",
            counting: "hashing",
            iterator: "design",
            "monotonic stack": "stack",
            "shortest path": "graph",
            "minimum spanning tree": "graph",
            "topological sort": "graph",
            "strongly connected component": "graph",
            "eulerian circuit": "graph",
            "bipartite graph": "graph",
            dfs: "depth-first search",
            bfs: "breadth-first search",
            bst: "binary search tree",
            "segment tree": "tree",
            "fenwick tree": "tree",
            "suffix array": "string",
            "string matching": "string",
            "rolling hash": "hashing",
            quickselect: "sorting",
            "merge sort": "sorting",
            "bucket sort": "sorting",
            "radix sort": "sorting",
            "counting sort": "sorting",
          };

          const mappedTag = tagMapping[tagName] || tagName;
          return allowedTags.includes(mappedTag) ? mappedTag : null;
        })
        .filter((tag) => tag !== null);

      // Remove duplicates
      questionTags = [...new Set(questionTags)];
    }

    // If no valid tags found, use basic tags based on difficulty
    if (questionTags.length === 0) {
      const defaultDifficulty = difficulty || "medium";
      questionTags =
        defaultDifficulty.toLowerCase() === "easy"
          ? ["array"]
          : defaultDifficulty.toLowerCase() === "medium"
            ? ["array", "dynamic programming"]
            : ["array", "dynamic programming", "graph"];
    }

    // Create the question document
    question = await Question.create({
      title: questionTitle,
      questionLink:
        questionLink ||
        (problemSlug ? `https://leetcode.com/problems/${problemSlug}/` : ""),
      difficulty: difficulty ? difficulty.toLowerCase() : "medium",
      tags: questionTags,
      userList: [],
    });

    console.log(
      `Created new question: ${questionTitle} with tags:`,
      questionTags
    );

    res.status(StatusCodes.CREATED).json({
      success: true,
      message: "Question created successfully",
      question: {
        id: question._id,
        title: question.title,
        difficulty: question.difficulty,
        tags: question.tags,
        questionLink: question.questionLink,
        userCount: 0,
      },
      isNew: true,
    });
  } catch (error) {
    console.error("Error in handleQuestionEntry:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Error handling question entry",
      error: error.message,
      stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
    });
  }
});

const afterSubmission = asyncHandler(async (req, res) => {
  try {
    //userid will be fetched from the cookie
    // questionTitle will be fetched from the request body
    const {
      userId,
      questionTitle,
      problemTitle, // Add problemTitle as alternative field name
      problemSlug,
      difficulty,
      code,
      language,
      approach,
      status,
      topicTags, // Add topicTags from the request body
      userId: requestUserId, // Add userId from request body as fallback
      isSheetQuestion, // Add flag to identify if this is from sheet pages
    } = req.body;

    // Use questionTitle or problemTitle (frontend sends problemTitle)
    const finalQuestionTitle = questionTitle || problemTitle;

    console.log("afterSubmission called with:", {
      questionTitle: finalQuestionTitle,
      userId: requestUserId,
      status,
      isSheetQuestion,
    });

    // Get user from request (auth middleware) or use userId from request body
    let finalUserId, user;
    if (req.user && req.user.id) {
      // Authenticated user
      finalUserId = req.user.id;
      user = await User.findById(finalUserId);
    } else if (requestUserId || userId) {
      // Fallback to userId from request body
      finalUserId = requestUserId || userId;
      user = await User.findById(finalUserId);
      console.log(`Using userId from request body: ${finalUserId}`);
    } else {
      res.status(StatusCodes.UNAUTHORIZED);
      throw new Error(
        "User authentication required or userId must be provided"
      );
    }

    if (!user) {
      res.status(StatusCodes.NOT_FOUND);
      throw new Error("User not found");
    }

    console.log(
      "Found user:",
      user.fullName,
      "Current XP:",
      user.xp,
      "Current streak:",
      user.streak
    );

    // Validate that we have a question title
    if (!finalQuestionTitle) {
      res.status(StatusCodes.BAD_REQUEST);
      throw new Error(
        "Question title is required (questionTitle or problemTitle)"
      );
    }

    // Only save accepted solutions
    if (status && status !== "Accepted") {
      res.status(StatusCodes.BAD_REQUEST);
      throw new Error("Only accepted solutions can be saved");
    }

    // Find or create the question document
    let question = await Question.findOne({ title: finalQuestionTitle });
    if (!question) {
      // Create question with tags from topicTags or fallback to basic tags
      let questionTags = [];
      if (topicTags && Array.isArray(topicTags) && topicTags.length > 0) {
        // Map LeetCode topic tags to our schema's allowed tags
        const allowedTags = [
          "array",
          "string",
          "linked list",
          "tree",
          "graph",
          "dynamic programming",
          "sorting",
          "searching",
          "hashing",
          "greedy",
          "backtracking",
          "math",
          "bit manipulation",
          "recursion",
          "binary search",
          "two pointers",
          "sliding window",
          "stack",
          "queue",
          "heap",
          "trie",
          "union find",
          "binary tree",
          "binary search tree",
          "depth-first search",
          "breadth-first search",
          "design",
          "simulation",
          "geometry",
          "brainteaser",
          "interactive",
          "database",
          "shell",
          "concurrency",
        ];

        questionTags = topicTags
          .map((tag) => {
            const tagName =
              typeof tag === "string"
                ? tag.toLowerCase()
                : tag.name?.toLowerCase() || "";
            // Map common LeetCode tags to our schema
            const tagMapping = {
              "hash table": "hashing",
              "divide and conquer": "recursion",
              "heap (priority queue)": "heap",
              matrix: "array",
              "prefix sum": "array",
              counting: "hashing",
              iterator: "design",
              "monotonic stack": "stack",
              "shortest path": "graph",
              "minimum spanning tree": "graph",
              "topological sort": "graph",
              "strongly connected component": "graph",
              "eulerian circuit": "graph",
              "bipartite graph": "graph",
              dfs: "depth-first search",
              bfs: "breadth-first search",
              bst: "binary search tree",
              "segment tree": "tree",
              "fenwick tree": "tree",
              "suffix array": "string",
              "string matching": "string",
              "rolling hash": "hashing",
              quickselect: "sorting",
              "merge sort": "sorting",
              "bucket sort": "sorting",
              "radix sort": "sorting",
              "counting sort": "sorting",
            };

            const mappedTag = tagMapping[tagName] || tagName;
            return allowedTags.includes(mappedTag) ? mappedTag : null;
          })
          .filter((tag) => tag !== null);

        // Remove duplicates
        questionTags = [...new Set(questionTags)];
      }

      // If no valid tags found, use basic tags based on difficulty
      if (questionTags.length === 0) {
        const defaultDifficulty = difficulty || "medium";
        questionTags =
          defaultDifficulty.toLowerCase() === "easy"
            ? ["array"]
            : defaultDifficulty.toLowerCase() === "medium"
              ? ["array", "dynamic programming"]
              : ["array", "dynamic programming", "graph"];
      }

      question = await Question.create({
        title: finalQuestionTitle,
        questionLink: problemSlug
          ? `https://leetcode.com/problems/${problemSlug}/`
          : "",
        difficulty: difficulty ? difficulty.toLowerCase() : "medium",
        tags: questionTags,
        userList: [],
      });

      console.log(
        `Created new question: ${finalQuestionTitle} with tags:`,
        questionTags
      );
    }

    // Check if solution already exists for this user and question
    const existingSolution = await Solution.findOne({
      user: finalUserId,
      question: question._id,
    });

    let solution;
    let isDuplicateCode = false;

    if (existingSolution) {
      // Update existing solution with new code, language, and approach
      try {
        existingSolution.code = code;
        existingSolution.language = language;
        existingSolution.approach = approach || "";
        existingSolution.updatedAt = new Date();

        solution = await existingSolution.save();
        console.log(
          `Updated existing solution for user ${finalUserId} and question ${question.title}`
        );
      } catch (updateError) {
        if (updateError.code === 11000) {
          // Duplicate code error when updating
          isDuplicateCode = true;
          solution = existingSolution; // Keep the existing solution
          console.log(
            `Duplicate code detected for user ${finalUserId} - keeping existing solution`
          );
        } else {
          throw updateError; // Re-throw if it's not a duplicate error
        }
      }
    } else {
      // Step 2(i): Create solution doc
      try {
        solution = await Solution.create({
          user: finalUserId,
          question: question._id,
          code,
          language,
          approach: approach || "",
        });
        console.log(
          `Created new solution for user ${finalUserId} and question ${question.title}`
        );
      } catch (createError) {
        if (createError.code === 11000) {
          // Duplicate code error - this means another user has the same solution
          isDuplicateCode = true;
          console.log(
            `Duplicate code detected for user ${finalUserId} - solution matches existing user's code`
          );

          // Don't create a solution, but still treat as "accepted" for user experience
          // We'll return success but with a warning message
          return res.status(StatusCodes.OK).json({
            success: true,
            message:
              "Solution accepted, but appears to be copied from another user. Please solve using a unique approach.",
            isDuplicate: true,
            warning:
              "This solution matches an existing submission. Try to solve with your own approach for better learning.",
            solution: null,
          });
        } else {
          throw createError; // Re-throw if it's not a duplicate error
        }
      }
    }

    // Update User.sol Map and Question.userList only for new solutions
    if (!existingSolution) {
      // Step 2(ii): Append user to question's userList if not already present
      if (!question.userList.includes(finalUserId)) {
        question.userList.push(finalUserId);
        await question.save();
      }

      // Step 2(ii-b): If this is a sheet question, also update SheetQuestion.userList
      if (isSheetQuestion) {
        try {
          // Find the sheet question by title
          let sheetQuestion = await SheetQuestion.findOne({
            title: finalQuestionTitle,
          });

          // Sheet question exists, add user to userList if not already present
          if (!sheetQuestion.userList.includes(finalUserId)) {
            sheetQuestion.userList.push(finalUserId);
            await sheetQuestion.save();
            console.log(
              `Added user ${finalUserId} to SheetQuestion userList for: ${finalQuestionTitle}`
            );
          } else {
            console.log(
              `User ${finalUserId} already in SheetQuestion userList for: ${finalQuestionTitle}`
            );
          }

          // } else {
          //   // Sheet question doesn't exist, create it

          //   const normalizedDifficulty = difficulty ?
          //     (difficulty.toLowerCase() === 'easy' ? 'Easy' :
          //      difficulty.toLowerCase() === 'medium' ? 'Medium' : 'Hard') : 'Medium';

          //   // Use topicTags if available, otherwise use basic tags
          //   let sheetTags = [];
          //   if (topicTags && Array.isArray(topicTags) && topicTags.length > 0) {
          //     const allowedSheetTags = [
          //       'array', 'string', 'linked list', 'tree', 'graph', 'dynamic programming',
          //       'sorting', 'searching', 'hashing', 'greedy', 'backtracking', 'math',
          //       'bit manipulation', 'recursion', 'binary search', 'two pointers',
          //       'sliding window', 'stack', 'queue', 'heap', 'trie', 'union find',
          //       'binary tree', 'binary search tree', 'depth-first search',
          //       'breadth-first search', 'design', 'simulation', 'geometry',
          //       'brainteaser', 'interactive', 'database', 'shell', 'concurrency'
          //     ];

          //     sheetTags = topicTags
          //       .map(tag => {
          //         const tagName = typeof tag === 'string' ? tag.toLowerCase() : tag.name?.toLowerCase() || '';
          //         return allowedSheetTags.includes(tagName) ? tagName : null;
          //       })
          //       .filter(tag => tag !== null);

          //     // Remove duplicates
          //     sheetTags = [...new Set(sheetTags)];
          //   }

          //   // If no valid tags found, use basic tags based on difficulty
          //   if (sheetTags.length === 0) {
          //     sheetTags = normalizedDifficulty === 'Easy' ? ['array'] :
          //                normalizedDifficulty === 'Medium' ? ['array', 'string'] :
          //                ['array', 'dynamic programming'];
          //   }

          //   sheetQuestion = await SheetQuestion.create({
          //     title: finalQuestionTitle,
          //     questionLink: problemSlug ? `https://leetcode.com/problems/${problemSlug}/` : '',
          //     difficulty: normalizedDifficulty,
          //     tags: sheetTags,
          //     userList: [finalUserId],
          //     topic: 'Other' // Default topic, can be updated later
          //   });

          //   console.log(`Created new SheetQuestion and added user ${finalUserId}: ${finalQuestionTitle}`);
          // }
        } catch (sheetError) {
          // Log error but don't fail the entire request
          console.error(
            "Error updating SheetQuestion (solution still saved):",
            sheetError.message
          );
        }
      }

      // Step 2(iii): Update user's sol map
      const difficultyKey = difficulty ? difficulty.toLowerCase() : "medium";
      if (!user.sol.get(difficultyKey)) {
        user.sol.set(difficultyKey, []);
      }

      const currentSolutions = user.sol.get(difficultyKey);
      currentSolutions.push(solution._id);
      user.sol.set(difficultyKey, currentSolutions);

      // Step 2(iv): If this is a sheet question, also update sheetQuestionSol
      if (isSheetQuestion) {
        if (!user.sheetQuestionSol) {
          user.sheetQuestionSol = new Map();
        }

        if (!user.sheetQuestionSol.get(difficultyKey)) {
          user.sheetQuestionSol.set(difficultyKey, []);
        }

        const currentSheetSolutions = user.sheetQuestionSol.get(difficultyKey);
        currentSheetSolutions.push(solution._id);
        user.sheetQuestionSol.set(difficultyKey, currentSheetSolutions);

        console.log(
          `Updated sheetQuestionSol for user ${finalUserId} with difficulty ${difficultyKey}`
        );
      }
      user.sol.set(difficultyKey, currentSolutions);

      // Save user.sol update first (this is required functionality)
      await user.save();

      // Try to update XP and streak separately (bonus feature, won't break if it fails)
      try {
        // Reload user from database to get fresh instance
        const freshUser = await User.findById(finalUserId);

        const today = new Date();
        today.setHours(0, 0, 0, 0); // Set to start of day

        // Initialize XP and streak if they don't exist
        if (!freshUser.xp || freshUser.xp === null) {
          freshUser.xp = 0;
        }
        if (!freshUser.streak || freshUser.streak === null) {
          freshUser.streak = 0;
        }

        const lastSolvedDate = freshUser.lastQuestionSolvedDate
          ? new Date(freshUser.lastQuestionSolvedDate)
          : null;
        if (lastSolvedDate) {
          lastSolvedDate.setHours(0, 0, 0, 0);
        }

        // Add 10 XP for solving a question
        // if the user has again solved same question, don't add XP

        freshUser.xp = (freshUser.xp || 0) + 10;

        // Update streak logic
        if (!lastSolvedDate) {
          // First question ever solved
          freshUser.streak = 1;
        } else {
          const daysDifference =
            (today - lastSolvedDate) / (1000 * 60 * 60 * 24);

          if (daysDifference === 1) {
            // Consecutive day - increment streak
            freshUser.streak = (freshUser.streak || 0) + 1;
          } else if (daysDifference === 0) {
            // Same day - don't change streak
            // Streak remains the same
          } else {
            // More than 1 day gap - reset streak to 1 (since they solved today)
            freshUser.streak = 1;
          }
        }

        freshUser.lastQuestionSolvedDate = new Date();

        console.log(
          `About to save user XP: ${freshUser.xp}, Streak: ${freshUser.streak}`
        );

        // Save user with XP and streak updates
        await freshUser.save();

        console.log(
          `Successfully updated user XP: ${freshUser.xp}, Streak: ${freshUser.streak} for user ${finalUserId}`
        );

        // Update the user object with fresh data for response
        user.xp = freshUser.xp;
        user.streak = freshUser.streak;
        user.lastQuestionSolvedDate = freshUser.lastQuestionSolvedDate;
      } catch (xpUpdateError) {
        console.error(
          "Error updating user XP/streak (solution still saved):",
          xpUpdateError.message
        );
        console.error("XP Update Error Details:", xpUpdateError);
        // Continue with the response - solution is still saved
      }
    }

    res.status(StatusCodes.CREATED).json({
      success: true,
      message: isDuplicateCode
        ? "Solution accepted, but appears to be copied. Please solve using a unique approach."
        : existingSolution
          ? "Solution updated successfully"
          : "Solution saved successfully",
      isDuplicate: isDuplicateCode,
      warning: isDuplicateCode
        ? "This solution matches an existing submission. Try to solve with your own approach for better learning."
        : null,
      solution: solution
        ? {
            id: solution._id,
            questionTitle: finalQuestionTitle,
            language: solution.language,
            createdAt: solution.createdAt,
            updatedAt: solution.updatedAt,
            isUpdate: !!existingSolution,
            userXP: user.xp || 0,
            userStreak: user.streak || 0,
          }
        : null,
      // Include updated user data for frontend to use
      updatedUser: {
        id: user._id,
        fullName: user.fullName,
        username: user.username,
        email: user.email,
        xp: user.xp || 0,
        streak: user.streak || 0,
        lastQuestionSolvedDate: user.lastQuestionSolvedDate,
      },
    });
  } catch (error) {
    console.error("Error in afterSubmission:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Error saving solution",
      error: error.message,
      stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
    });
  }
});
/**
 * Check run code result
 * @route GET /api/v1/leetcode/run-code/check/:interpretId
 * @access Public
 */
const checkRunResult = asyncHandler(async (req, res) => {
  const { interpretId } = req.params;

  // Extract headers from request
  const headers = {
    cookie: req.headers.cookie,
    csrfToken: req.headers["x-csrftoken"],
    userAgent: req.headers["user-agent"],
    origin: req.headers.origin,
    referer: req.headers.referer,
  };

  // Validate headers
  const { error: headersError } = headersSchema.validate(headers);
  if (headersError) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid headers: ${headersError.message}`);
  }

  // Check the run result
  const runResult = await SubmissionService.checkRunResult(
    interpretId,
    headers
  );

  res.status(StatusCodes.OK).json(runResult);
});

/**
 * Get latest submission ID
 * @route GET /api/v1/leetcode/submissions/latest
 * @access Public
 */
const getLatestSubmissionId = asyncHandler(async (req, res) => {
  // This is a placeholder endpoint that would normally fetch the latest submission ID
  // Since we don't have a way to get this directly, we'll return a 404
  res.status(StatusCodes.NOT_FOUND);
  throw new Error("Not implemented");
});

export {
  runCode,
  submitSolution,
  checkSubmission,
  handleQuestionEntry,
  afterSubmission,
  checkRunResult,
  getLatestSubmissionId,
};
