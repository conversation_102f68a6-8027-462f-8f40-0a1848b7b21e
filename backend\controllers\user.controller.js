import User from "../models/user.model.js";
import Question from "../models/question.model.js";
import AppError from "../utils/error.util.js";
import sendEmail from "../utils/sendEmail.js";
import cloudinary from "cloudinary";
import fs from "fs/promises";
import crypto from "crypto";
import moment from "moment-timezone";
const cookieOptions = {
  // Use secure cookies in production AND in development with Chrome
  secure: true,
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  httpOnly: true,
  sameSite: "none", // 'none' works for cross-domain in both environments with secure:true
  // Use domain when in production
  domain:
    process.env.NODE_ENV === "production"
      ? process.env.COOKIE_DOMAIN
      : undefined,
};

const register = async (req, res, next) => {
  let { fullName, username, email, password, profile } = req.body;

  if (!fullName || !email || !password) {
    return next(new AppError("Please fill in all fields", 400));
  }

  const userExist = await User.findOne({ email });
  if (userExist) {
    return next(new AppError("User already exists", 400));
  }

  // Generate username from email if not provided
  if (!username) {
    username = email.split("@")[0] + "_" + Date.now().toString().slice(-4);
  }

  const user = await User.create({
    fullName,
    username,
    email,
    password,
    sol: new Map(),
    sheetQuestionSol: new Map(),
    // avatar: {
    //     public_id: email,
    //     secure_id: '123'
    // },
  });

  if (!user) {
    return next(
      new AppError("User registeration failed, Please try again later", 400)
    );
  }

  // File related work
  // if(req.file){
  //     try {
  //         const result = await cloudinary.v2.uploader.upload(req.file.path,{
  //             folder: 'user',
  //             width: 250,
  //             height: 250,
  //             gravity: 'face',
  //             crop: 'fill'
  //         })

  //         if(result){
  //             user.avatar.public_id = result.public_id
  //             user.avatar.secure_id = result.secure_url

  //             // remove file from server

  //             fs.rm(`uploads/${req.file.filename}`)
  //         }
  //     } catch (error) {
  //         return next(new AppError(error.message || 'Problem with file upload', 500))
  //     }
  // }
  // await user.save()

  user.password = undefined; // To not send the password in the response

  // Send welcome email
  try {
    const welcomeSubject =
      "🎉 Welcome to BIG(O) - Let's Start Your Coding Journey!";
    const welcomeMessage = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to BIG(O)!</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>') repeat;
            opacity: 0.3;
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .logo {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ffffff, #e0e6ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 0;
            font-weight: 300;
        }
        
        .content {
            padding: 40px 30px;
            background: #ffffff;
        }
        
        .welcome-icon {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .celebration-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .greeting {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.8;
            text-align: center;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            border: 2px solid #e0e6ff;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        
        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin: 30px 0;
            color: white;
        }
        
        .cta-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .cta-button {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white !important;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 15px 10px;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .cta-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .stats-section {
            background: #f8f9ff;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            text-align: center;
        }
        
        .stats-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            display: block;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .footer {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
            padding: 35px 30px;
            text-align: center;
            border-top: 1px solid #e0e6ff;
        }
        
        .footer-text {
            color: #666;
            font-size: 16px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .social-links {
            margin: 25px 0;
        }
        
        .social-link {
            display: inline-block;
            margin: 0 15px;
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }
        
        .social-link:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }
        
        .company-info {
            color: #999;
            font-size: 13px;
            line-height: 1.6;
            margin-top: 25px;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 12px;
            }
            
            .header, .content, .footer {
                padding: 25px 20px;
            }
            
            .logo {
                font-size: 30px;
            }
            
            .greeting {
                font-size: 24px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .cta-button {
                display: block;
                margin: 10px 0;
            }
            
            .social-link {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="header-content">
                <div class="logo">BIG(O)</div>
                <div class="header-subtitle">Master Data Structures & Algorithms</div>
            </div>
        </div>
        
        <div class="content">
            <div class="welcome-icon">
                <div class="celebration-icon">🎉</div>
            </div>
            
            <div class="greeting">Welcome, ${user.fullName || user.username}!</div>
            
            <div class="message">
                We're thrilled to have you join the BIG(O) community! You've just taken the first step towards mastering data structures, algorithms, and competitive programming. Get ready for an amazing journey of learning and growth! 🚀
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">📚</div>
                    <div class="feature-title">Curated Problems</div>
                    <div class="feature-desc">Access hundreds of carefully selected problems from arrays to advanced topics</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🏆</div>
                    <div class="feature-title">Track Progress</div>
                    <div class="feature-desc">Monitor your growth with detailed analytics and achievement badges</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <div class="feature-title">Community</div>
                    <div class="feature-desc">Connect with fellow programmers and learn together</div>
                </div>
            </div>
            
            <div class="stats-section">
                <div class="stats-title">Join thousands of developers</div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Problems</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">Topics</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">Support</span>
                    </div>
                </div>
            </div>
            
            <div class="cta-section">
                <div class="cta-title">Ready to start your coding journey?</div>
                <div>
                    <a href="${process.env.FRONTEND_URL}/signin" class="cta-button">🚀 Start Solving</a>
                    <a href="${process.env.FRONTEND_URL}/sheets" class="cta-button">📋 View Problem Sets</a>
                </div>
            </div>
            
            <div class="message">
                <strong>Pro Tips for Success:</strong><br>
                • Start with easy problems and gradually increase difficulty<br>
                • Practice consistently - even 30 minutes daily makes a difference<br>
                • Don't hesitate to check solutions if you're stuck<br>
                • Join our community discussions for tips and tricks<br>
                • Set weekly goals and track your progress
            </div>
        </div>
        
        <div class="footer">
            <div class="footer-text">
                <strong>Welcome to the family! Let's code together! 💻</strong>
            </div>
            
            <div class="social-links">
                <a href="#" class="social-link">📧 Support</a>
                <a href="#" class="social-link">🌐 Website</a>
                <a href="#" class="social-link">👥 Community</a>
                <a href="#" class="social-link">📖 Resources</a>
            </div>
            
            <div class="company-info">
                <strong>BIG(O)</strong> - Your Gateway to Competitive Programming Excellence<br>
                Making algorithms and data structures accessible and fun for everyone<br><br>
                
                <em>You're receiving this email because you just created an account with BIG(O).<br>
                We're excited to be part of your programming journey!</em>
            </div>
        </div>
    </div>
</body>
</html>
        `;

    await sendEmail(user.email, welcomeSubject, welcomeMessage);
  } catch (emailError) {
    // Don't fail registration if email fails
    console.error("Welcome email failed:", emailError.message);
  }

  const token = await user.getJwtToken();
  res.cookie("token", token, cookieOptions);

  return res.status(201).json({
    success: true,
    message: "User registered successfully",
    user,
    token, // Include token in response payload for cross-domain environments
  });
};

const login = async (req, res, next) => {
  const { email, password } = req.body;

  try {
    if (!email || !password) {
      return next(new AppError("Please fill in all fields", 400));
    }

    const user = await User.findOne({ email }).select("+password");

    if (!user || !(await user.comparePassword(password))) {
      return next(new AppError("Invalid email or password", 400));
    }

    const token = await user.getJwtToken();
    res.cookie("token", token, cookieOptions);

    user.password = undefined;
    return res.status(200).json({
      success: true,
      message: "User logged in successfully",
      user,
      token, // Include token in response payload for cross-domain environments
    });
  } catch (error) {
    return next(new AppError(error.message, 500));
  }
};

const logout = async (req, res, next) => {
  // Clear the token cookie with same settings as when it was set
  res.cookie("token", "", {
    secure: true,
    maxAge: 0,
    httpOnly: true,
    sameSite: "none",
    domain:
      process.env.NODE_ENV === "production"
        ? process.env.COOKIE_DOMAIN
        : undefined,
  });

  res.status(200).json({
    success: true,
    message: "User logged out successfully",
  });
};

const profile = async (req, res, next) => {
  try {
    const userID = req.user.id;
    const user = await User.findById(userID);

    if (!user) {
      return next(new AppError("User not found", 404));
    }

    // Check and update daily streak automatically when user visits the site
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to start of day

      // Initialize streak if it doesn't exist
      if (user.streak === undefined || user.streak === null) {
        user.streak = 0;
      }

      const lastSolvedDate = user.lastQuestionSolvedDate
        ? new Date(user.lastQuestionSolvedDate)
        : null;

      if (lastSolvedDate) {
        lastSolvedDate.setHours(0, 0, 0, 0);

        const daysDifference = (today - lastSolvedDate) / (1000 * 60 * 60 * 24);

        // If more than 2 days have passed since last question was solved, reset streak to 0
        // This gives users the full current day to solve before streak is reset
        if (daysDifference > 2) {
          console.log(
            `Resetting streak for user ${userID}. Days since last solve: ${daysDifference}`
          );
          user.streak = 0;
          await user.save();
        }
      }
    } catch (streakError) {
      console.error(
        "Error checking daily streak on profile fetch:",
        streakError.message
      );
      // Continue with profile fetch even if streak check fails
    }

    return res.status(200).json({
      success: true,
      message: "User profile fetched successfully",
      user,
    });
  } catch (error) {
    return next(new AppError("Failed to fetch User profile", 500));
  }
};

const forgotPassword = async (req, res, next) => {
  const { email } = req.body;

  if (!email) {
    return next(new AppError("Please enter your email", 400));
  }

  const user = await User.findOne({ email });
  if (!user) {
    return next(new AppError("User not found", 404));
  }

  const resetToken = await user.getResetPasswordToken();
  await user.save(); // This will save the forgorPasswordToken and forgorPasswordExpire in the database

  // Use frontend URL for the reset password link
  const resetPasswordUrl = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;

  // Beautiful HTML email template
  const subject = "🔐 Reset Your BIG(O) Password";
  const message = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password - BIG(O)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>') repeat;
            opacity: 0.3;
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .logo {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ffffff, #e0e6ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 0;
            font-weight: 300;
        }
        
        .content {
            padding: 40px 30px;
            background: #ffffff;
        }
        
        .icon-container {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .lock-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
            text-align: center;
        }
        
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            text-decoration: none;
            padding: 18px 36px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            border: none;
            cursor: pointer;
        }
        
        .reset-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }
        
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .security-notice {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
            border-left: 4px solid #667eea;
            padding: 25px;
            margin: 30px 0;
            border-radius: 0 12px 12px 0;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
        }
        
        .security-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        
        .security-text {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .security-text li {
            margin-bottom: 8px;
            list-style: none;
            position: relative;
            padding-left: 20px;
        }
        
        .security-text li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }
        
        .alternative-link {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border: 2px dashed #dee2e6;
        }
        
        .alternative-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            font-weight: 500;
        }
        
        .link-text {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #667eea;
            word-break: break-all;
            background: #ffffff;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .expiry-notice {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #f0b90b;
            color: #856404;
            padding: 20px;
            border-radius: 12px;
            margin: 25px 0;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(240, 185, 11, 0.1);
        }
        
        .footer {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
            padding: 35px 30px;
            text-align: center;
            border-top: 1px solid #e0e6ff;
        }
        
        .footer-text {
            color: #666;
            font-size: 16px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .company-info {
            color: #999;
            font-size: 13px;
            line-height: 1.6;
            margin-top: 25px;
        }
        
        .social-links {
            margin: 25px 0;
        }
        
        .social-link {
            display: inline-block;
            margin: 0 15px;
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }
        
        .social-link:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }
        
        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #dee2e6, transparent);
            margin: 30px 0;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 12px;
            }
            
            .header, .content, .footer {
                padding: 25px 20px;
            }
            
            .logo {
                font-size: 30px;
            }
            
            .greeting {
                font-size: 20px;
            }
            
            .reset-button {
                display: block;
                text-align: center;
                width: 100%;
                padding: 20px;
                font-size: 18px;
            }
            
            .lock-icon {
                width: 60px;
                height: 60px;
                font-size: 28px;
            }
            
            .social-link {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="header-content">
                <div class="logo">BIG(O)</div>
                <div class="header-subtitle">Master Data Structures & Algorithms</div>
            </div>
        </div>
        
        <div class="content">
            <div class="icon-container">
                <div class="lock-icon">🔐</div>
            </div>
            
            <div class="greeting">Hello ${user.fullName || user.username}! 👋</div>
            
            <div class="message">
                We received a request to reset your password for your BIG(O) account. Don't worry, we've got you covered! Click the button below to create a new password and get back to solving amazing algorithms.
            </div>
            
            <div class="button-container">
                <a href="${resetPasswordUrl}" class="reset-button">
                    Reset Your Password
                </a>
            </div>
            
            <div class="expiry-notice">
                ⏰ <strong>Important:</strong> This reset link will expire in <strong>1 hour</strong> for your security.
            </div>
            
            <div class="security-notice">
                <div class="security-title">🛡️ Security Guidelines</div>
                <ul class="security-text">
                    <li>This link can only be used once</li>
                    <li>If you didn't request this reset, please ignore this email</li>
                    <li>Never share this link with anyone</li>
                    <li>Always use a strong, unique password</li>
                    <li>Consider using a password manager</li>
                </ul>
            </div>
            
            <div class="alternative-link">
                <div class="alternative-text">
                    <strong>🔗 Can't click the button?</strong> Copy and paste this link into your browser:
                </div>
                <div class="link-text">${resetPasswordUrl}</div>
            </div>
            
            <div class="divider"></div>
            
            <div class="message">
                If you have any questions or need assistance, our support team is here to help. Keep coding and stay awesome! 🚀
            </div>
        </div>
        
        <div class="footer">
            <div class="footer-text">
                <strong>Happy Coding! 💻</strong>
            </div>
            
            <div class="social-links">
                <a href="#" class="social-link">📧 Support</a>
                <a href="#" class="social-link">🌐 Website</a>
                <a href="#" class="social-link">👥 Community</a>
            </div>
            
            <div class="company-info">
                <strong>BIG(O)</strong> - Your Gateway to Competitive Programming Excellence<br>
                Making algorithms and data structures accessible and fun for everyone<br><br>
                
                <em>This email was sent because you requested a password reset for your BIG(O) account.<br>
                If you didn't make this request, please ignore this email and your account will remain secure.</em>
            </div>
        </div>
    </div>
</body>
</html>
    `;

  try {
    // await sendEmail(email, subject, message)   //smtp---> env
    await sendEmail(user.email, subject, message);
    return res.status(200).json({
      success: true,
      message: `Email sent to ${email} successfully`,
    });
  } catch (error) {
    user.forgotPasswordToken = undefined;
    user.forgotPasswordExpire = undefined;

    await user.save();

    return next(new AppError(error.message || "Email could not be sent", 500));
  }
};

const resetPassword = async (req, res, next) => {
  const { token } = req.params;
  const { password } = req.body;

  if (!token || !password) {
    return next(new AppError("Please provide a token and new password", 400));
  }

  const resetPasswordToken = crypto
    .createHash("sha256")
    .update(token)
    .digest("hex");

  const user = await User.findOne({
    forgotPasswordToken: resetPasswordToken,
    forgotPasswordExpire: { $gt: Date.now() },
  });

  if (!user) {
    return next(
      new AppError("Invalid token or token has expired. Please try again", 400)
    );
  }

  user.password = password;
  user.forgotPasswordToken = undefined;
  user.forgotPasswordExpire = undefined;
  await user.save();

  return res.status(200).json({
    success: true,
    message: "Password updated successfully",
  });
};

const changePassword = async (req, res, next) => {
  const { oldPassword, newPassword } = req.body;
  const userID = req.user.id;

  if (!oldPassword || !newPassword) {
    return next(new AppError("Please enter your old and new password", 400));
  }

  const user = await User.findById(userID).select("+password");
  if (!user) {
    return next(new AppError("User does not exists", 400));
  }

  const isMatch = await user.comparePassword(oldPassword);
  if (!isMatch) {
    return next(new AppError("Old password is incorrect", 400));
  }

  user.password = newPassword;
  await user.save();

  user.password = undefined;

  return res.status(200).json({
    success: true,
    message: "Password updated successfully",
  });
};

const updateUser = async (req, res, next) => {
  const { fullName } = req.body;
  const { id } = req.params;

  const user = await User.findById(id);
  if (!user) {
    return next(new AppError("User does not exists", 400));
  }
  if (fullName) {
    user.fullName = fullName;
  }

  if (req.file) {
    await cloudinary.v2.uploader.destroy(user.avatar.public_id);
    try {
      const result = await cloudinary.v2.uploader.upload(req.file.path, {
        folder: "lms",
        width: 250,
        height: 250,
        gravity: "face",
        crop: "fill",
      });

      if (result) {
        user.avatar.secure_id = result.secure_url;
        user.avatar.public_id = result.public_id;

        // remove file from server
        fs.rm(`uploads/${req.file.filename}`);
      }
    } catch (error) {
      return next(
        new AppError(error.message || "Problem with file upload", 500)
      );
    }
  }
  await user.save();

  return res.status(200).json({
    success: true,
    message: "User updated successfully",
  });
};

const getUserStats = async (req, res, next) => {
  try {
    const userId = req.user?.id || req.params.userId;

    console.log("getUserStats called with userId:", userId);
    console.log("req.user:", req.user);

    if (!userId) {
      console.log("No userId found, returning default stats");
      // Return default stats if no user ID
      const stats = {
        problemsSolved: 0,
        successRate: "100%",
        rank: "Beginner",
      };

      return res.status(200).json({
        success: true,
        message: "Default user statistics (no user ID)",
        stats,
      });
    }

    const user = await User.findById(userId);

    if (!user) {
      console.log("User not found, returning default stats");
      // Return default stats if user not found instead of error
      const stats = {
        problemsSolved: 0,
        successRate: "100%",
        rank: "Beginner",
      };

      return res.status(200).json({
        success: true,
        message: "Default user statistics (user not found)",
        stats,
      });
    }

    console.log("User found:", user.fullName, "Sol data:", user.sol);

    // Count total problems solved by the user
    let problemsSolved = 0;
    if (user.sol && user.sol instanceof Map) {
      // Count unique problems (Map keys are problem identifiers)
      problemsSolved = user.sol.size;
    } else if (user.sol && typeof user.sol === "object") {
      // In case sol is stored as a regular object
      problemsSolved = Object.keys(user.sol).length;
    }

    console.log("Problems solved count:", problemsSolved);

    // For now, set success rate to 100% and rank to Beginner as requested
    const stats = {
      problemsSolved,
      successRate: "100%",
      rank: "Beginner",
    };

    return res.status(200).json({
      success: true,
      message: "User statistics fetched successfully",
      stats,
    });
  } catch (error) {
    console.error("Error in getUserStats:", error);

    // Return default stats instead of error
    const stats = {
      problemsSolved: 0,
      successRate: "100%",
      rank: "Beginner",
    };

    return res.status(200).json({
      success: true,
      message: "Default user statistics (error occurred)",
      stats,
    });
  }
};

const getUserStatsByUserId = async (req, res, next) => {
  try {
    const { userId } = req.params;

    console.log("getUserStatsByUserId called with userId:", userId);

    if (!userId) {
      console.log("No userId provided, returning default stats");
      return res.status(200).json({
        success: true,
        message: "Default user statistics (no user ID provided)",
        stats: {
          problemsSolved: 0,
          successRate: "100%",
          rank: "Beginner",
        },
      });
    }

    // Count questions where the user ID is in the userList array
    const problemsSolved = await Question.countDocuments({
      userList: { $in: [userId] },
    });

    console.log("Problems solved count for user", userId, ":", problemsSolved);

    const stats = {
      problemsSolved,
      successRate: "100%",
      rank: "Beginner",
    };

    return res.status(200).json({
      success: true,
      message: "User statistics fetched successfully",
      stats,
    });
  } catch (error) {
    console.error("Error in getUserStatsByUserId:", error);

    // Return default stats instead of error
    const stats = {
      problemsSolved: 0,
      successRate: "100%",
      rank: "Beginner",
    };

    return res.status(200).json({
      success: true,
      message: "Default user statistics (error occurred)",
      stats,
    });
  }
};

const testAuth = async (req, res, next) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Not authenticated",
      });
    }

    const user = await User.findById(userId);
    return res.status(200).json({
      success: true,
      message: "User is authenticated",
      user: {
        id: user._id,
        fullName: user.fullName,
        username: user.username,
        email: user.email,
      },
    });
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: "Authentication error",
      error: error.message,
    });
  }
};

const updateUserXPAndStreak = async (req, res, next) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return next(new AppError("User ID is required", 400));
    }

    const user = await User.findById(userId);
    if (!user) {
      return next(new AppError("User not found", 404));
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day

    const lastSolvedDate = user.lastQuestionSolvedDate
      ? new Date(user.lastQuestionSolvedDate)
      : null;
    if (lastSolvedDate) {
      lastSolvedDate.setHours(0, 0, 0, 0);
    }

    // Add 10 XP for solving a question
    user.xp += 10;

    // Update streak logic
    if (!lastSolvedDate) {
      // First question ever solved
      user.streak = 1;
    } else {
      const daysDifference = (today - lastSolvedDate) / (1000 * 60 * 60 * 24);

      if (daysDifference === 1) {
        // Consecutive day - increment streak
        user.streak += 1;
      } else if (daysDifference === 0) {
        // Same day - don't change streak
        // Streak remains the same
      } else {
        // More than 1 day gap - reset streak
        user.streak = 0;
      }
    }

    user.lastQuestionSolvedDate = new Date();
    await user.save();

    return res.status(200).json({
      success: true,
      message: "User XP and streak updated successfully",
      user: {
        id: user._id,
        xp: user.xp,
        streak: user.streak,
        lastQuestionSolvedDate: user.lastQuestionSolvedDate,
      },
    });
  } catch (error) {
    return next(
      new AppError(error.message || "Failed to update user XP and streak", 500)
    );
  }
};

const getGlobalLeaderboard = async (req, res, next) => {
  try {
    // Get all users sorted by XP (descending), then by streak (descending)
    const users = await User.find({}, "fullName username xp streak")
      .sort({ xp: -1, streak: -1 })
      .limit(100); // Limit to top 100 users

    const leaderboard = users.map((user, index) => ({
      position: index + 1,
      username: user.username || user.fullName,
      fullName: user.fullName,
      xp: user.xp || 0,
      streak: user.streak || 0,
    }));

    return res.status(200).json({
      success: true,
      message: "Global leaderboard fetched successfully",
      leaderboard,
    });
  } catch (error) {
    return next(
      new AppError(error.message || "Failed to fetch global leaderboard", 500)
    );
  }
};

const calculateUserXPFromSolutions = async (req, res, next) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return next(new AppError("User ID is required", 400));
    }

    // Count questions where the user ID is in the userList array
    const problemsSolved = await Question.countDocuments({
      userList: { $in: [userId] },
    });

    // Calculate XP (10 XP per question solved)
    const calculatedXP = problemsSolved * 10;

    // Update user's XP
    const user = await User.findByIdAndUpdate(
      userId,
      { xp: calculatedXP },
      { new: true }
    );

    if (!user) {
      return next(new AppError("User not found", 404));
    }

    return res.status(200).json({
      success: true,
      message: "User XP calculated and updated successfully",
      data: {
        userId,
        problemsSolved,
        xp: calculatedXP,
        username: user.username || user.fullName,
      },
    });
  } catch (error) {
    return next(
      new AppError(error.message || "Failed to calculate user XP", 500)
    );
  }
};

/**
 * Check and update daily streak for authenticated user
 * This endpoint is called when user visits the website to ensure streak is up-to-date
 */
const checkAndUpdateDailyStreak = async (req, res, next) => {
  try {
    const { userId } = req.body; // Get user ID from authenticated request
    const user = await User.findById(userId);

    if (!user) {
      return next(new AppError("User not found", 404));
    }
    // Initialize streak and XP if they don't exist
    if (user.streak === undefined || user.streak === null) {
      user.streak = 0;
    }
    if (user.xp === undefined || user.xp === null) {
      user.xp = 0;
    }
    const lastSolvedDate = user.lastQuestionSolvedDate
      ? new Date(user.lastQuestionSolvedDate)
      : null;
    console.log(
      `Checking daily streak for user ${userId}. Last solved date: ${lastSolvedDate}`
    );
    const nowIST = moment().tz("Asia/Kolkata");
    const today530 = nowIST
      .clone()
      .startOf("day")
      .add(5, "hours")
      .add(30, "minutes");

    // Determine the current 5:30 AM window
    let windowStart;
    if (nowIST.isBefore(today530)) {
      windowStart = today530.clone().subtract(1, "day");
    } else {
      windowStart = today530;
    }

    // Calculate two previous windows
    const prevWindowStart = windowStart.clone().subtract(1, "day");
    const prevPrevWindowStart = windowStart.clone().subtract(2, "days");
    console.log(
      `Current window start: ${windowStart.format()}, Previous window start: ${prevWindowStart.format()}, Previous previous window start: ${prevPrevWindowStart.format()}`
    );
    const lastSolvedIST = moment(lastSolvedDate).tz("Asia/Kolkata");
    let streakUpdated = false;
    if (lastSolvedIST.isBefore(prevWindowStart)) {
      user.streak = 0;
      streakUpdated = true;
      console.log("streak updated to 0 for user", userId);
      await user.save();
    }

    // const today = new Date().toLocaleString("en-IN", {
    //   timeZone: "Asia/Kolkata",
    // });
    // today.setHours(0, 0, 0, 0); // Set to start of day

    // let streakUpdated = false;

    // if (lastSolvedDate) {
    //   lastSolvedDate.setHours(0, 0, 0, 0);

    //   const daysDifference = (today - lastSolvedDate) / (1000 * 60 * 60 * 24);

    //   // If more than 2 days have passed since last question was solved, reset streak to 0
    //   // This gives users the full current day to solve before streak is reset
    //   if (daysDifference >= 2 && user.streak > 0) {
    //     console.log(
    //       `Auto-resetting streak for user ${userId}. Days since last solve: ${daysDifference}, Previous streak: ${user.streak}`
    //     );
    //     user.streak = 0;
    //     streakUpdated = true;
    //   }
    // }

    // // Only save if streak was updated
    // if (streakUpdated) {
    //   await user.save();
    // }

    return res.status(200).json({
      success: true,
      message: streakUpdated
        ? "Daily streak checked and updated"
        : "Daily streak checked - no update needed",
      user: {
        id: userId,
        fullName: user.fullName,
        username: user.username,
        email: user.email,
        xp: user.xp,
        streak: user.streak,
        lastQuestionSolvedDate: user.lastQuestionSolvedDate,
      },
      streakUpdated,
    });
  } catch (error) {
    console.error("Error checking daily streak:", error);
    return next(
      new AppError(error.message || "Failed to check daily streak", 500)
    );
  }
};

export {
  register,
  login,
  logout,
  profile,
  forgotPassword,
  resetPassword,
  changePassword,
  updateUser,
  getUserStats,
  testAuth,
  getUserStatsByUserId,
  updateUserXPAndStreak,
  getGlobalLeaderboard,
  calculateUserXPFromSolutions,
  checkAndUpdateDailyStreak,
};
