import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import Editor from '@monaco-editor/react';
import { Sun, Moon, Plus, Minus } from 'lucide-react';

const CodeEditorForm = ({ onSubmit, isLoading, user, challengeData }) => {
  // Generate a unique key for localStorage based on the problem
  const storageKey = `codeEditor_${challengeData?.questionTitle || 'daily'}_${challengeData?.date || 'current'}`;
  
  // Function to load saved form data from localStorage
  const loadSavedFormData = () => {
    try {
      const saved = localStorage.getItem(storageKey);
      return saved ? JSON.parse(saved) : null;
    } catch (error) {
      console.error('Error loading saved form data:', error);
      return null;
    }
  };

  // Function to save form data to localStorage
  const saveFormData = (data) => {
    try {
      localStorage.setItem(storageKey, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving form data:', error);
    }
  };

  // Initialize state with saved data or defaults
  const savedData = loadSavedFormData();
  const [username, setUsername] = useState(savedData?.username || '');
  const [code, setCode] = useState(savedData?.code || '// Loading code template...');
  const [approach, setApproach] = useState(savedData?.approach || 'Approach:\n- \n\nTime Complexity: O()\nSpace Complexity: O()');
  const [language, setLanguage] = useState(savedData?.language || 'python3');
  const [error, setError] = useState('');
  const [theme, setTheme] = useState(savedData?.theme || 'vs-dark'); // 'vs-dark' or 'light'
  const [fontSize, setFontSize] = useState(savedData?.fontSize || 14);
  const [problemDetails, setProblemDetails] = useState(null);
  const [loadingProblem, setLoadingProblem] = useState(false);
  const [hasLoadedTemplate, setHasLoadedTemplate] = useState(savedData?.hasLoadedTemplate || false);

  // Save form data whenever state changes
  useEffect(() => {
    const formData = {
      username,
      code,
      approach,
      language,
      theme,
      fontSize,
      hasLoadedTemplate
    };
    saveFormData(formData);
  }, [username, code, approach, language, theme, fontSize, hasLoadedTemplate, storageKey]);

  // Auto-fill user's name when component mounts (only if not already saved)
  useEffect(() => {
    if (user?.fullName && !savedData?.username) {
      setUsername(user.fullName);
    }
  }, [user, savedData?.username]);

  // Fetch problem details when challengeData changes
  useEffect(() => {
    const fetchProblemDetails = async () => {
      if (!challengeData?.questionLink) return;

      try {
        setLoadingProblem(true);
        // Extract title slug from the question link
        const titleSlug = challengeData.questionLink.split('/problems/')[1]?.split('/')[0];
        
        if (!titleSlug) {
          console.error('Could not extract title slug from question link:', challengeData.questionLink);
          return;
        }

        // console.log('Fetching problem details for:', titleSlug);
        
        const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/leetcode/problem/${titleSlug}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch problem details: ${response.status}`);
        }

        const details = await response.json();
        // console.log('Fetched problem details:', details);
        
        setProblemDetails(details);
        
        // Update challengeData with fetched code snippets and real questionId
        if (details.codeSnippets) {
          challengeData.codeSnippets = details.codeSnippets;
        }
        
        // Set the real questionId from the API response
        if (details.questionId) {
          challengeData.realQuestionId = details.questionId;
        }
        
      } catch (error) {
        console.error('Error fetching problem details:', error);
        setError('Failed to load problem details. Using fallback template.');
      } finally {
        setLoadingProblem(false);
      }
    };

    fetchProblemDetails();
  }, [challengeData]);

  // Set initial code based on problem details and language (only if no saved data exists)
  useEffect(() => {
    if (loadingProblem) {
      return;
    }

    // Don't load template if we already have saved code or have already loaded a template
    if (hasLoadedTemplate || (savedData?.code && savedData.code !== '// Loading code template...')) {
      return;
    }

    // Try to use problemDetails first, then fallback to challengeData
    const codeSnippets = problemDetails?.codeSnippets || challengeData?.codeSnippets;
    
    if (codeSnippets && codeSnippets.length > 0) {
      // Find the code snippet for the selected language
      const snippet = codeSnippets.find(
        snippet => snippet.langSlug === language || 
                   (language === 'python3' && (snippet.langSlug === 'python3' || snippet.langSlug === 'python')) ||
                   (language === 'javascript' && snippet.langSlug === 'javascript') ||
                   (language === 'cpp' && snippet.langSlug === 'cpp') ||
                   (language === 'java' && snippet.langSlug === 'java') ||
                   (language === 'c' && snippet.langSlug === 'c')
      );
      
      if (snippet) {
        // console.log('Loading code template for', language, ':', snippet.code.substring(0, 100) + '...');
        setCode(snippet.code);
        setHasLoadedTemplate(true);
      } else {
        // console.log('No snippet found for', language, 'using generic template');
        setGenericCodeTemplate(language);
        setHasLoadedTemplate(true);
      }
    } else {
      // console.log('No code snippets available, using generic template');
      setGenericCodeTemplate(language);
      setHasLoadedTemplate(true);
    }
  }, [language, problemDetails, loadingProblem, challengeData, hasLoadedTemplate, savedData?.code]);

  const setGenericCodeTemplate = (selectedLanguage) => {
    switch (selectedLanguage) {
      case 'python3':
        setCode('# Write your Python solution here\n\ndef solution():\n    pass\n');
        break;
      case 'java':
        setCode('// Write your Java solution here\n\nclass Solution {\n    public static void main(String[] args) {\n        // Your solution here\n    }\n}');
        break;
      case 'cpp':
        setCode('// Write your C++ solution here\n\n#include <iostream>\n\nint main() {\n    // Your solution here\n    return 0;\n}');
        break;
      case 'javascript':
        setCode('// Write your JavaScript solution here\n\nfunction solution() {\n    // Your code here\n}\n');
        break;
      case 'c':
        setCode('// Write your C solution here\n\n#include <stdio.h>\n\nint main() {\n    // Your solution here\n    return 0;\n}');
        break;
      default:
        setCode('// Write your code here');
    }
  };

  // Map of language IDs to Monaco editor language identifiers
  const languageMap = {
    'python3': 'python',
    'java': 'java',
    'cpp': 'cpp',
    'javascript': 'javascript',
    'c': 'c'
  };

  const toggleTheme = () => {
    setTheme(theme === 'vs-dark' ? 'light' : 'vs-dark');
  };

  const increaseFontSize = () => {
    setFontSize(prev => Math.min(prev + 2, 24)); // Max font size 24
  };

  const decreaseFontSize = () => {
    setFontSize(prev => Math.max(prev - 2, 10)); // Min font size 10
  };

  const handleLanguageChange = (e) => {
    const selectedLanguage = e.target.value;
    setLanguage(selectedLanguage);
    
    // Always load the boilerplate/template for the new language
    // This ensures users get the proper syntax and structure for each language
    const codeSnippets = problemDetails?.codeSnippets || challengeData?.codeSnippets;
    
    if (codeSnippets && codeSnippets.length > 0) {
      const snippet = codeSnippets.find(
        snippet => snippet.langSlug === selectedLanguage || 
                   (selectedLanguage === 'python3' && (snippet.langSlug === 'python3' || snippet.langSlug === 'python')) ||
                   (selectedLanguage === 'javascript' && snippet.langSlug === 'javascript') ||
                   (selectedLanguage === 'cpp' && snippet.langSlug === 'cpp') ||
                   (selectedLanguage === 'java' && snippet.langSlug === 'java') ||
                   (selectedLanguage === 'c' && snippet.langSlug === 'c')
      );
      
      if (snippet) {
        // console.log('Loading boilerplate for', selectedLanguage, ':', snippet.code.substring(0, 100) + '...');
        setCode(snippet.code);
      } else {
        // console.log('No snippet found for', selectedLanguage, 'using generic template');
        setGenericCodeTemplate(selectedLanguage);
      }
    } else {
      console.log('No code snippets available, using generic template for', selectedLanguage);
      setGenericCodeTemplate(selectedLanguage);
    }
    
    // The code will be automatically saved to localStorage via the useEffect
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!username.trim()) {
      setError('Please enter your username');
      return;
    }

    if (!code.trim()) {
      setError('Please write some code before submitting');
      return;
    }

    onSubmit({ username, code, language, approach });
  };

  // Function to reset to code template
  const resetToTemplate = () => {
    const codeSnippets = problemDetails?.codeSnippets || challengeData?.codeSnippets;
    
    if (codeSnippets && codeSnippets.length > 0) {
      const snippet = codeSnippets.find(
        snippet => snippet.langSlug === language || 
                   (language === 'python3' && (snippet.langSlug === 'python3' || snippet.langSlug === 'python')) ||
                   (language === 'javascript' && snippet.langSlug === 'javascript') ||
                   (language === 'cpp' && snippet.langSlug === 'cpp') ||
                   (language === 'java' && snippet.langSlug === 'java') ||
                   (language === 'c' && snippet.langSlug === 'c')
      );
      
      if (snippet) {
        setCode(snippet.code);
      } else {
        setGenericCodeTemplate(language);
      }
    } else {
      setGenericCodeTemplate(language);
    }
  };

  // Function to clear all saved data
  const clearSavedData = () => {
    try {
      localStorage.removeItem(storageKey);
      // Reset form to defaults
      setCode('// Loading code template...');
      setApproach('Approach:\n- \n\nTime Complexity: O()\nSpace Complexity: O()');
      setLanguage('python3');
      setTheme('vs-dark');
      setFontSize(14);
      setHasLoadedTemplate(false);
      if (user?.fullName) {
        setUsername(user.fullName);
      }
    } catch (error) {
      console.error('Error clearing saved data:', error);
    }
  };

  return (
    <div className="h-full flex flex-col max-h-screen overflow-hidden">
      {/* Saved data indicator */}
      {savedData && (
        <div className="relative bg-gradient-to-r from-purple-900/30 to-pink-900/30 backdrop-blur-sm border-b border-purple-500/20 text-purple-200 px-4 py-3 text-sm flex justify-between items-center">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-t-lg blur-sm"></div>
          <div className="relative flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-purple-400 animate-pulse"></div>
            <span className="font-medium">Form data auto-saved</span>
          </div>
          <button
            type="button"
            onClick={clearSavedData}
            className="relative text-purple-300 hover:text-white transition-all duration-200 text-sm bg-purple-800/30 hover:bg-purple-700/50 px-3 py-1.5 rounded-lg border border-purple-500/20 hover:border-purple-400/40"
            title="Clear saved data and reset form"
          >
            Clear saved data
          </button>
        </div>
      )}
      
      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-2 rounded-md mb-4 mx-4 flex-shrink-0">
          {error}
        </div>
      )}

      {/* Top Controls */}
      <div className="flex flex-row gap-4 px-4 py-2 items-end border-b border-gray-700 flex-shrink-0">
        <div className="w-1/4">
          <label htmlFor="username" className="block text-gray-300 mb-1">
            Your Name
          </label>
          <input
            type="text"
            id="username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            className="w-full bg-gray-800 border border-gray-700 rounded-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            placeholder="Enter your name"
            required
          />
        </div>

        <div className="w-1/4">
          <label htmlFor="language" className="block text-gray-300 mb-1">
            Language
          </label>
          <select
            id="language"
            value={language}
            onChange={handleLanguageChange}
            className="w-full bg-gray-800 border border-gray-700 rounded-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="python3">Python</option>
            <option value="java">Java</option>
            <option value="cpp">C++</option>
            <option value="javascript">JavaScript</option>
            <option value="c">C</option>
          </select>
        </div>

        <div className="flex items-end gap-2">
          <div>
            <label className="block text-gray-300 mb-1">Theme</label>
            <button
              type="button"
              onClick={toggleTheme}
              className="bg-gray-800 border border-gray-700 rounded-md p-2 text-white hover:bg-gray-700 transition-colors"
              aria-label={theme === 'vs-dark' ? 'Switch to light theme' : 'Switch to dark theme'}
            >
              {theme === 'vs-dark' ? <Sun size={20} /> : <Moon size={20} />}
            </button>
          </div>

          <div>
            <label className="block text-gray-300 mb-1">Font Size: {fontSize}px</label>
            <div className="flex">
              <button
                type="button"
                onClick={decreaseFontSize}
                className="bg-gray-800 border border-gray-700 border-r-0 rounded-l-md p-2 text-white hover:bg-gray-700 transition-colors"
                aria-label="Decrease font size"
              >
                <Minus size={20} />
              </button>
              <button
                type="button"
                onClick={increaseFontSize}
                className="bg-gray-800 border border-gray-700 rounded-r-md p-2 text-white hover:bg-gray-700 transition-colors"
                aria-label="Increase font size"
              >
                <Plus size={20} />
              </button>
            </div>
          </div>

          <div>
            <label className="block text-gray-300 mb-1">Template</label>
            <button
              type="button"
              onClick={resetToTemplate}
              className="bg-gray-800 border border-gray-700 rounded-md px-3 py-2 text-white hover:bg-gray-700 transition-colors text-sm"
              title="Reset to default code template"
            >
              Reset
            </button>
          </div>
        </div>

        <div className="flex-grow flex justify-end">
          <button
            type="submit"
            onClick={handleSubmit}
            disabled={isLoading || loadingProblem}
            className={`bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-2 rounded-md font-medium transition-all duration-200 ${
              isLoading || loadingProblem
                ? 'opacity-70 cursor-not-allowed'
                : 'hover:from-purple-700 hover:to-pink-700 hover:shadow-lg'
            }`}
          >
            {isLoading ? 'Submitting...' : loadingProblem ? 'Loading...' : 'Submit Solution'}
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex min-h-0 overflow-hidden">
        {/* Code Editor and Approach - Full Width */}
        <div className="flex-1 flex flex-col min-h-0">
          <div className="flex-1 flex gap-4 p-4 min-h-0">
            <div className="w-2/5 flex flex-col min-h-0">
              <label htmlFor="approach" className="block text-gray-300 mb-2 flex-shrink-0">
                Your Approach
              </label>
              <textarea
                id="approach"
                value={approach}
                onChange={(e) => setApproach(e.target.value)}
                className="flex-1 w-full bg-gray-800 border border-gray-700 rounded-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono resize-none overflow-y-auto"
                style={{ fontSize: `${fontSize}px` }}
                placeholder="Describe your approach, time and space complexity"
              />
            </div>

            <div className="w-3/5 flex flex-col min-h-0">
              <label htmlFor="code-editor" className="block text-gray-300 mb-2 flex-shrink-0">
                Write Your Solution
              </label>
              <div className="flex-1 border border-gray-700 rounded-md overflow-hidden">
                <Editor
                  height="100%"
                  defaultLanguage="python"
                  language={languageMap[language] || 'plaintext'}
                  value={code}
                  onChange={setCode}
                  theme={theme}
                  options={{
                    minimap: { enabled: true },
                    scrollBeyondLastLine: false,
                    fontSize: fontSize,
                    tabSize: 2,
                    automaticLayout: true,
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

CodeEditorForm.propTypes = {
  onSubmit: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
  user: PropTypes.shape({
    fullName: PropTypes.string,
    username: PropTypes.string
  }),
  challengeData: PropTypes.shape({
    codeSnippets: PropTypes.arrayOf(PropTypes.shape({
      lang: PropTypes.string,
      langSlug: PropTypes.string,
      code: PropTypes.string
    }))
  })
};

CodeEditorForm.defaultProps = {
  isLoading: false,
  user: null,
  challengeData: null
};

export default CodeEditorForm;
