/**
 * Problem routes
 * Defines routes for problem-related endpoints
 */
import express from 'express';
import { 
  getProblemByTitleSlug, 
  submitProblemSolution, 
  submitProblemSolutionAsProxy 
} from '../controllers/problem.controller.js';

const router = express.Router();

/**
 * @route   GET /api/v1/leetcode/problems/:titleSlug
 * @desc    Get problem by title slug
 * @access  Public
 */
router.get('/:titleSlug', getProblemByTitleSlug);

/**
 * @route   POST /api/v1/leetcode/problems/submit
 * @desc    Submit solution for a problem
 * @access  Public
 */
router.post('/submit', submitProblemSolution);

/**
 * @route   POST /api/v1/leetcode/problems/submit-as-proxy
 * @desc    Submit solution as proxy
 * @access  Public
 */
router.post('/submit-as-proxy', submitProblemSolutionAsProxy);

export default router;
