import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, Send, User, Mail, MessageSquare, CheckCircle, AlertCircle } from 'lucide-react';
import PropTypes from 'prop-types';

const ContactModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    query: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const [showSuccess, setShowSuccess] = useState(false);

  // Handle form input changes
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage({ type: '', text: '' });

    try {
      // Make API call to backend
      const backendUrl = import.meta.env.VITE_API_URL || 'http://localhost:5000';
      const response = await fetch(`${backendUrl}/api/v1/query/contact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Show success message and reset form
        setMessage({
          type: 'success',
          text: data.message || 'Thank you for reaching out! We\'ll get back to you soon.'
        });
        setShowSuccess(true);
        setFormData({ name: '', email: '', query: '' });

        // Auto close modal after 3 seconds
        setTimeout(() => {
          setShowSuccess(false);
          setMessage({ type: '', text: '' });
          onClose();
        }, 3000);
      } else {
        // Show error message
        setMessage({
          type: 'error',
          text: data.error || 'Failed to send message. Please try again.'
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setMessage({
        type: 'error',
        text: 'Network error. Please check your connection and try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset states when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setMessage({ type: '', text: '' });
      setShowSuccess(false);
    }
  }, [isOpen]);

  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-2 xs:p-3 sm:p-4">
      {/* Backdrop with blur */}
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-md"
        onClick={onClose}
      />
      
      {/* Modal Container */}
      <div className="relative w-[98%] xs:w-[95%] sm:w-[85%] md:w-[75%] lg:w-[60%] max-w-2xl mx-auto">
        {/* Gradient Background Effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 to-pink-600/20 rounded-2xl blur-xl" />
        
        {/* Modal Content */}
        <div className="relative bg-gray-900/95 backdrop-blur-sm rounded-xl xs:rounded-2xl border border-purple-500/30 shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="relative px-3 xs:px-4 sm:px-6 md:px-8 py-3 xs:py-4 sm:py-6 border-b border-purple-500/20">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-pink-600/10" />
            <div className="relative flex items-center justify-between">
              <h2 className="text-lg xs:text-xl sm:text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">
                Connect With Us
              </h2>
              <button
                onClick={onClose}
                className="p-1.5 xs:p-2 rounded-full hover:bg-purple-500/20 text-gray-400 hover:text-white transition-all duration-200"
                aria-label="Close modal"
              >
                <X size={16} className="xs:hidden" />
                <X size={20} className="hidden xs:block" />
              </button>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-4 sm:p-6 md:p-8 space-y-4 sm:space-y-6">
            {/* Success/Error Message */}
            {message.text && (
              <div className={`p-3 sm:p-4 rounded-lg border flex items-center gap-2 sm:gap-3 ${
                message.type === 'success'
                  ? 'bg-green-900/30 border-green-500/30 text-green-300'
                  : 'bg-red-900/30 border-red-500/30 text-red-300'
              }`}>
                {message.type === 'success' ? (
                  <CheckCircle size={16} className="text-green-400 flex-shrink-0" />
                ) : (
                  <AlertCircle size={16} className="text-red-400 flex-shrink-0" />
                )}
                <span className="text-xs sm:text-sm">{message.text}</span>
              </div>
            )}

            {/* Success Animation */}
            {showSuccess && (
              <div className="text-center py-4 sm:py-6">
                <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-green-500/20 rounded-full mb-3 sm:mb-4">
                  <CheckCircle size={24} className="text-green-400 sm:hidden" />
                  <CheckCircle size={32} className="text-green-400 hidden sm:block" />
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-green-300 mb-1 sm:mb-2">Message Sent!</h3>
                <p className="text-gray-400 text-xs sm:text-sm">We'll get back to you soon.</p>
              </div>
            )}

            {/* Form Fields - Only show if not in success state */}
            {!showSuccess && (
              <>
            {/* Name Field */}
            <div className="space-y-1 sm:space-y-2">
              <label className="flex items-center gap-2 text-xs sm:text-sm font-medium text-gray-300">
                <User size={14} className="text-purple-400" />
                Name
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-3 sm:px-4 py-2 sm:py-3 bg-purple-900/30 border border-purple-500/30 rounded-lg focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 text-white text-sm placeholder-gray-400 transition-all duration-200"
                placeholder="Enter your full name"
              />
            </div>

            {/* Email Field */}
            <div className="space-y-1 sm:space-y-2">
              <label className="flex items-center gap-2 text-xs sm:text-sm font-medium text-gray-300">
                <Mail size={14} className="text-purple-400" />
                Email
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full px-3 sm:px-4 py-2 sm:py-3 bg-purple-900/30 border border-purple-500/30 rounded-lg focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 text-white text-sm placeholder-gray-400 transition-all duration-200"
                placeholder="Enter your email address"
              />
            </div>

            {/* Query Field */}
            <div className="space-y-1 sm:space-y-2">
              <label className="flex items-center gap-2 text-xs sm:text-sm font-medium text-gray-300">
                <MessageSquare size={14} className="text-purple-400" />
                Query
              </label>
              <textarea
                name="query"
                value={formData.query}
                onChange={handleChange}
                required
                rows={3}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 bg-purple-900/30 border border-purple-500/30 rounded-lg focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 text-white text-sm placeholder-gray-400 resize-none transition-all duration-200"
                placeholder="Tell us how we can help you..."
              />
            </div>

            {/* Submit Button */}
            <button                type="submit"
              disabled={isSubmitting}
              className="w-full flex items-center justify-center gap-2 px-4 sm:px-6 py-2 sm:py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:from-purple-800 disabled:to-pink-800 text-white text-sm sm:text-base font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] disabled:scale-100 shadow-lg hover:shadow-xl disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send size={18} />
                  Send Message
                </>
              )}
            </button>
            </>
            )}
          </form>
        </div>
      </div>
    </div>,
    document.body
  );
};

ContactModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default ContactModal;
