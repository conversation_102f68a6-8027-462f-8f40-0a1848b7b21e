Stack trace:
Frame         Function      Args
0007FFFFA160  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9060) msys-2.0.dll+0x1FE8E
0007FFFFA160  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA438) msys-2.0.dll+0x67F9
0007FFFFA160  000210046832 (000210286019, 0007FFFFA018, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA160  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA160  000210068E24 (0007FFFFA170, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA440  00021006A225 (0007FFFFA170, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF83F9E0000 ntdll.dll
7FF83E580000 KERNEL32.DLL
7FF83CBF0000 KERNELBASE.dll
7FF83D910000 USER32.dll
7FF83D750000 win32u.dll
7FF83E900000 GDI32.dll
7FF83D560000 gdi32full.dll
7FF83D6A0000 msvcp_win.dll
7FF83D070000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF83F080000 advapi32.dll
7FF83F190000 msvcrt.dll
7FF83E750000 sechost.dll
7FF83EA20000 RPCRT4.dll
7FF83C250000 CRYPTBASE.DLL
7FF83D340000 bcryptPrimitives.dll
7FF83F040000 IMM32.DLL
