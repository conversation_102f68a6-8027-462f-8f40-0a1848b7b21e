import PropTypes from 'prop-types';
import CountUp from 'react-countup';
import { useInView } from 'react-intersection-observer';

const CommunityCard = ({ title, description, icon, link, index = 0 }) => {
  const { ref, inView } = useInView({ 
    triggerOnce: true,
    threshold: 0.3
  });
  const targetNumber = parseInt(title.replace(/[^\d]/g, ""), 10) || 0;

  // Color variants for each card
  const colorVariants = [
    {
      gradient: 'from-green-500/20 to-emerald-500/20',
      icon: 'text-green-400',
      number: 'text-green-400',
      border: 'border-green-500/20',
      glow: 'shadow-green-500/25',
      hoverBorder: 'hover:border-green-400/40'
    },
    {
      gradient: 'from-blue-500/20 to-cyan-500/20',
      icon: 'text-blue-400',
      number: 'text-blue-400',
      border: 'border-blue-500/20',
      glow: 'shadow-blue-500/25',
      hoverBorder: 'hover:border-blue-400/40'
    },
    {
      gradient: 'from-purple-500/20 to-pink-500/20',
      icon: 'text-purple-400',
      number: 'text-purple-400',
      border: 'border-purple-500/20',
      glow: 'shadow-purple-500/25',
      hoverBorder: 'hover:border-purple-400/40'
    }
  ];

  const variant = colorVariants[index % colorVariants.length];

  return (
    <div
      ref={ref}
      className={`group relative overflow-hidden rounded-2xl p-[1px] transition-all duration-500 hover:scale-105 ${
        inView ? 'animate-fade-in-up' : 'opacity-0 translate-y-10'
      }`}
      style={{ animationDelay: `${index * 200}ms` }}
    >
      {/* Animated border gradient */}
      <div className={`absolute inset-0 bg-gradient-to-r ${variant.gradient} rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>
      
      {/* Main card */}
      <div className={`relative bg-gray-900/60 backdrop-blur-sm border ${variant.border} ${variant.hoverBorder} rounded-2xl p-6 h-full min-h-[320px] flex flex-col transition-all duration-500 group-hover:shadow-xl ${variant.glow}`}>
        
        {/* Floating particles effect */}
        <div className="absolute top-4 right-4 w-2 h-2 bg-white/20 rounded-full animate-pulse"></div>
        <div className="absolute bottom-8 left-6 w-1 h-1 bg-white/30 rounded-full animate-pulse animation-delay-1000"></div>
        
        {/* Icon with animated background */}
        <div className="relative mb-4 flex justify-center">
          <div className={`relative p-3 rounded-xl bg-gradient-to-br ${variant.gradient} backdrop-blur-sm border ${variant.border} group-hover:scale-110 transition-all duration-500`}>
            <div className={`text-3xl ${variant.icon} transition-all duration-500 group-hover:scale-110`}>
              {icon}
            </div>
            
            {/* Animated ring */}
            <div className={`absolute inset-0 rounded-xl border-2 ${variant.border} animate-ping opacity-0 group-hover:opacity-75`}></div>
          </div>
        </div>

        {/* Number with enhanced animation */}
        <div className="text-center mb-3">
          <h3 className={`text-5xl font-bold ${variant.number} transition-all duration-500 group-hover:scale-110 font-mono`}>
            {inView ? (
              <CountUp 
                start={0} 
                end={targetNumber} 
                duration={2.5} 
                separator="," 
                delay={index * 0.2}
              />
            ) : "0"}
            <span className="text-2xl ml-1">+</span>
          </h3>
          
          {/* Animated underline */}
          <div className={`w-12 h-1 bg-gradient-to-r ${variant.gradient} mx-auto rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500`}></div>
        </div>

        {/* Description */}
        <div className="flex-1 flex items-center justify-center">
          <p className="text-gray-300 text-center leading-relaxed group-hover:text-white transition-colors duration-300">
            {description}
          </p>
        </div>

        {/* No Call to action button */}

        {/* Hover overlay */}
        <div className={`absolute inset-0 bg-gradient-to-br ${variant.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-2xl`}></div>
      </div>

      <style jsx>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fade-in-up {
          animation: fade-in-up 0.8s ease-out forwards;
        }
        
        .animation-delay-1000 {
          animation-delay: 1000ms;
        }
      `}</style>
    </div>
  );
};

CommunityCard.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  icon: PropTypes.node.isRequired,
  link: PropTypes.string,
  index: PropTypes.number,
};

export default CommunityCard;
