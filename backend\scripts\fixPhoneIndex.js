import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const fixPhoneIndex = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Get the users collection
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');

    // Check current indexes
    console.log('Current indexes:');
    const indexes = await usersCollection.indexes();
    console.log(indexes);

    // Drop the phone index if it exists
    try {
      await usersCollection.dropIndex('phone_1');
      console.log('Successfully dropped phone_1 index');
    } catch (error) {
      if (error.code === 27) {
        console.log('phone_1 index does not exist');
      } else {
        console.error('Error dropping phone_1 index:', error);
      }
    }

    // Check indexes after dropping
    console.log('Indexes after dropping phone_1:');
    const indexesAfter = await usersCollection.indexes();
    console.log(indexesAfter);

    console.log('Phone index fix completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error fixing phone index:', error);
    process.exit(1);
  }
};

fixPhoneIndex();
