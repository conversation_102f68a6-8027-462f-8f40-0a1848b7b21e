/**
 * Quick test to manually update a user with XP and streak
 */
import mongoose from 'mongoose';
import User from '../models/user.model.js';
import dotenv from 'dotenv';

dotenv.config();

const testUserUpdate = async () => {
  try {
    await mongoose.connect(process.env.DATABASE_URI);
    console.log('Connected to database');

    // Find first user
    const user = await User.findOne({});
    if (!user) {
      console.log('No users found');
      return;
    }

    console.log('Found user:', user.fullName, 'Current XP:', user.xp, 'Current streak:', user.streak);

    // Try to update the user
    user.xp = 50;
    user.streak = 3;
    user.lastQuestionSolvedDate = new Date();

    const savedUser = await user.save();
    console.log('Updated user successfully:', savedUser.fullName, 'XP:', savedUser.xp, 'Streak:', savedUser.streak);

  } catch (error) {
    console.error('Error updating user:', error);
  } finally {
    await mongoose.disconnect();
  }
};

testUserUpdate();
