import React, { useRef, useEffect, useState } from 'react';

const RoadmapScroll = () => {
  const scrollContainerRef = useRef(null);
  const [showInstructions, setShowInstructions] = useState(true);
  
  // Add mouse drag functionality for better desktop experience
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;
    
    let isDown = false;
    let startX;
    let scrollLeft;
    
    // Hide instructions after 5 seconds
    const timer = setTimeout(() => {
      setShowInstructions(false);
    }, 5000);
    
    const handleMouseDown = (e) => {
      isDown = true;
      scrollContainer.classList.add('active');
      startX = e.pageX - scrollContainer.offsetLeft;
      scrollLeft = scrollContainer.scrollLeft;
      setShowInstructions(false); // Hide instructions when user starts interacting
    };
    
    const handleMouseLeave = () => {
      isDown = false;
      scrollContainer.classList.remove('active');
    };
    
    const handleMouseUp = () => {
      isDown = false;
      scrollContainer.classList.remove('active');
    };
    
    const handleMouseMove = (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - scrollContainer.offsetLeft;
      const walk = (x - startX) * 2; // Scroll speed multiplier
      scrollContainer.scrollLeft = scrollLeft - walk;
    };
    
    // Handle touch events for mobile
    const handleTouchStart = (e) => {
      setShowInstructions(false); // Hide instructions when user starts interacting
      const touch = e.touches[0];
      startX = touch.clientX;
      scrollLeft = scrollContainer.scrollLeft;
    };
    
    const handleTouchMove = (e) => {
      if (!startX) return;
      const touch = e.touches[0];
      const x = touch.clientX;
      const walk = (startX - x) * 1.5; // Touch scroll speed
      scrollContainer.scrollLeft = scrollLeft + walk;
    };
    
    // Add event listeners
    scrollContainer.addEventListener('mousedown', handleMouseDown);
    scrollContainer.addEventListener('mouseleave', handleMouseLeave);
    scrollContainer.addEventListener('mouseup', handleMouseUp);
    scrollContainer.addEventListener('mousemove', handleMouseMove);
    scrollContainer.addEventListener('touchstart', handleTouchStart);
    scrollContainer.addEventListener('touchmove', handleTouchMove);
    
    // Clean up
    return () => {
      clearTimeout(timer);
      scrollContainer.removeEventListener('mousedown', handleMouseDown);
      scrollContainer.removeEventListener('mouseleave', handleMouseLeave);
      scrollContainer.removeEventListener('mouseup', handleMouseUp);
      scrollContainer.removeEventListener('mousemove', handleMouseMove);
      scrollContainer.removeEventListener('touchstart', handleTouchStart);
      scrollContainer.removeEventListener('touchmove', handleTouchMove);
    };
  }, []);
  
  // Calculate responsive height based on screen width
  const [containerHeight, setContainerHeight] = useState('720px');
  const [imgLoaded, setImgLoaded] = useState(false);
  const imgRef = useRef(null);
  
  // Handle image load to maintain aspect ratio
  const handleImageLoad = () => {
    setImgLoaded(true);
    updateHeight();
  };
  
  const updateHeight = () => {
    // Set height proportionally based on screen width
    // Original ratio is 6400:720 which is ~8.9:1
    const screenWidth = window.innerWidth;
    const containerWidth = Math.min(screenWidth - 40, 1280); // Account for padding and max container width
    
    // Calculate aspect ratio from the image if available
    if (imgRef.current && imgRef.current.naturalWidth) {
      const imgAspectRatio = imgRef.current.naturalWidth / imgRef.current.naturalHeight;
      // Ensure minimum and maximum heights
      let calculatedHeight = Math.max(Math.min(containerWidth / imgAspectRatio, 720), 240);
      setContainerHeight(`${calculatedHeight}px`);
      return;
    }
    
    // Fallback if image dimensions aren't available yet
    if (screenWidth < 640) {
      setContainerHeight('300px');
    } else if (screenWidth < 768) {
      setContainerHeight('400px');
    } else if (screenWidth < 1024) {
      setContainerHeight('500px');
    } else {
      setContainerHeight('720px');
    }
  };
  
  useEffect(() => {
    // Set initial height
    updateHeight();
    
    // Update height when window resizes
    window.addEventListener('resize', updateHeight);
    
    return () => {
      window.removeEventListener('resize', updateHeight);
    };
  }, [imgLoaded]);
  
  return (
    <div className="relative w-full">
      {/* Scroll indicators */}
      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-gradient-to-r from-gray-900 to-transparent w-12 h-full pointer-events-none opacity-60"></div>
      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-gradient-to-l from-gray-900 to-transparent w-12 h-full pointer-events-none opacity-60"></div>
      
      <div 
        ref={scrollContainerRef}
        className="scroll-container rounded-xl"
        style={{
          width: '100%',
          height: containerHeight,
          overflowX: 'auto',
          overflowY: 'hidden',
          whiteSpace: 'nowrap',
          paddingBottom: '1rem',
          borderRadius: '0.5rem',
          boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
          background: 'linear-gradient(to bottom, rgba(15, 23, 42, 0.8), rgba(15, 23, 42, 0.6))',
          display: 'flex',
          alignItems: 'center', // Center vertically
        }}
      >
        <div style={{ display: 'inline-block', height: '100%' }}>
          <img
            ref={imgRef}
            src="/Roadmap.png"
            alt="DSA Learning Roadmap"
            style={{
              height: '100%', // Use 100% of container height
              width: 'auto', // Allow width to adjust based on aspect ratio
              maxWidth: 'none', // Let the image be its original size
              objectFit: 'contain', // Ensures the image maintains its aspect ratio
              objectPosition: 'left center',
            }}
            onLoad={handleImageLoad}
            draggable="false" // Prevent image dragging for better scrolling experience
          />
        </div>
        
        {/* Instructions overlay */}
        {showInstructions && (
          <div 
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black bg-opacity-75 px-4 py-3 rounded-lg text-white text-sm md:text-base font-medium opacity-90"
            style={{ 
              animation: 'fadeOut 3s forwards 2s',
              pointerEvents: 'none',
              zIndex: 20,
            }}
          >
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19l7-7-7-7" />
              </svg>
              Scroll or drag horizontally to explore
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RoadmapScroll;
