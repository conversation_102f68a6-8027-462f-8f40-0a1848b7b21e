{"homepage": "https://abhinavcodedeveloper.github.io/Big-O--Web/", "name": "bigo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"predeploy": "npm run build", "deploy": "gh-pages -d build", "dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@reactflow/background": "^11.3.14", "@reactflow/controls": "^11.2.14", "@reactflow/core": "^11.11.4", "@reactflow/minimap": "^11.7.14", "@reactflow/node-resizer": "^2.2.14", "@tailwindcss/vite": "^4.0.8", "bigo": "file:", "dagre": "^0.8.5", "dotenv": "^16.4.7", "framer-motion": "^12.9.4", "lucide-react": "^0.475.0", "prop-types": "^15.8.1", "react": "^19.0.0", "react-countup": "^6.5.3", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.0", "react-simple-typewriter": "^5.0.1", "reactflow": "^11.11.4", "tailwindcss": "^4.0.8"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "vite": "^6.2.0"}}