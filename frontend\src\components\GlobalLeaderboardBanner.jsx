import { useState, useEffect } from 'react';
import { Trophy } from 'lucide-react';
import GlobalLeaderboardModal from './GlobalLeaderboardModal';

const GlobalLeaderboardBanner = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [showBanner, setShowBanner] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    // First show the ropes and rings immediately
    setIsVisible(true);
    
    // Then after a delay, drop the banner from the ropes
    const timer = setTimeout(() => {
      setShowBanner(true);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const handleBannerClick = () => {
    setIsModalOpen(true);
  };

  return (
    <div className="w-full flex justify-center relative z-40 mt-3 xs:mt-5 mb-1 xs:mb-2">
      <div 
        className="relative cursor-pointer translate-x-0"
        onClick={handleBannerClick}
      >
        {/* Hanging Ropes - appear first */}
        <div className={`absolute -top-6 xs:-top-8 left-1/4 w-0.5 xs:w-1 h-6 xs:h-8 bg-gradient-to-b from-yellow-600 to-yellow-500 rounded-full shadow-sm transition-all duration-500 ${
          isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-75'
        }`}></div>
        <div className={`absolute -top-6 xs:-top-8 right-1/4 w-0.5 xs:w-1 h-6 xs:h-8 bg-gradient-to-b from-yellow-600 to-yellow-500 rounded-full shadow-sm transition-all duration-500 ${
          isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-75'
        }`}></div>
        
        {/* Small connecting rings - appear with ropes */}
        <div className={`absolute -top-8 xs:-top-10 left-1/4 transform -translate-x-1/2 w-2 xs:w-3 h-2 xs:h-3 border-1.5 xs:border-2 border-yellow-500 rounded-full bg-yellow-400 transition-all duration-500 ${
          isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-50'
        }`}></div>
        <div className={`absolute -top-8 xs:-top-10 right-1/4 transform translate-x-1/2 w-2 xs:w-3 h-2 xs:h-3 border-1.5 xs:border-2 border-yellow-500 rounded-full bg-yellow-400 transition-all duration-500 ${
          isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-50'
        }`}></div>
        
        {/* Banner Body - drops down from the ropes after delay */}
        <div className={`relative bg-gradient-to-r from-yellow-500 via-yellow-400 to-yellow-500 rounded-lg shadow-2xl transform hover:scale-105 transition-all duration-700 hover:shadow-yellow-500/20 ${
          showBanner 
            ? 'translate-y-0 opacity-100' 
            : '-translate-y-16 opacity-0'
        }`}>
          {/* Banner decorative edges */}
          <div className="absolute -left-1.5 xs:-left-2 top-1.5 xs:top-2 w-3 xs:w-4 h-3 xs:h-4 bg-yellow-600 transform rotate-45"></div>
          <div className="absolute -right-1.5 xs:-right-2 top-1.5 xs:top-2 w-3 xs:w-4 h-3 xs:h-4 bg-yellow-600 transform rotate-45"></div>
          <div className="absolute -left-1.5 xs:-left-2 bottom-1.5 xs:bottom-2 w-3 xs:w-4 h-3 xs:h-4 bg-yellow-600 transform rotate-45"></div>
          <div className="absolute -right-1.5 xs:-right-2 bottom-1.5 xs:bottom-2 w-3 xs:w-4 h-3 xs:h-4 bg-yellow-600 transform rotate-45"></div>
          
          {/* Main Content */}
          <div className="px-4 xs:px-6 sm:px-8 py-2 xs:py-3 sm:py-4 relative z-10">
            <div className="flex items-center justify-center gap-2 xs:gap-3">
              <Trophy size={16} className="text-yellow-900 drop-shadow-sm xs:hidden" />
              <Trophy size={20} className="text-yellow-900 drop-shadow-sm hidden xs:block sm:hidden" />
              <Trophy size={24} className="text-yellow-900 drop-shadow-sm hidden sm:block" />
              <h2 className="text-base xs:text-lg sm:text-xl font-bold text-yellow-900 tracking-wide font-serif">
                Leaderboard
              </h2>
            </div>
            
            {/* Decorative border */}
            <div className="absolute inset-1 border-2 border-yellow-600 rounded-md opacity-30"></div>
          </div>
          
          {/* Bottom decorative triangles */}
          <div className="absolute -bottom-2 xs:-bottom-3 left-1/2 transform -translate-x-1/2">
            <div className="w-0 h-0 border-l-3 xs:border-l-4 border-r-3 xs:border-r-4 border-t-4 xs:border-t-6 border-l-transparent border-r-transparent border-t-yellow-500"></div>
          </div>
          <div className="absolute -bottom-1.5 xs:-bottom-2 left-[calc(50%-12px)] xs:left-[calc(50%-16px)]">
            <div className="w-0 h-0 border-l-2 xs:border-l-3 border-r-2 xs:border-r-3 border-t-3 xs:border-t-4 border-l-transparent border-r-transparent border-t-yellow-600"></div>
          </div>
          <div className="absolute -bottom-1.5 xs:-bottom-2 left-[calc(50%+12px)] xs:left-[calc(50%+16px)]">
            <div className="w-0 h-0 border-l-2 xs:border-l-3 border-r-2 xs:border-r-3 border-t-3 xs:border-t-4 border-l-transparent border-r-transparent border-t-yellow-600"></div>
          </div>
        </div>

        {/* Subtle glow effect - appears with banner */}
        <div className={`absolute inset-0 bg-gradient-to-r from-yellow-400/20 via-yellow-300/20 to-yellow-400/20 rounded-lg blur-lg -z-10 transition-all duration-700 ${
          showBanner ? 'opacity-100' : 'opacity-0'
        }`}></div>
      </div>

      {/* Global Leaderboard Modal */}
      <GlobalLeaderboardModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
      />
    </div>
  );
};

export default GlobalLeaderboardBanner;
