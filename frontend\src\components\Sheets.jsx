import { Glow<PERSON>ard } from "./ui/spotlight-card";
import { Users, Laptop } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Sheets = () => {
  const navigate = useNavigate();

  const handleOnCampusClick = () => {
    navigate("/sheets/oncampus");
  };

  const handleOffCampusClick = () => {
    navigate("/sheets/offcampus");
  };

  return (
    <div className="min-h-screen px-4 sm:px-8 md:px-16 lg:px-24 xl:px-40 py-8 sm:py-16">
      <div className="max-w-6xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-8 sm:mb-16">
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 sm:mb-8">
            Sheets
          </h1>

          {/* Description Card */}
          <div className="max-w-4xl mx-auto mb-8 sm:mb-16">
            <div className="relative p-4 sm:p-6 md:p-8 rounded-2xl bg-gradient-to-br from-purple-900/30 to-transparent backdrop-blur-sm border border-purple-500/20 shadow-xl">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent rounded-2xl blur-[40px]"></div>
              <p className="relative text-gray-300 text-sm sm:text-base md:text-lg leading-relaxed z-10">
                Whether you&apos;re preparing for campus placements or looking
                to crack interviews at top tech companies, our curated problem
                sheets will help you build the skills you need to succeed.
              </p>
            </div>
          </div>
        </div>

        {/* Choose Your Path Section */}
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-2">
            Choose Your Path
          </h2>
        </div>

        {/* Cards Section */}
        <div className="flex flex-col lg:flex-row gap-10 md:gap-8 justify-center items-center mb-12 px-2">
          {/* On Campus Card */}
          <div onClick={handleOnCampusClick} className="relative">
            <GlowCard
              glowColor="purple"
              customSize={true}
              className="w-full max-w-md h-80 sm:h-96 cursor-pointer hover:scale-105 transition-transform duration-300 group relative"
            >
              <div className="flex flex-col items-center justify-center h-full text-center p-4 sm:p-6 pb-8 sm:pb-8">
                {/* Icon */}
                <div className="w-24 h-24 sm:w-28 md:w-32 sm:h-28 md:h-32 mb-4 sm:mb-6 relative group-hover:scale-110 transition-transform duration-300">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>{" "}
                  <div className="absolute inset-2 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                    <Users size={40} className="text-white sm:hidden" />
                    <Users
                      size={50}
                      className="text-white hidden sm:block md:hidden"
                    />
                    <Users size={64} className="text-white hidden md:block" />
                  </div>
                </div>

                {/* Content */}
                <div className="space-y-2 sm:space-y-4">
                  <h3 className="text-xl sm:text-2xl font-bold text-white group-hover:text-purple-300 transition-colors duration-300">
                    On Campus
                  </h3>
                  <p className="text-gray-300 text-xs sm:text-sm leading-relaxed">
                    Curated problems specifically designed for on-campus
                    placement preparation. Focus on fundamental concepts and
                    commonly asked interview questions.
                  </p>
                </div>
              </div>
            </GlowCard>
          </div>

          {/* Off Campus Card */}
          <div onClick={handleOffCampusClick} className="relative">
            <GlowCard
              glowColor="blue"
              customSize={true}
              className="w-full max-w-md h-80 sm:h-96 cursor-pointer hover:scale-105 transition-transform duration-300 group relative"
            >
              <div className="flex flex-col items-center justify-center h-full text-center p-4 sm:p-6 pb-8 sm:pb-8">
                {/* Icon */}
                <div className="w-24 h-24 sm:w-28 md:w-32 sm:h-28 md:h-32 mb-4 sm:mb-6 relative group-hover:scale-110 transition-transform duration-300">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>{" "}
                  <div className="absolute inset-2 bg-gradient-to-br from-blue-600 to-cyan-600 rounded-full flex items-center justify-center">
                    <Laptop size={40} className="text-white sm:hidden" />
                    <Laptop
                      size={50}
                      className="text-white hidden sm:block md:hidden"
                    />
                    <Laptop size={64} className="text-white hidden md:block" />
                  </div>
                </div>

                {/* Content */}
                <div className="space-y-2 sm:space-y-4">
                  <h3 className="text-xl sm:text-2xl font-bold text-white group-hover:text-blue-300 transition-colors duration-300">
                    Off Campus
                  </h3>
                  <p className="text-gray-300 text-xs sm:text-sm leading-relaxed">
                    Advanced problem sets for off-campus opportunities and
                    competitive programming. Includes complex algorithms and
                    data structure challenges.
                  </p>
                </div>
              </div>
            </GlowCard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sheets;
