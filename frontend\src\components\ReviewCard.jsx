import PropTypes from 'prop-types';

const ReviewCard = ({ title, description, image }) => {
  return (
    
    <div className="group relative overflow-hidden rounded-xl p-1 transition-all duration-300 ease-in-out hover:scale-105">
       <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg"></div>
      <div className="relative h-full w-[340px] xs:w-[360px] sm:w-[380px] bg-gray-900 hover:bg-purple-950 shadow-2xl  hover:shadow-none backdrop-blur-sm rounded-lg p-4 sm:p-6 flex flex-col items-center justify-center gap-3 sm:gap-4 min-h-[180px] xs:min-h-[200px] cursor-pointer transition-all duration-700 ease-in-out">
        
        <div className="flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 bg-purple-200 rounded-full shadow-lg group-hover:shadow-xl transition-all duration-500 ease-in-out overflow-hidden">
          <img 
            src={image} 
            alt={title}
            className="w-full h-full object-cover rounded-full transition-all duration-500 ease-in-out group-hover:scale-110"
            onError={(e) => {
              // Fallback to a default avatar if image fails to load
              e.target.src = '/default-avatar.png';
            }}
          />
        </div>

        <h3 className="text-lg sm:text-xl font-semibold text-purple-50 text-center group-hover:text-white transition-colors duration-500 ease-in-out">
          {title}
        </h3>

        <p className="text-gray-100 text-xs sm:text-sm text-center group-hover:text-white transition-colors duration-500 ease-in-out">
          {description}
        </p>
      </div>
    </div>
  );
};

ReviewCard.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  image: PropTypes.string.isRequired
};

export default ReviewCard;
