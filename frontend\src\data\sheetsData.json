{"onCampus": {"title": "On Campus - DSA Preparation", "description": "Essential problems for campus placement interviews. Focus on fundamental concepts and commonly asked questions.", "topics": [{"id": "arrays-strings", "title": "Arrays & Strings", "totalProblems": 15, "completedProblems": 0, "problems": [{"id": "two-sum", "title": "Two Sum", "difficulty": "Easy", "link": "https://leetcode.com/problems/two-sum/", "completed": false, "tags": ["Array", "Hash Table"]}, {"id": "best-time-stock", "title": "Best Time to Buy and Sell Stock", "difficulty": "Easy", "link": "https://leetcode.com/problems/best-time-to-buy-and-sell-stock/", "completed": false, "tags": ["Array", "Dynamic Programming"]}, {"id": "contains-duplicate", "title": "Contains Duplicate", "difficulty": "Easy", "link": "https://leetcode.com/problems/contains-duplicate/", "completed": false, "tags": ["Array", "Hash Table"]}, {"id": "product-except-self", "title": "Product of Array Except Self", "difficulty": "Medium", "link": "https://leetcode.com/problems/product-of-array-except-self/", "completed": false, "tags": ["Array", "Prefix Sum"]}, {"id": "maximum-subarray", "title": "Maximum Subarray", "difficulty": "Medium", "link": "https://leetcode.com/problems/maximum-subarray/", "completed": false, "tags": ["Array", "Dynamic Programming"]}]}, {"id": "linked-lists", "title": "Linked Lists", "totalProblems": 12, "completedProblems": 0, "problems": [{"id": "reverse-linked-list", "title": "Reverse Linked List", "difficulty": "Easy", "link": "https://leetcode.com/problems/reverse-linked-list/", "completed": false, "tags": ["Linked List", "Recursion"]}, {"id": "merge-two-lists", "title": "Merge Two Sorted Lists", "difficulty": "Easy", "link": "https://leetcode.com/problems/merge-two-sorted-lists/", "completed": false, "tags": ["Linked List", "Recursion"]}, {"id": "linked-list-cycle", "title": "Linked List Cycle", "difficulty": "Easy", "link": "https://leetcode.com/problems/linked-list-cycle/", "completed": false, "tags": ["Linked List", "Two Pointers"]}, {"id": "remove-nth-node", "title": "Remove Nth Node From End of List", "difficulty": "Medium", "link": "https://leetcode.com/problems/remove-nth-node-from-end-of-list/", "completed": false, "tags": ["Linked List", "Two Pointers"]}]}, {"id": "stacks-queues", "title": "Stacks & Queues", "totalProblems": 10, "completedProblems": 0, "problems": [{"id": "valid-parentheses", "title": "Valid Parentheses", "difficulty": "Easy", "link": "https://leetcode.com/problems/valid-parentheses/", "completed": false, "tags": ["String", "<PERSON><PERSON>"]}, {"id": "implement-queue-stacks", "title": "Implement Queue using Stacks", "difficulty": "Easy", "link": "https://leetcode.com/problems/implement-queue-using-stacks/", "completed": false, "tags": ["<PERSON><PERSON>", "Queue", "Design"]}, {"id": "min-stack", "title": "<PERSON>", "difficulty": "Medium", "link": "https://leetcode.com/problems/min-stack/", "completed": false, "tags": ["<PERSON><PERSON>", "Design"]}]}, {"id": "trees", "title": "Binary Trees", "totalProblems": 18, "completedProblems": 0, "problems": [{"id": "invert-binary-tree", "title": "Invert Binary Tree", "difficulty": "Easy", "link": "https://leetcode.com/problems/invert-binary-tree/", "completed": false, "tags": ["Tree", "Depth-First Search"]}, {"id": "maximum-depth-tree", "title": "Maximum Depth of Binary Tree", "difficulty": "Easy", "link": "https://leetcode.com/problems/maximum-depth-of-binary-tree/", "completed": false, "tags": ["Tree", "Depth-First Search"]}, {"id": "same-tree", "title": "Same Tree", "difficulty": "Easy", "link": "https://leetcode.com/problems/same-tree/", "completed": false, "tags": ["Tree", "Depth-First Search"]}, {"id": "subtree-another-tree", "title": "Subtree of Another Tree", "difficulty": "Medium", "link": "https://leetcode.com/problems/subtree-of-another-tree/", "completed": false, "tags": ["Tree", "Depth-First Search"]}]}, {"id": "sorting-searching", "title": "Sorting & Searching", "totalProblems": 8, "completedProblems": 0, "problems": [{"id": "binary-search", "title": "Binary Search", "difficulty": "Easy", "link": "https://leetcode.com/problems/binary-search/", "completed": false, "tags": ["Array", "Binary Search"]}, {"id": "search-rotated-array", "title": "Search in Rotated Sorted Array", "difficulty": "Medium", "link": "https://leetcode.com/problems/search-in-rotated-sorted-array/", "completed": false, "tags": ["Array", "Binary Search"]}, {"id": "find-minimum-rotated", "title": "Find Minimum in Rotated Sorted Array", "difficulty": "Medium", "link": "https://leetcode.com/problems/find-minimum-in-rotated-sorted-array/", "completed": false, "tags": ["Array", "Binary Search"]}]}]}, "offCampus": {"title": "Off Campus - Advanced DSA", "description": "Advanced problems for competitive programming and tech company interviews. Includes complex algorithms and optimization techniques.", "topics": [{"id": "dynamic-programming", "title": "Dynamic Programming", "totalProblems": 25, "completedProblems": 0, "problems": [{"id": "climbing-stairs", "title": "Climbing Stairs", "difficulty": "Easy", "link": "https://leetcode.com/problems/climbing-stairs/", "completed": false, "tags": ["Math", "Dynamic Programming"]}, {"id": "coin-change", "title": "Coin Change", "difficulty": "Medium", "link": "https://leetcode.com/problems/coin-change/", "completed": false, "tags": ["Array", "Dynamic Programming"]}, {"id": "longest-increasing-subsequence", "title": "Longest Increasing Subsequence", "difficulty": "Medium", "link": "https://leetcode.com/problems/longest-increasing-subsequence/", "completed": false, "tags": ["Array", "Dynamic Programming"]}, {"id": "word-break", "title": "Word Break", "difficulty": "Medium", "link": "https://leetcode.com/problems/word-break/", "completed": false, "tags": ["Hash Table", "String", "Dynamic Programming"]}, {"id": "combination-sum", "title": "Combination Sum IV", "difficulty": "Medium", "link": "https://leetcode.com/problems/combination-sum-iv/", "completed": false, "tags": ["Array", "Dynamic Programming"]}]}, {"id": "graphs", "title": "Graph Algorithms", "totalProblems": 20, "completedProblems": 0, "problems": [{"id": "number-of-islands", "title": "Number of Islands", "difficulty": "Medium", "link": "https://leetcode.com/problems/number-of-islands/", "completed": false, "tags": ["Array", "Depth-First Search"]}, {"id": "clone-graph", "title": "<PERSON><PERSON> Graph", "difficulty": "Medium", "link": "https://leetcode.com/problems/clone-graph/", "completed": false, "tags": ["Hash Table", "Depth-First Search", "Graph"]}, {"id": "course-schedule", "title": "Course Schedule", "difficulty": "Medium", "link": "https://leetcode.com/problems/course-schedule/", "completed": false, "tags": ["Depth-First Search", "Graph", "Topological Sort"]}, {"id": "pacific-atlantic", "title": "Pacific Atlantic Water Flow", "difficulty": "Medium", "link": "https://leetcode.com/problems/pacific-atlantic-water-flow/", "completed": false, "tags": ["Array", "Depth-First Search", "Matrix"]}]}, {"id": "advanced-trees", "title": "Advanced Trees", "totalProblems": 15, "completedProblems": 0, "problems": [{"id": "serialize-deserialize-tree", "title": "Serialize and Deserialize Binary Tree", "difficulty": "Hard", "link": "https://leetcode.com/problems/serialize-and-deserialize-binary-tree/", "completed": false, "tags": ["String", "Tree", "Design"]}, {"id": "lowest-common-ancestor", "title": "Lowest Common Ancestor of BST", "difficulty": "Medium", "link": "https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-search-tree/", "completed": false, "tags": ["Tree", "Depth-First Search"]}, {"id": "validate-bst", "title": "Validate Binary Search Tree", "difficulty": "Medium", "link": "https://leetcode.com/problems/validate-binary-search-tree/", "completed": false, "tags": ["Tree", "Depth-First Search"]}]}, {"id": "heap-priority-queue", "title": "Heap / Priority Queue", "totalProblems": 12, "completedProblems": 0, "problems": [{"id": "merge-k-lists", "title": "<PERSON>rge k <PERSON> Lists", "difficulty": "Hard", "link": "https://leetcode.com/problems/merge-k-sorted-lists/", "completed": false, "tags": ["Linked List", "<PERSON><PERSON>"]}, {"id": "top-k-frequent", "title": "Top K Frequent Elements", "difficulty": "Medium", "link": "https://leetcode.com/problems/top-k-frequent-elements/", "completed": false, "tags": ["Array", "Hash Table", "<PERSON><PERSON>"]}, {"id": "find-median-stream", "title": "Find Median from Data Stream", "difficulty": "Hard", "link": "https://leetcode.com/problems/find-median-from-data-stream/", "completed": false, "tags": ["Two Pointers", "Design", "<PERSON><PERSON>"]}]}, {"id": "backtracking", "title": "Backtracking", "totalProblems": 10, "completedProblems": 0, "problems": [{"id": "combination-sum", "title": "Combination Sum", "difficulty": "Medium", "link": "https://leetcode.com/problems/combination-sum/", "completed": false, "tags": ["Array", "Backtracking"]}, {"id": "word-search", "title": "Word Search", "difficulty": "Medium", "link": "https://leetcode.com/problems/word-search/", "completed": false, "tags": ["Array", "Backtracking", "Matrix"]}, {"id": "palindrome-partitioning", "title": "Palindrome Partitioning", "difficulty": "Medium", "link": "https://leetcode.com/problems/palindrome-partitioning/", "completed": false, "tags": ["String", "Dynamic Programming", "Backtracking"]}]}]}}