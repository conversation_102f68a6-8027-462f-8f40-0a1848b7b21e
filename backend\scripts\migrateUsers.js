/**
 * Simple migration script to add XP and streak fields to existing users
 */
import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const migrateUsers = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.DATABASE_URI);
    console.log('Connected to database');

    // Update all users to have the new fields if they don't exist
    const result = await mongoose.connection.db.collection('users').updateMany(
      {}, 
      {
        $set: {
          xp: { $ifNull: ["$xp", 0] },
          streak: { $ifNull: ["$streak", 0] },
          lastQuestionSolvedDate: { $ifNull: ["$lastQuestionSolvedDate", null] }
        }
      }
    );

    console.log(`Migration complete! Updated ${result.modifiedCount} users`);

    // Now update XP based on solved questions
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const Question = mongoose.model('Question', new mongoose.Schema({}, { strict: false }));

    const users = await User.find({});
    console.log(`Calculating XP for ${users.length} users...`);

    let updatedCount = 0;
    for (const user of users) {
      try {
        // Count questions where the user ID is in the userList array
        const problemsSolved = await Question.countDocuments({
          userList: { $in: [user._id] }
        });

        // Calculate XP (10 XP per question solved)
        const calculatedXP = problemsSolved * 10;

        // Update user's XP
        await User.findByIdAndUpdate(user._id, { xp: calculatedXP });

        console.log(`User ${user.username || user.fullName}: ${problemsSolved} problems, ${calculatedXP} XP`);
        updatedCount++;
      } catch (error) {
        console.error(`Error updating user ${user._id}:`, error.message);
      }
    }

    console.log(`XP calculation complete! Updated ${updatedCount} users`);

  } catch (error) {
    console.error('Migration error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Database connection closed');
  }
};

migrateUsers();
