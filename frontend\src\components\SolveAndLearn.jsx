import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import LearningCard from './LearningCard';
import { dsaTopics } from '../data/dsaTopics';
import {
  FaCode,
  FaLayerGroup,
  FaNetworkWired,
  FaTree,
  FaListOl,
  FaRandom,
  FaSearch,
  FaLink,
} from 'react-icons/fa';

// Function to get the icon component based on the icon name
const getIconComponent = (iconName) => {
  switch (iconName) {
    case 'FaCode':
      return <FaCode />;
    case 'FaLayerGroup':
      return <FaLayerGroup />;
    case 'FaNetworkWired':
      return <FaNetworkWired />;
    case 'FaTree':
      return <FaTree />;
    case 'FaListOl':
      return <FaListOl />;
    case 'FaRandom':
      return <FaRandom />;
    case 'FaSearch':
      return <FaSearch />;
    case 'FaLink':
      return <FaLink />;
    default:
      return <FaCode />;
  }
};

const SolveAndLearn = () => {
  const [topicsWithCounts, setTopicsWithCounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch question counts for each topic
  useEffect(() => {
    const fetchTopicCounts = async () => {
      try {
        setLoading(true);
        const topicsWithCountsData = await Promise.all(
          dsaTopics.map(async (topic) => {
            try {
              // Map topic IDs to appropriate tags for database queries
              const tagMapping = {
                'arrays': ['array'],
                'strings': ['string'],
                'dynamic-programming': ['dynamic programming'],
                'graphs': ['graph'],
                'trees': ['tree', 'binary tree', 'binary search tree'],
                'backtracking': ['backtracking'],
                'binary-search': ['binary search'],
                'linked-lists': ['linked list'],
                'stacks-and-queues': ['stack', 'queue']
              };

              const tags = tagMapping[topic.id] || [topic.id.replace('-', ' ')];
              const tagsQuery = tags.join(',');

              const response = await fetch(
                `${import.meta.env.VITE_API_URL}/api/v1/leetcode/questions/by-tags?tags=${encodeURIComponent(tagsQuery)}&limit=100`,
                {
                  method: 'GET',
                  headers: {
                    'Accept': 'application/json',
                  },
                }
              );

              if (response.ok) {
                const result = await response.json();
                return {
                  ...topic,
                  questionCount: result.success ? result.count : 0,
                  hasQuestions: result.success && result.count > 0
                };
              } else {
                console.warn(`Failed to fetch questions for ${topic.title}:`, response.status);
                return {
                  ...topic,
                  questionCount: 0,
                  hasQuestions: false
                };
              }
            } catch (error) {
              console.error(`Error fetching questions for ${topic.title}:`, error);
              return {
                ...topic,
                questionCount: 0,
                hasQuestions: false
              };
            }
          })
        );

        setTopicsWithCounts(topicsWithCountsData);
      } catch (error) {
        console.error('Error fetching topic counts:', error);
        setError('Failed to load topic information');
        // Fallback to static data
        setTopicsWithCounts(dsaTopics.map(topic => ({
          ...topic,
          questionCount: topic.problems?.length || 0,
          hasQuestions: true
        })));
      } finally {
        setLoading(false);
      }
    };

    fetchTopicCounts();
  }, []);

  if (loading) {
    return (
      <section id="solve-and-learn" className="w-full py-16 max-w-7xl">
        <h2 className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">
          Solve and Learn
        </h2>
        <div className="w-full">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(9)].map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-gray-800/50 rounded-xl p-6 h-48">
                  <div className="h-8 bg-gray-700 rounded mb-4"></div>
                  <div className="h-4 bg-gray-700 rounded mb-2"></div>
                  <div className="h-4 bg-gray-700 rounded mb-2"></div>
                  <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="solve-and-learn" className="w-full py-16 max-w-7xl">
      <h2 className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-purple-400 to-pink-600 text-transparent bg-clip-text">
        Solve and Learn
      </h2>
      {error && (
        <div className="text-center mb-6">
          <p className="text-yellow-400 text-sm">{error}</p>
        </div>
      )}
      <div className="w-full">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {topicsWithCounts.map((topic, index) => (
            <Link key={index} to={`/topics/${topic.id}`} className="no-underline">
              <div className="relative">
                <LearningCard
                  title={topic.title}
                  description={topic.description}
                  icon={getIconComponent(topic.icon)}
                />
                {/* Question count badge */}
                <div className="absolute top-3 right-3 bg-purple-600/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full">
                  {topic.questionCount} problems
                </div>
                {/* Indicator for dynamic vs static data */}
                {topic.hasQuestions && topic.questionCount > 0 && (
                  <div className="absolute bottom-3 right-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" title="Dynamic data available"></div>
                  </div>
                )}
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SolveAndLearn;