import { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Eye, EyeOff, CheckCircle, AlertCircle } from 'lucide-react';

const ResetPassword = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [tokenValid, setTokenValid] = useState(true);
  
  const { token } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    // Basic token validation (check if token exists and has reasonable length)
    if (!token || token.length < 10) {
      setTokenValid(false);
      setError('Invalid or missing reset token. Please request a new password reset.');
    }
  }, [token]);

  const validatePassword = (password) => {
    if (password.length < 6) {
      return 'Password must be at least 6 characters long';
    }
    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');

    // Validate passwords
    const passwordError = validatePassword(password);
    if (passwordError) {
      setError(passwordError);
      setLoading(false);
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/reset/${token}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage(data.message);
        setIsSuccess(true);
        // Redirect to sign in after 3 seconds
        setTimeout(() => {
          navigate('/signin');
        }, 3000);
      } else {
        setError(data.message || 'Failed to reset password');
        if (data.message?.includes('token') || data.message?.includes('expired')) {
          setTokenValid(false);
        }
      }
    } catch (error) {
      setError('Network error. Please try again.');
    }

    setLoading(false);
  };

  if (!tokenValid) {
    return (
      <div className="h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-blue-900 flex flex-col overflow-hidden">
        <div className="flex justify-center pt-8 pb-4">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-purple-200 text-transparent bg-clip-text">
            Invalid Reset Link
          </h1>
        </div>

        <div className="flex-1 flex items-center justify-center w-full max-w-2xl mx-auto px-8">
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 shadow-2xl border border-white/20 text-center">
            <div className="flex justify-center mb-6">
              <div className="bg-red-500/20 p-4 rounded-full">
                <AlertCircle size={48} className="text-red-400" />
              </div>
            </div>
            
            <h2 className="text-2xl font-bold text-white mb-4">Reset Link Invalid</h2>
            <p className="text-gray-300 mb-6">
              This password reset link is invalid or has expired. Please request a new password reset.
            </p>
            
            <div className="space-y-4">
              <Link
                to="/forgot-password"
                className="block w-full bg-gradient-to-r from-gray-800 to-gray-900 text-white py-2.5 px-6 rounded-lg font-medium hover:from-gray-700 hover:to-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200 text-center"
              >
                Request New Reset Link
              </Link>
              
              <Link
                to="/signin"
                className="block w-full border border-white/30 text-white py-2.5 px-6 rounded-lg font-medium hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/50 transition-all duration-200 text-center"
              >
                Back to Sign In
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-blue-900 flex flex-col overflow-hidden">
      {/* Welcome Header */}
      <div className="flex justify-center pt-8 pb-4">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-purple-200 text-transparent bg-clip-text">
          Reset Your Password
        </h1>
      </div>

      {/* Main Container */}
      <div className="flex-1 flex items-center justify-center w-full max-w-6xl mx-auto px-8 gap-16">
        {/* Left Side - Illustration */}
        <div className="hidden lg:flex flex-1 items-center justify-center">
          <div className="w-96 h-96 flex items-center justify-center">
            <img
              src="/side-hero.svg"
              alt="Reset Password Illustration"
              className="w-full h-full object-contain"
            />
          </div>
        </div>

        {/* Right Side - Reset Password Form */}
        <div className="flex-1 max-w-md">
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 shadow-2xl border border-white/20">
            {/* Back to Sign In Link */}
            <Link
              to="/signin"
              className="flex items-center text-gray-300 hover:text-white transition-colors duration-200 mb-6"
            >
              <ArrowLeft size={20} className="mr-2" />
              Back to Sign In
            </Link>

            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Create New Password</h2>
              <p className="text-gray-300 text-sm">
                Enter your new password below to complete the reset process.
              </p>
            </div>

            {/* Success Message */}
            {isSuccess && (
              <div className="mb-4 p-4 rounded-lg bg-green-500/20 border border-green-500/30 text-green-300 text-sm">
                <div className="flex items-center">
                  <CheckCircle size={16} className="mr-2 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Password reset successful!</p>
                    <p className="mt-1">{message}</p>
                    <p className="mt-2 text-xs">Redirecting to sign in page...</p>
                  </div>
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="mb-4 p-3 rounded-lg bg-red-500/20 border border-red-500/30 text-red-300 text-sm">
                {error}
              </div>
            )}

            {/* Form */}
            {!isSuccess && (
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* New Password Field */}
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
                    New Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      id="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="w-full px-4 py-2.5 pr-12 rounded-lg bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-200"
                      placeholder="Enter new password"
                      minLength="6"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-300 hover:text-white transition-colors duration-200"
                    >
                      {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                </div>

                {/* Confirm Password Field */}
                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300 mb-1">
                    Confirm New Password
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      id="confirmPassword"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      className="w-full px-4 py-2.5 pr-12 rounded-lg bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-200"
                      placeholder="Confirm new password"
                      minLength="6"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-300 hover:text-white transition-colors duration-200"
                    >
                      {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                </div>

                {/* Password Requirements */}
                <div className="text-xs text-gray-400 bg-white/5 p-3 rounded-lg">
                  <p className="font-medium mb-1">Password requirements:</p>
                  <ul className="space-y-1">
                    <li className={`flex items-center ${password.length >= 6 ? 'text-green-400' : 'text-gray-400'}`}>
                      <span className="mr-2">{password.length >= 6 ? '✓' : '•'}</span>
                      At least 6 characters long
                    </li>
                    <li className={`flex items-center ${password === confirmPassword && password !== '' ? 'text-green-400' : 'text-gray-400'}`}>
                      <span className="mr-2">{password === confirmPassword && password !== '' ? '✓' : '•'}</span>
                      Passwords match
                    </li>
                  </ul>
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={loading || !password || !confirmPassword}
                  className="w-full bg-gradient-to-r from-gray-800 to-gray-900 text-white py-2.5 px-6 rounded-lg font-medium hover:from-gray-700 hover:to-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Resetting Password...
                    </div>
                  ) : (
                    'Reset Password'
                  )}
                </button>
              </form>
            )}

            {/* Sign Up Link */}
            <div className="mt-6 text-center">
              <p className="text-gray-300">
                Don't have an account?{' '}
                <Link
                  to="/signup"
                  className="text-white hover:text-gray-200 font-medium transition-colors duration-200 underline"
                >
                  Sign up here
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
