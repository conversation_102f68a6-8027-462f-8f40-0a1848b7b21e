/**
 * Questions routes
 * Defines routes for questions-related endpoints
 */
import express from 'express';
import { getAllQuestions, getQuestionsByTags } from '../controllers/questions.controller.js';

const router = express.Router();

/**
 * @route   GET /api/v1/leetcode/questions
 * @desc    Get all LeetCode questions
 * @access  Public
 */
router.get('/', getAllQuestions);

/**
 * @route   GET /api/v1/leetcode/questions/by-tags
 * @desc    Get questions filtered by tags for DSA topics
 * @access  Public
 */
router.get('/by-tags', getQuestionsByTags);

export default router;
