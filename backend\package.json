{"name": "bigo_backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "lint": "eslint .", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/AK-shat-JAIN/big0_backend.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/AK-shat-JAIN/big0_backend/issues"}, "homepage": "https://github.com/AK-shat-JAIN/big0_backend#readme", "description": "Merged backend for BigO website with LeetCode API integration", "keywords": ["leetcode", "api", "node", "express", "bigo"], "dependencies": {"axios": "^1.6.7", "bcryptjs": "^2.4.3", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "email-validation": "^0.1.2", "express": "^4.21.2", "express-async-handler": "^1.2.0", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "joi": "^17.12.1", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "mongoose": "^8.9.5", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.16", "nodemon": "^3.1.9", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.5"}}