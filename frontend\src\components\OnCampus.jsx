import { useState, useEffect, useRef } from "react";
import {
  ChevronDown,
  ChevronRight,
  ExternalLink,
  CheckCircle,
  Circle,
  ArrowLeft,
  Youtube,
  Eye,
  Code,
} from "lucide-react";
import { Link } from "react-router-dom";
import CodeEditorForm from "./CodeEditorForm";
import SheetProblemLeaderboardModal from "./SheetProblemLeaderboardModal";
import { useAuth } from "../contexts/AuthContext";

const OnCampus = () => {
  const [openAccordions, setOpenAccordions] = useState(new Set());
  const [completedProblems, setCompletedProblems] = useState(new Set());
  const [data, setData] = useState(null);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [selectedProblem, setSelectedProblem] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [acceptedSolutions, setAcceptedSolutions] = useState([]);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [isDataLoaded, setIsDataLoaded] = useState(false); // Track if data is already loaded
  const [showLeaderboard, setShowLeaderboard] = useState(false);
  const [selectedLeaderboardProblem, setSelectedLeaderboardProblem] =
    useState(null);
  const [leaderboardSolutions, setLeaderboardSolutions] = useState([]);
  const [loadingLeaderboard, setLoadingLeaderboard] = useState(false);
  const [showCoinAnimation, setShowCoinAnimation] = useState(false);
  const { isAuthenticated, user, refreshUser, updateUserData } = useAuth();
  const hasLoadedUserProgress = useRef(false); // Track if user progress has been loaded

  useEffect(() => {
    // Only fetch if data hasn't been loaded yet
    if (!isDataLoaded) {
      fetchSheetQuestions();
    }
  }, [isDataLoaded]);

  // Fetch user-specific completion status from database
  const fetchUserCompletionStatus = async (sheetData) => {
    if (!isAuthenticated() || !user) return;

    try {
      // console.log("Fetching user completion status for user:", user._id);
      // Get token from localStorage or auth context
      const token = localStorage.getItem("token") || user?.token;

      // Get user ID as fallback
      const userId = user?._id || user?.id;

      // console.log(
      //   "Using auth token for API request:",
      //   token ? "Token found" : "No token available"
      // );
      // console.log("User ID for fallback:", userId);

      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/v1/leetcode/sheet/user-questions`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
            Authorization: token ? `Bearer ${token}` : "",
            "X-User-ID": userId || "", // Add user ID as custom header for fallback
            "X-Auth-Token": token || "", // Add token as custom header (some servers need this format)
          },
          credentials: "include",
        }
      );

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.questions) {
          // console.log("Fetched user solved questions:", result.questions);

          // Create set of completed problem IDs based on titles from database
          const userCompleted = new Set();

          // Match solved questions with current sheet problems by title
          if (sheetData && sheetData.topics) {
            sheetData.topics.forEach((topic) => {
              topic.problems.forEach((problem) => {
                const isSolved = result.questions.some(
                  (solved) => solved.title === problem.title
                );
                if (isSolved) {
                  userCompleted.add(problem.id);
                }
              });
            });
          }

          setCompletedProblems(userCompleted);

          // Update localStorage with user-specific data
          const userStorageKey = `onCampusCompleted_${user._id}`;
          localStorage.setItem(
            userStorageKey,
            JSON.stringify([...userCompleted])
          );

          // console.log(
          //   `Set ${userCompleted.size} completed problems for user ${user._id}`
          // );
        } else {
          // console.log("No solved questions found for user, starting fresh");
          setCompletedProblems(new Set());
          const userStorageKey = `onCampusCompleted_${user._id}`;
          localStorage.setItem(userStorageKey, JSON.stringify([]));
        }
      } else {
        console.error(
          "Failed to fetch user completion status:",
          response.status
        );
        // Fallback to empty state for this user
        setCompletedProblems(new Set());
      }
    } catch (error) {
      console.error("Error fetching user completion status:", error);
      // Fallback to empty state for this user
      setCompletedProblems(new Set());
    }
  };

  // Watch for authentication changes to load/reset user progress
  useEffect(() => {
    if (isDataLoaded && data) {
      const currentUserId = user?._id;

      if (
        isAuthenticated() &&
        currentUserId &&
        !hasLoadedUserProgress.current
      ) {
        // User logged in and we haven't loaded their progress yet
        // console.log("Loading user progress for first time:", currentUserId);
        hasLoadedUserProgress.current = true;
        fetchUserCompletionStatus(data);
      } else if (!isAuthenticated() && hasLoadedUserProgress.current) {
        // User logged out - reset all completion status
        // console.log("User logged out, resetting progress");
        hasLoadedUserProgress.current = false;
        resetCompletionStatus();
      }
    }
  }, [isAuthenticated, user?._id, isDataLoaded]);

  // Reset completion status when user logs out
  const resetCompletionStatus = () => {
    // console.log("Resetting completion status - user logged out");
    setCompletedProblems(new Set());
    hasLoadedUserProgress.current = false; // Reset the ref

    // Clear any existing user-specific localStorage data
    // Note: We don't clear all localStorage, just set current to empty
    const keys = Object.keys(localStorage);
    keys.forEach((key) => {
      if (key.startsWith("onCampusCompleted_")) {
        // Keep user-specific data but don't load it
      }
    });

    // Set generic key to empty (for non-authenticated state)
    localStorage.setItem("onCampusCompleted", JSON.stringify([]));
  };

  // Fetch sheet questions organized by topic from database
  const fetchSheetQuestions = async () => {
    // Prevent multiple calls if data is already being loaded or loaded
    if (isDataLoaded) return;

    try {
      const response = await fetch(
        `${
          import.meta.env.VITE_API_URL
        }/api/v1/leetcode/sheet/questions-by-topic`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
          },
          credentials: "include", // Include cookies for authentication (optional)
        }
      );

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // console.log("Fetched sheet questions from database:", result.data);
          setData(result.data);
          setIsDataLoaded(true); // Mark data as loaded

          // Reset progress loading state when new data is loaded
          hasLoadedUserProgress.current = false;

          // Don't set completion status here - it will be handled by useEffect watching auth state
          // Initialize with empty set - user-specific data will be loaded if authenticated
          setCompletedProblems(new Set());
        } else {
          console.error("Failed to fetch sheet questions:", result.message);
          // Fallback to empty state
          setData({
            title: "On Campus - DSA Preparation",
            description: "No questions available in database yet.",
            topics: [],
          });
          setIsDataLoaded(true); // Mark as loaded even if empty
        }
      } else {
        console.error("Failed to fetch sheet questions:", response.status);
        // Fallback to empty state
        setData({
          title: "On Campus - DSA Preparation",
          description: "Unable to load questions. Please try again later.",
          topics: [],
        });
        setIsDataLoaded(true); // Mark as loaded even if failed
      }
    } catch (error) {
      console.error("Error fetching sheet questions:", error);
      // Fallback to empty state
      setData({
        title: "On Campus - DSA Preparation",
        description: "Unable to load questions. Please check your connection.",
        topics: [],
      });
      setIsDataLoaded(true); // Mark as loaded even if error occurred
    }
  };

  // Cleanup effect to restore scrolling when component unmounts or modal closes
  useEffect(() => {
    return () => {
      // Restore scrolling when component unmounts
      document.body.style.overflow = "unset";
    };
  }, []);

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscapeKey = (event) => {
      if (event.key === "Escape" && isUploadModalOpen) {
        handleCloseModal();
      }
    };

    if (isUploadModalOpen) {
      document.addEventListener("keydown", handleEscapeKey);
      return () => {
        document.removeEventListener("keydown", handleEscapeKey);
      };
    }
  }, [isUploadModalOpen]);

  const toggleAccordion = (topicId) => {
    const newOpen = new Set(openAccordions);
    if (newOpen.has(topicId)) {
      newOpen.delete(topicId);
    } else {
      newOpen.add(topicId);
    }
    setOpenAccordions(newOpen);
  };

  // const toggleProblemCompletion = async (problem) => {
  //   // Check if user is authenticated
  //   if (!isAuthenticated()) {
  //     setShowLoginPrompt(true);
  //     return;
  //   }

  //   try {
  //     // Call backend API to toggle question status
  //     const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/leetcode/sheet/toggleq`, {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //         'Accept': 'application/json'
  //       },
  //       credentials: 'include', // Include cookies for authentication
  //       body: JSON.stringify({
  //         userId: user?._id, // Add user ID as fallback
  //         title: problem.title,
  //         questionLink: problem.link,
  //         difficulty: problem.difficulty,
  //         tags: problem.tags || []
  //       })
  //     });

  //     if (!response.ok) {
  //       const errorData = await response.json();
  //       throw new Error(errorData.message || 'Failed to update question status');
  //     }

  //     const result = await response.json();

  //     // Update local state based on backend response
  //     const newCompleted = new Set(completedProblems);
  //     if (result.solved) {
  //       newCompleted.add(problem.id);
  //     } else {
  //       newCompleted.delete(problem.id);
  //     }

  //     setCompletedProblems(newCompleted);

  //     // Save to user-specific localStorage key
  //     if (user && user._id) {
  //       const userStorageKey = `onCampusCompleted_${user._id}`;
  //       localStorage.setItem(userStorageKey, JSON.stringify([...newCompleted]));
  //     }

  //     console.log(`Question ${result.solved ? 'marked as solved' : 'unmarked'} for user ${user._id}:`, problem.title);

  //   } catch (error) {
  //     console.error('Error toggling problem completion:', error);
  //     // Show error message to user (you can implement a toast notification here)
  //     alert(error.message || 'Failed to update question status. Please try again.');
  //   }
  // };

  const toggleProblemCompletion = async (problem) => {
    // Check if user is authenticated
    if (!isAuthenticated()) {
      setShowLoginPrompt(true);
      return;
    }

    // Optimistically update UI
    const isCompleted = completedProblems.has(problem.id);
    const newCompleted = new Set(completedProblems);
    if (isCompleted) {
      newCompleted.delete(problem.id);
    } else {
      newCompleted.add(problem.id);
    }
    setCompletedProblems(newCompleted);

    // Save to user-specific localStorage key
    if (user && user._id) {
      const userStorageKey = `onCampusCompleted_${user._id}`;
      localStorage.setItem(userStorageKey, JSON.stringify([...newCompleted]));
    }

    try {
      // Call backend API to toggle question status
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/v1/leetcode/sheet/toggleq`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          credentials: "include",
          body: JSON.stringify({
            userId: user?._id,
            title: problem.title,
            questionLink: problem.link,
            difficulty: problem.difficulty,
            tags: problem.tags || [],
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Failed to update question status"
        );
      }

      // Optionally, you can check backend result and revert if needed
      // const result = await response.json();
      // if (result.solved !== !isCompleted) {
      //   // Backend disagrees, revert UI
      //   setCompletedProblems(completedProblems);
      // }
    } catch (error) {
      // Revert optimistic update on error
      setCompletedProblems(completedProblems);
      alert(
        error.message || "Failed to update question status. Please try again."
      );
    }
  };
  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case "easy":
        return "text-green-400 bg-green-400/10 border-green-400/20";
      case "medium":
        return "text-orange-400 bg-orange-400/10 border-orange-400/20";
      case "hard":
        return "text-red-400 bg-red-400/10 border-red-400/20";
      default:
        return "text-gray-400 bg-gray-400/10 border-gray-400/20";
    }
  };

  // Handle YouTube search URL generation (fallback when solutionLink is not available)
  const getYouTubeSearchUrl = (problemTitle) => {
    const searchQuery = `${problemTitle} leetcode solution explanation`;
    return `https://www.youtube.com/results?search_query=${encodeURIComponent(
      searchQuery
    )}`;
  };

  const getCompletedCount = (topicProblems) => {
    return topicProblems.filter((problem) => completedProblems.has(problem.id))
      .length;
  };

  const getProgressPercentage = (topicProblems) => {
    const completed = getCompletedCount(topicProblems);
    const total = topicProblems.length;
    return total > 0 ? (completed / total) * 100 : 0;
  };

  // Convert problem data to challengeData format for modal
  const convertProblemToChallengeData = (problem) => {
    const leetcodeUrl = problem.link;
    // Use title slug as questionId temporarily - this will be updated when problemDetails are fetched
    const titleSlug =
      leetcodeUrl.split("/problems/")[1]?.split("/")[0] || "unknown";

    return {
      id: problem.id, // Preserve original problem ID for completion tracking
      questionId: titleSlug, // This will be updated in CodeEditorForm when problemDetails are fetched
      questionTitle: problem.title,
      questionLink: leetcodeUrl,
      difficulty: problem.difficulty,
      topicTags: problem.tags || [],
      codeSnippets: [], // Will be populated by the modal if needed
    };
  };

  // Handle write solution button click
  const handleWriteSolution = (problem) => {
    if (!isAuthenticated()) {
      setShowLoginPrompt(true);
      return;
    }

    // Prevent background scrolling when modal opens
    document.body.style.overflow = "hidden";

    setSelectedProblem(convertProblemToChallengeData(problem));
    setIsUploadModalOpen(true);
    setSubmissionResult(null);
  };

  // Handle close modal
  const handleCloseModal = () => {
    if (window.submissionPollingTimeout) {
      clearTimeout(window.submissionPollingTimeout);
      window.submissionPollingTimeout = null;
    }

    // Restore background scrolling when modal closes
    document.body.style.overflow = "unset";

    setIsUploadModalOpen(false);
    setSelectedProblem(null);
    setSubmissionResult(null);
  };

  // View solutions - show leaderboard modal
  const handleViewSolutions = async (problem) => {
    setSelectedLeaderboardProblem(problem);
    setLoadingLeaderboard(true);
    setShowLeaderboard(true);

    try {
      // console.log("Fetching solutions for problem:", problem.title);
      const response = await fetch(
        `${
          import.meta.env.VITE_API_URL
        }/api/v1/leetcode/sheet/problem-solutions/${encodeURIComponent(
          problem.title
        )}`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
          },
          credentials: "include",
        }
      );

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // console.log(
          //   `Fetched ${result.count} solutions for ${problem.title}:`,
          //   result.solutions
          // );
          setLeaderboardSolutions(result.solutions || []);
        } else {
          console.error("Failed to fetch solutions:", result.message);
          setLeaderboardSolutions([]);
        }
      } else {
        console.error("Failed to fetch solutions:", response.status);
        setLeaderboardSolutions([]);
      }
    } catch (error) {
      console.error("Error fetching solutions:", error);
      setLeaderboardSolutions([]);
    } finally {
      setLoadingLeaderboard(false);
    }
  };

  const handleCloseLeaderboard = () => {
    setShowLeaderboard(false);
    setSelectedLeaderboardProblem(null);
    setLeaderboardSolutions([]);
  };

  // Function to save solution to database
  const saveSolutionToDatabase = async ({
    submissionId,
    username,
    code,
    language,
    approach,
    data,
    challengeData,
  }) => {
    try {
      const titleSlug = challengeData.questionLink
        .split("/problems/")[1]
        ?.split("/")[0];

      const solutionData = {
        problemTitle: challengeData.questionTitle,
        problemSlug: titleSlug,
        difficulty: challengeData.difficulty,
        code,
        language,
        approach: approach || "",
        status: data.status_msg || "Accepted",
        topicTags: challengeData.topicTags || [],
        userId: user?._id,
        isSheetQuestion: true, // Flag to identify this as a sheet question
      };

      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/v1/leetcode/aftersubmit`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          credentials: "include",
          body: JSON.stringify(solutionData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to save solution");
      }

      const result = await response.json();
      // console.log("Solution saved to database:", result);

      if (result.isDuplicate) {
        return {
          ...result,
          warningMessage:
            result.warning ||
            "This solution appears to match an existing submission. Please try solving with your own unique approach for better learning.",
        };
      }

      return result;
    } catch (error) {
      console.error("Error saving solution to database:", error);
      throw error;
    }
  };

  // Handle submit solution (matching Daily Challenge Card functionality)
  const handleSubmitSolution = async ({
    username,
    code,
    language,
    approach,
  }) => {
    if (window.submissionPollingTimeout) {
      clearTimeout(window.submissionPollingTimeout);
      window.submissionPollingTimeout = null;
    }

    setIsSubmitting(true);
    setSubmissionResult({
      loading: true,
      message: "Submitting solution...",
      details: "Please wait while we submit your solution ",
    });

    try {
      const titleSlug = selectedProblem.questionLink
        .split("/problems/")[1]
        ?.split("/")[0];

      if (!titleSlug) {
        throw new Error("Could not determine problem slug from question link");
      }

      const payload = {
        lang: language,
        question_id:
          selectedProblem.realQuestionId || selectedProblem.questionId,
        typed_code: code,
      };

      setSubmissionResult({
        loading: true,
        message: "Submitting solution ...",
        details: "Your solution is being submitted to the judge.",
      });

      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/v1/leetcode/submit/${titleSlug}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to submit solution (${response.status})`);
      }

      const data = await response.json();

      // Extract submission ID from response
      const submissionId =
        data.submission_id ||
        data.submission?.id ||
        response.headers.get("x-submission-id");

      if (!submissionId) {
        throw new Error("No submission ID received from server");
      }

      // Start checking submission status
      setSubmissionResult({
        loading: true,
        message: "Checking submission status...",
      });

      checkSubmissionStatus(submissionId, username, code, language, approach);
    } catch (error) {
      console.error("Error submitting solution:", error);
      setSubmissionResult({
        success: false,
        message:
          error.message || "An error occurred while submitting your solution",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const checkSubmissionStatus = async (
    submissionId,
    username,
    code,
    language,
    approach,
    attempt = 1
  ) => {
    const maxAttempts = 10;
    const pollInterval = 2000;

    try {
      // Update loading state
      setSubmissionResult({
        loading: true,
        message: `Checking submission status... (attempt ${attempt}/${maxAttempts})`,
        details: "Please wait while we check your solution.",
      });

      // Check if we've exceeded max attempts
      if (attempt > maxAttempts) {
        setSubmissionResult({
          success: false,
          message: "Submission timeout",
          details:
            "The submission is taking longer than expected. Please check your account for the result.",
        });
        return;
      }

      // Check submission status
      const response = await fetch(
        `${
          import.meta.env.VITE_API_URL
        }/api/v1/leetcode/submissions/${submissionId}/check`,
        {
          headers: { Accept: "application/json" },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to check submission status (${response.status})`
        );
      }

      const data = await response.json();

      // Handle completed submission
      if (data.state === "SUCCESS") {
        clearTimeout(window.submissionPollingTimeout);

        const isAccepted =
          data.status_code === 10 ||
          (data.status_msg === "Accepted" && data.run_success === true);

        if (isAccepted) {
          // Format success result
          const resultDetails = [
            `Status: ${data.status_msg || "Accepted"}`,
            `Runtime: ${data.status_runtime || "N/A"}`,
            `Memory: ${data.status_memory || "N/A"}`,
            `Runtime Percentile: ${
              data.runtime_percentile?.toFixed(2) || "N/A"
            }%`,
            `Memory Percentile: ${
              data.memory_percentile?.toFixed(2) || "N/A"
            }%`,
            `Test Cases: ${data.total_correct || "N/A"}/${
              data.total_testcases || "N/A"
            }`,
          ].join("\n");

          setSubmissionResult({
            success: true,
            message:
              "Solution accepted! Your solution has been added to the community solutions.",
            details: resultDetails,
            fullData: data,
          });

          // Trigger coin animation for successful submission
          setShowCoinAnimation(true);
          setTimeout(() => setShowCoinAnimation(false), 3000);

          // Mark the current problem as completed
          if (selectedProblem && selectedProblem.id) {
            const newCompleted = new Set(completedProblems);
            newCompleted.add(selectedProblem.id);
            setCompletedProblems(newCompleted);

            // Save to user-specific localStorage key
            if (user && user._id) {
              const userStorageKey = `onCampusCompleted_${user._id}`;
              localStorage.setItem(
                userStorageKey,
                JSON.stringify([...newCompleted])
              );
            }
          }

          // Save to database if authenticated
          if (isAuthenticated()) {
            try {
              const saveResult = await saveSolutionToDatabase({
                submissionId,
                username,
                code,
                language,
                approach,
                data,
                challengeData: selectedProblem,
              });

              if (saveResult.warningMessage) {
                setSubmissionResult((prev) => ({
                  ...prev,
                  details: `${resultDetails}\n\n⚠️ ${saveResult.warningMessage}`,
                  warning: saveResult.warningMessage,
                }));
              }

              if (saveResult.updatedUser) {
                updateUserData(saveResult.updatedUser);
              }
              if (refreshUser) {
                await refreshUser();
              }
            } catch (error) {
              console.error("Error saving solution:", error);
            }
          }
        } else {
          // Format rejection result
          let errorDetails = `Error: ${data.status_msg}`;
          if (data.compile_error)
            errorDetails += `\n\nCompile Error:\n${data.compile_error}`;
          if (data.runtime_error)
            errorDetails += `\n\nRuntime Error:\n${data.runtime_error}`;
          if (data.last_testcase)
            errorDetails += `\n\nFailed Test Case:\n${data.last_testcase}`;
          if (data.total_correct && data.total_testcases) {
            errorDetails += `\n\nTest Cases: ${data.total_correct}/${data.total_testcases}`;
          }

          setSubmissionResult({
            success: false,
            message: "Solution rejected",
            details: errorDetails,
            fullData: data,
          });
        }
        return;
      }

      // Handle still processing states
      if (data.state === "PENDING" || data.state === "STARTED") {
        const stateMessages = {
          PENDING: "Solution is waiting to be evaluated...",
          STARTED: "Solution evaluation has started...",
        };

        setSubmissionResult({
          loading: true,
          message:
            stateMessages[data.state] || "Solution is being evaluated...",
          details: "Your solution is being processed by the judge.",
        });

        // Continue polling
        window.submissionPollingTimeout = setTimeout(
          () =>
            checkSubmissionStatus(
              submissionId,
              username,
              code,
              language,
              approach,
              attempt + 1
            ),
          pollInterval
        );
        return;
      }

      // Handle error state
      if (data.state === "ERROR") {
        setSubmissionResult({
          success: false,
          message: "Error evaluating solution",
          details:
            data.status_msg || "There was an error evaluating your solution.",
        });
        return;
      }

      // Handle unknown states - continue polling for minimal response objects
      if (Object.keys(data).length <= 2 || !data.state) {
        setSubmissionResult({
          loading: true,
          message: "Waiting for evaluation to begin...",
          details:
            "Your solution has been submitted and is waiting to be processed.",
        });

        window.submissionPollingTimeout = setTimeout(
          () =>
            checkSubmissionStatus(
              submissionId,
              username,
              code,
              language,
              approach,
              attempt + 1
            ),
          pollInterval
        );
        return;
      }

      // Handle completely unknown states
      setSubmissionResult({
        success: false,
        message: "Unknown submission state",
        details:
          "The submission returned an unknown state. Please check your account for the result.",
        fullData: data,
      });
    } catch (error) {
      console.error("Error checking submission status:", error);

      // Retry on error up to max attempts
      if (attempt < maxAttempts) {
        window.submissionPollingTimeout = setTimeout(
          () =>
            checkSubmissionStatus(
              submissionId,
              username,
              code,
              language,
              approach,
              attempt + 1
            ),
          2000
        );
      } else {
        setSubmissionResult({
          success: false,
          message: "Error checking submission",
          details:
            error.message || "An error occurred while checking your submission",
        });
      }
    }
  };

  if (!data) {
    return (
      <div className="min-h-screen px-4 sm:px-8 md:px-12 lg:px-16 xl:px-20 bg-gradient-to-r from-[#54206A] to-[#241641] flex items-center justify-center">
        <div className="flex-col items-center text-center justify-center max-w-md w-full">
          {/* Loading Animation */}
          <div className="relative mb-6 sm:mb-8">
            <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto relative">
              <div className="absolute inset-0 rounded-full border-3 sm:border-4 border-purple-800/30"></div>
              <div className="absolute inset-0 rounded-full border-3 sm:border-4 border-transparent border-t-purple-400 animate-spin"></div>
              <div className="absolute inset-2 rounded-full border-2 border-transparent border-t-pink-400 animate-spin animation-delay-150"></div>
            </div>
          </div>

          {/* Loading Text */}
          <h2 className="text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4">
            Loading On Campus Sheet
          </h2>
          <p className="text-sm sm:text-base text-purple-200 mb-6 sm:mb-8">
            Fetching problems from database...
          </p>

          {/* Animated Progress Bar */}
          <div className="justify-center bg-gray-800/50 rounded-full h-1.5 sm:h-2 mb-3 sm:mb-4 overflow-hidden">
            <div className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse loading-bar"></div>
          </div>

          {/* Loading Steps */}
          <div className="text-xs sm:text-sm text-gray-400 space-y-1.5 sm:space-y-2">
            <div className="flex items-center justify-center gap-2">
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-400 rounded-full animate-pulse"></div>
              <span>Connecting to database</span>
            </div>
            <div className="flex items-center justify-center gap-2">
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-400 rounded-full animate-pulse animation-delay-300"></div>
              <span>Loading problem sets</span>
            </div>
            <div className="flex items-center justify-center gap-2">
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-400 rounded-full animate-pulse animation-delay-600"></div>
              <span>Organizing by topics</span>
            </div>
          </div>
        </div>

        <style jsx>{`
          .loading-bar {
            animation: loading-progress 2s ease-in-out infinite;
          }

          @keyframes loading-progress {
            0% {
              width: 0%;
              opacity: 0.5;
            }
            50% {
              width: 70%;
              opacity: 1;
            }
            100% {
              width: 100%;
              opacity: 0.8;
            }
          }

          .animation-delay-150 {
            animation-delay: 150ms;
          }

          .animation-delay-300 {
            animation-delay: 300ms;
          }

          .animation-delay-600 {
            animation-delay: 600ms;
          }
        `}</style>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-r px-4 sm:px-8 md:px-12 lg:px-16 xl:px-20 py-4 sm:py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <Link
            to="/sheets"
            className="inline-flex items-center gap-2 text-purple-300 hover:text-white transition-all duration-200 mb-4 sm:mb-6 bg-purple-900/20 hover:bg-purple-800/30 backdrop-blur-sm border border-purple-500/20 hover:border-purple-400/40 px-3 sm:px-4 py-2 rounded-lg cursor-pointer text-sm sm:text-base"
          >
            <ArrowLeft size={16} className="sm:hidden" />
            <ArrowLeft size={20} className="hidden sm:block" />
            <span className="hidden sm:inline">Back to Sheets</span>
            <span className="sm:hidden">Back</span>
          </Link>

          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4">
            {data.title}
          </h1>

          <div className="relative p-4 sm:p-6 rounded-xl sm:rounded-2xl bg-gradient-to-br from-purple-900/30 to-transparent backdrop-blur-sm border border-purple-500/20">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent rounded-xl sm:rounded-2xl blur-[40px]"></div>
            <p className="relative text-gray-300 text-sm sm:text-base lg:text-lg leading-relaxed z-10">
              {data.description}
            </p>
          </div>
        </div>

        {/* Overall Progress */}
        <div className="mb-6 sm:mb-8">
          <div className="bg-purple-900/30 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-purple-500/20">
            <h2 className="text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4">
              Overall Progress
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              <div className="text-center p-3 sm:p-4 bg-purple-800/20 rounded-lg">
                <div className="text-2xl sm:text-3xl font-bold text-purple-400">
                  {completedProblems.size}
                </div>
                <div className="text-sm sm:text-base text-gray-400">
                  Problems Solved
                </div>
              </div>
              <div className="text-center p-3 sm:p-4 bg-blue-800/20 rounded-lg">
                <div className="text-2xl sm:text-3xl font-bold text-blue-400">
                  {data.topics
                    ? data.topics.reduce(
                        (acc, topic) => acc + topic.problems.length,
                        0
                      )
                    : 0}
                </div>
                <div className="text-sm sm:text-base text-gray-400">
                  Total Problems
                </div>
              </div>
              <div className="text-center p-3 sm:p-4 bg-green-800/20 rounded-lg sm:col-span-2 lg:col-span-1">
                <div className="text-2xl sm:text-3xl font-bold text-green-400">
                  {data.topics ? data.topics.length : 0}
                </div>
                <div className="text-sm sm:text-base text-gray-400">Topics</div>
              </div>
            </div>
          </div>
        </div>

        {/* Topics Accordion */}
        <div className="space-y-2 sm:space-y-3">
          {data.topics && data.topics.length > 0 ? (
            data.topics.map((topic) => {
              const isOpen = openAccordions.has(topic.id);
              const completedCount = getCompletedCount(topic.problems);
              const progressPercentage = getProgressPercentage(topic.problems);

              return (
                <div
                  key={topic.id}
                  className="bg-gray-900/60 backdrop-blur-sm rounded-lg sm:rounded-xl border border-purple-500/20 overflow-hidden"
                >
                  {/* Accordion Header */}
                  <button
                    onClick={() => toggleAccordion(topic.id)}
                    className="w-full p-3 sm:p-4 text-left hover:bg-purple-800/20 transition-colors duration-200 flex items-center justify-between"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 sm:gap-3 mb-1 flex-wrap">
                        <h3 className="text-base sm:text-lg font-bold text-white truncate">
                          {topic.title}
                        </h3>
                        <span className="text-xs text-purple-300 bg-purple-800/30 px-2 py-1 rounded-full whitespace-nowrap">
                          {completedCount}/{topic.problems.length}
                        </span>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-gray-700/50 rounded-full h-1.5 mb-1">
                        <div
                          className="bg-gradient-to-r from-purple-500 to-pink-500 h-1.5 rounded-full transition-all duration-500"
                          style={{ width: `${progressPercentage}%` }}
                        />
                      </div>

                      <div className="text-xs text-gray-400">
                        {Math.round(progressPercentage)}% Complete
                      </div>
                    </div>

                    <div className="text-purple-400 ml-2 flex-shrink-0">
                      {isOpen ? (
                        <ChevronDown size={20} />
                      ) : (
                        <ChevronRight size={20} />
                      )}
                    </div>
                  </button>

                  {/* Accordion Content */}
                  {isOpen && (
                    <div className="border-t border-purple-500/20">
                      <div className="p-3 sm:p-4 max-h-96 overflow-y-auto custom-scrollbar space-y-2 sm:space-y-3">
                        {topic.problems.map((problem) => {
                          const isCompleted = completedProblems.has(problem.id);
                          const questionLink = problem.link;
                          //if the link prefix is "https://leetcode.com/", extract the text leetcode
                          const leetcodePrefix = "https://leetcode.com/";
                          const isLeetCodeProblem =
                            questionLink.startsWith(leetcodePrefix);
                          return (
                            <div
                              key={problem.id}
                              className={`p-3 sm:p-4 rounded-lg border transition-all duration-200 ${
                                isCompleted
                                  ? "bg-green-900/20 border-green-500/30"
                                  : "bg-gray-800/50 border-gray-600/30 hover:border-purple-500/50"
                              }`}
                            >
                              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                                <div className="flex items-center gap-3 flex-1 min-w-0">
                                  <button
                                    onClick={() =>
                                      toggleProblemCompletion(problem)
                                    }
                                    className={`transition-colors duration-200 cursor-pointer flex-shrink-0 ${
                                      isCompleted
                                        ? "text-green-400"
                                        : "text-gray-400 hover:text-purple-400"
                                    }`}
                                    title={
                                      isCompleted
                                        ? "Mark as incomplete"
                                        : "Mark as complete"
                                    }
                                  >
                                    {isCompleted ? (
                                      <CheckCircle size={20} />
                                    ) : (
                                      <Circle size={20} />
                                    )}
                                  </button>

                                  <div className="flex-1 min-w-0">
                                    <h4
                                      className={`font-medium text-sm sm:text-base transition-colors duration-200 ${
                                        isCompleted
                                          ? "text-green-400 line-through"
                                          : "text-white"
                                      }`}
                                    >
                                      {problem.title}
                                    </h4>

                                    <div className="flex items-center gap-1.5 mt-1 flex-wrap">
                                      <span
                                        className={`text-xs px-1.5 py-0.5 rounded border ${getDifficultyColor(
                                          problem.difficulty
                                        )}`}
                                      >
                                        {problem.difficulty}
                                      </span>

                                      <div className="flex gap-1 flex-wrap">
                                        {problem.tags
                                          .slice(0, 2)
                                          .map((tag, index) => (
                                            <span
                                              key={index}
                                              className="text-xs px-1.5 py-0.5 bg-purple-800/30 text-purple-300 rounded"
                                            >
                                              {tag}
                                            </span>
                                          ))}
                                        <div className="hidden sm:block">
                                          {problem.tags.length > 2 &&
                                            problem.tags
                                              .slice(2, 3)
                                              .map((tag, index) => (
                                                <span
                                                  key={index + 2}
                                                  className="text-xs px-1.5 py-0.5 bg-purple-800/30 text-purple-300 rounded"
                                                >
                                                  {tag}
                                                </span>
                                              ))}
                                        </div>
                                        {problem.tags.length > 2 && (
                                          <span className="text-xs px-1.5 py-0.5 bg-gray-700/50 text-gray-400 rounded sm:hidden">
                                            +{problem.tags.length - 2}
                                          </span>
                                        )}
                                        {problem.tags.length > 3 && (
                                          <span className="text-xs px-1.5 py-0.5 bg-gray-700/50 text-gray-400 rounded hidden sm:block">
                                            +{problem.tags.length - 3}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex items-center gap-1.5 sm:gap-2 w-full sm:w-auto overflow-x-auto">
                                  {/* YouTube Link */}
                                  {problem.solutionLink ? (
                                    <a
                                      href={problem.solutionLink}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="flex items-center gap-1.5 text-red-400 hover:text-red-300 bg-red-400/10 hover:bg-red-400/20 transition-all duration-200 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-red-400/20 hover:border-red-400/40 text-xs sm:text-sm font-medium whitespace-nowrap"
                                      title="Watch YouTube solution"
                                    >
                                      <Youtube
                                        size={14}
                                        className="sm:hidden"
                                      />
                                      <Youtube
                                        size={16}
                                        className="hidden sm:block"
                                      />
                                      <span className="hidden md:inline">
                                        YouTube
                                      </span>
                                    </a>
                                  ) : (
                                    <a
                                      href={getYouTubeSearchUrl(problem.title)}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="flex items-center gap-1.5 text-red-400 hover:text-red-300 bg-red-400/10 hover:bg-red-400/20 transition-all duration-200 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-red-400/20 hover:border-red-400/40 text-xs sm:text-sm font-medium whitespace-nowrap"
                                      title="Search YouTube solutions"
                                    >
                                      <Youtube
                                        size={14}
                                        className="sm:hidden"
                                      />
                                      <Youtube
                                        size={16}
                                        className="hidden sm:block"
                                      />
                                      <span className="hidden md:inline">
                                        YouTube
                                      </span>
                                    </a>
                                  )}

                                  {/* View Solutions */}
                                  {isLeetCodeProblem ? (
                                    <button
                                      onClick={() =>
                                        handleViewSolutions(problem)
                                      }
                                      className="flex items-center gap-1.5 text-blue-400 hover:text-blue-300 bg-blue-400/10 hover:bg-blue-400/20 transition-all duration-200 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-blue-400/20 hover:border-blue-400/40 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap"
                                      title="View all solutions"
                                    >
                                      <Eye size={16} />
                                      <span className="hidden sm:inline">
                                        Solutions
                                      </span>
                                    </button>
                                  ) : (
                                    <button
                                      className="flex items-center gap-1.5 text-gray-400 bg-gray-400/10  px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg    text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap"
                                      title="View all solutions"
                                    >
                                      <Eye size={16} />
                                      <span className="hidden sm:inline">
                                        Solutions
                                      </span>
                                    </button>
                                  )}

                                  {/* Write Solution */}
                                  {isLeetCodeProblem ? (
                                    <button
                                      onClick={() =>
                                        handleWriteSolution(problem)
                                      }
                                      className="flex items-center gap-1.5 text-green-400 hover:text-green-300 bg-green-400/10 hover:bg-green-400/20 transition-all duration-200 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-green-400/20 hover:border-green-400/40 text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap"
                                      title="Write your solution"
                                    >
                                      <Code size={14} className="sm:hidden" />
                                      <Code
                                        size={16}
                                        className="hidden sm:block"
                                      />
                                      <span className="hidden md:inline">
                                        Solve
                                      </span>
                                    </button>
                                  ) : (
                                    <button
                                      
                                      className="flex items-center gap-1.5  bg-gray-400/10 text-gray-400 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg   text-xs sm:text-sm font-medium cursor-pointer whitespace-nowrap"
                                      title="Write your solution"
                                    >
                                      <Code size={14} className="sm:hidden" />
                                      <Code
                                        size={16}
                                        className="hidden sm:block"
                                      />
                                      <span className="hidden md:inline">
                                        Solve
                                      </span>
                                    </button>
                                  )}

                                  {/* LeetCode Link */}
                                  <a
                                    href={problem.link}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex items-center gap-1.5 text-purple-400 hover:text-purple-300 bg-purple-400/10 hover:bg-purple-400/20 transition-all duration-200 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-purple-400/20 hover:border-purple-400/40 text-xs sm:text-sm font-medium whitespace-nowrap"
                                    title="Open problem on LeetCode"
                                  >
                                    <ExternalLink
                                      size={14}
                                      className="sm:hidden"
                                    />
                                    <ExternalLink
                                      size={16}
                                      className="hidden sm:block"
                                    />
                                    <span className="hidden md:inline">
                                      Problem
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              );
            })
          ) : (
            <div className="bg-gray-900/60 backdrop-blur-sm rounded-lg sm:rounded-xl border border-purple-500/20 p-6 sm:p-8 text-center">
              <h3 className="text-lg sm:text-xl font-bold text-white mb-3 sm:mb-4">
                No Questions Available
              </h3>
              <p className="text-sm sm:text-base text-gray-300">
                {data.topics === undefined
                  ? "Loading questions..."
                  : "No questions have been added to the database yet."}
              </p>
            </div>
          )}
        </div>

        {/* Upload Solution Modal */}
        {isUploadModalOpen && selectedProblem && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4">
            <div className="w-full h-full max-w-7xl max-h-[95vh] bg-gradient-to-br from-gray-900 to-purple-900/50 flex flex-col rounded-lg sm:rounded-xl overflow-hidden">
              <div className="flex-1 flex flex-col">
                <div className="flex justify-between items-center p-4 sm:p-6 border-b border-purple-500/30 flex-shrink-0">
                  <div className="min-w-0 flex-1">
                    <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-1 sm:mb-2 truncate">
                      Submit Solution
                    </h2>
                    <p className="text-purple-300 text-sm sm:text-base lg:text-lg truncate">
                      {selectedProblem.questionTitle}
                    </p>
                  </div>
                  <button
                    onClick={handleCloseModal}
                    className="text-gray-400 hover:text-white transition-colors p-2 sm:p-3 hover:bg-gray-700/50 rounded-lg ml-4 flex-shrink-0"
                  >
                    <svg
                      className="w-6 h-6 sm:w-8 sm:h-8"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <div className="flex-1 overflow-hidden p-4 sm:p-6">
                  {submissionResult ? (
                    <div className="h-full flex flex-col">
                      <div
                        className={`flex-grow flex flex-col items-center justify-center p-4 sm:p-8 ${
                          submissionResult.loading
                            ? "text-blue-200"
                            : submissionResult.success
                            ? "text-green-200"
                            : "text-red-200"
                        }`}
                      >
                        {submissionResult.loading && (
                          <div className="flex flex-col items-center justify-center w-full max-w-2xl">
                            <div className="flex items-center mb-4 sm:mb-6">
                              <div className="w-6 h-6 sm:w-8 sm:h-8 border-2 sm:border-3 border-t-transparent border-blue-500 rounded-full animate-spin mr-3"></div>
                              <span className="text-lg sm:text-xl font-medium">
                                {submissionResult.message}
                              </span>
                            </div>
                            {submissionResult.details && (
                              <div className="w-full p-3 sm:p-4 bg-gray-800/50 border border-gray-700 rounded-md">
                                <p className="text-sm sm:text-base text-gray-300">
                                  {submissionResult.details}
                                </p>
                              </div>
                            )}
                          </div>
                        )}

                        {!submissionResult.loading && (
                          <div className="w-full max-w-3xl">
                            <div
                              className={`p-4 sm:p-6 rounded-lg shadow-lg mb-6 sm:mb-8 ${
                                submissionResult.success
                                  ? "bg-green-900/30 border border-green-500"
                                  : "bg-red-900/30 border border-red-500"
                              }`}
                            >
                              <h3 className="text-lg sm:text-xl lg:text-2xl font-bold mb-4 sm:mb-6 text-center">
                                {submissionResult.message}
                              </h3>

                              {submissionResult.details && (
                                <div className="bg-gray-800/50 rounded-md p-4 sm:p-6 font-mono text-sm sm:text-base">
                                  <pre className="whitespace-pre-wrap">
                                    {submissionResult.details}
                                  </pre>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>

                      {!submissionResult.loading && (
                        <div className="px-4 sm:px-8 py-4 border-t border-gray-800">
                          <button
                            onClick={() => setSubmissionResult(null)}
                            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-2 sm:py-3 rounded-md font-medium transition-colors"
                          >
                            Close
                          </button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="h-full">
                      <CodeEditorForm
                        onSubmit={handleSubmitSolution}
                        isLoading={isSubmitting}
                        user={user}
                        challengeData={selectedProblem}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Login Prompt Modal */}
        {showLoginPrompt && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-gradient-to-br from-gray-900 to-purple-900/50 rounded-lg sm:rounded-2xl border border-purple-500/30 p-4 sm:p-6 max-w-md w-full">
              <h3 className="text-lg sm:text-xl font-bold text-white mb-3 sm:mb-4">
                Login Required
              </h3>
              <p className="text-sm sm:text-base text-gray-300 mb-4 sm:mb-6">
                Please log in to submit solutions and track your progress.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                <Link
                  to="/signin"
                  className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-2 px-4 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all duration-300 text-center text-sm sm:text-base"
                >
                  Sign In
                </Link>
                <button
                  onClick={() => setShowLoginPrompt(false)}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors text-sm sm:text-base"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Sheet Problem Leaderboard Modal */}
        <SheetProblemLeaderboardModal
          isOpen={showLeaderboard}
          onClose={handleCloseLeaderboard}
          problemTitle={selectedLeaderboardProblem?.title || ""}
          solutions={leaderboardSolutions}
        />

        {/* Coin Animation for XP Reward */}
        {showCoinAnimation && (
          <div className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none">
            <div className="relative">
              {/* Coin Animation */}
              <div className="coin-animation">
                <div className="coin">
                  <div className="coin-inner">
                    <div className="coin-front">
                      <div className="coin-symbol">⭐</div>
                    </div>
                    <div className="coin-back">
                      <div className="coin-symbol">⭐</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* XP Text Animation */}
              <div className="xp-text-animation">
                <div className="xp-text">
                  <span className="xp-plus">+</span>
                  <span className="xp-number">10</span>
                  <span className="xp-label">XP</span>
                </div>
              </div>

              {/* Sparkle Effects */}
              <div className="sparkles">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className={`sparkle sparkle-${i + 1}`}>
                    ✨
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        <style jsx>
          {`
            .coin-animation {
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              animation: coinDrop 3s ease-out forwards;
            }

            .coin {
              width: 80px;
              height: 80px;
              position: relative;
              transform-style: preserve-3d;
              animation: coinSpin 3s linear infinite;
            }

            .coin-inner {
              position: absolute;
              width: 100%;
              height: 100%;
              transform-style: preserve-3d;
            }

            .coin-front,
            .coin-back {
              position: absolute;
              width: 100%;
              height: 100%;
              border-radius: 50%;
              background: linear-gradient(
                145deg,
                #ffd700 0%,
                #ffed4e 50%,
                #ffd700 100%
              );
              border: 3px solid #b8860b;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            }

            .coin-front {
              transform: rotateY(0deg) translateZ(2px);
            }

            .coin-back {
              transform: rotateY(180deg) translateZ(2px);
            }

            .coin-symbol {
              font-size: 32px;
              color: #b8860b;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            }

            .xp-text-animation {
              position: absolute;
              top: -60px;
              left: 50%;
              transform: translateX(-50%);
              animation: xpTextRise 3s ease-out forwards;
            }

            .xp-text {
              display: flex;
              align-items: center;
              gap: 2px;
              font-weight: bold;
              color: #00ff88;
              text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
              font-size: 24px;
            }

            .xp-plus {
              animation: xpPlusGlow 0.5s ease-out 1s forwards;
            }

            .xp-number {
              animation: xpNumberGlow 0.5s ease-out 1.2s forwards;
            }

            .xp-label {
              animation: xpLabelGlow 0.5s ease-out 1.4s forwards;
            }

            .sparkles {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 200px;
              height: 200px;
              pointer-events: none;
            }

            .sparkle {
              position: absolute;
              font-size: 16px;
              opacity: 0;
              animation: sparkleAnimation 2s ease-out forwards;
            }

            .sparkle-1 {
              top: 20%;
              left: 20%;
              animation-delay: 0.5s;
            }
            .sparkle-2 {
              top: 20%;
              right: 20%;
              animation-delay: 0.7s;
            }
            .sparkle-3 {
              bottom: 20%;
              left: 20%;
              animation-delay: 0.9s;
            }
            .sparkle-4 {
              bottom: 20%;
              right: 20%;
              animation-delay: 1.1s;
            }
            .sparkle-5 {
              top: 10%;
              left: 50%;
              animation-delay: 0.6s;
            }
            .sparkle-6 {
              bottom: 10%;
              left: 50%;
              animation-delay: 1s;
            }
            .sparkle-7 {
              top: 50%;
              left: 10%;
              animation-delay: 0.8s;
            }
            .sparkle-8 {
              top: 50%;
              right: 10%;
              animation-delay: 1.2s;
            }

            @keyframes coinDrop {
              0% {
                transform: translateY(-100px) scale(0.5);
                opacity: 0;
              }
              20% {
                transform: translateY(0px) scale(1.1);
                opacity: 1;
              }
              30% {
                transform: translateY(-10px) scale(1);
              }
              40% {
                transform: translateY(0px) scale(1);
              }
              100% {
                transform: translateY(0px) scale(1);
                opacity: 1;
              }
            }

            @keyframes coinSpin {
              0% {
                transform: rotateY(0deg);
              }
              100% {
                transform: rotateY(360deg);
              }
            }

            @keyframes xpTextRise {
              0% {
                transform: translateX(-50%) translateY(20px);
                opacity: 0;
              }
              20% {
                transform: translateX(-50%) translateY(0px);
                opacity: 1;
              }
              80% {
                transform: translateX(-50%) translateY(-10px);
                opacity: 1;
              }
              100% {
                transform: translateX(-50%) translateY(-30px);
                opacity: 0;
              }
            }

            @keyframes xpPlusGlow {
              0% {
                transform: scale(1);
              }
              50% {
                transform: scale(1.3);
                color: #00ff88;
              }
              100% {
                transform: scale(1);
              }
            }

            @keyframes xpNumberGlow {
              0% {
                transform: scale(1);
              }
              50% {
                transform: scale(1.3);
                color: #00ff88;
              }
              100% {
                transform: scale(1);
              }
            }

            @keyframes xpLabelGlow {
              0% {
                transform: scale(1);
              }
              50% {
                transform: scale(1.3);
                color: #00ff88;
              }
              100% {
                transform: scale(1);
              }
            }

            @keyframes sparkleAnimation {
              0% {
                transform: scale(0) rotate(0deg);
                opacity: 0;
              }
              50% {
                transform: scale(1) rotate(180deg);
                opacity: 1;
              }
              100% {
                transform: scale(0) rotate(360deg);
                opacity: 0;
              }
            }
          `}
        </style>
      </div>
    </div>
  );
};

export default OnCampus;
