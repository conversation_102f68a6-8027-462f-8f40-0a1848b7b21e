import { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';

const CountdownTimer = () => {
  const [timeLeft, setTimeLeft] = useState({ hours: 0, minutes: 0, seconds: 0 });

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date();
      const targetTime = new Date();

      // Set target time to 5:30 AM IST for the next day
      targetTime.setUTCHours(0); // 5:30 AM IST is 00:00 UTC
      targetTime.setUTCMinutes(0);
      targetTime.setUTCSeconds(0);
      targetTime.setUTCMilliseconds(0);

      // If current time is past 5:30 AM IST, set target to next day
      if (now >= targetTime) {
        targetTime.setUTCDate(targetTime.getUTCDate() + 1);
      }

      const difference = targetTime - now;

      if (difference > 0) {
        const hours = Math.floor(difference / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ hours, minutes, seconds });
      }
    };

    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="countdown-timer bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full py-2 px-4 flex items-center gap-2 shadow-lg">
      <Clock size={16} className="text-white" />
      <div className="flex items-center font-medium">
        <span>{String(timeLeft.hours).padStart(2, '0')}</span>
        <span className="mx-1">:</span>
        <span>{String(timeLeft.minutes).padStart(2, '0')}</span>
        <span className="mx-1">:</span>
        <span>{String(timeLeft.seconds).padStart(2, '0')}</span>
      </div>
      <span className="text-xs opacity-80 whitespace-nowrap">until next challenge</span>
    </div>
  );
};

export default CountdownTimer;