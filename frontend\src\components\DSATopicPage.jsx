import { useState, useEffect, useRef } from "react";
import { useParams } from "react-router-dom";
import { dsaTopics } from "../data/dsaTopics";
import {
  Search,
  Filter,
  ExternalLink,
  Eye,
  Code,
  CheckCircle,
  Circle,
  ArrowLeft,
  Play
} from "lucide-react";
import { Link } from "react-router-dom";
import CodeEditorForm from "./CodeEditorForm";
import SheetProblemLeaderboardModal from "./SheetProblemLeaderboardModal";
import { useAuth } from "../contexts/AuthContext";

const DSATopicPage = () => {
  const { topicId } = useParams();
  const [searchQuery, setSearchQuery] = useState("");
  const [difficultyFilter, setDifficultyFilter] = useState("");
  const [dynamicProblems, setDynamicProblems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [useDynamicData, setUseDynamicData] = useState(false);

  // OnCampus-style functionality states
  const [completedProblems, setCompletedProblems] = useState(new Set());
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [selectedProblem, setSelectedProblem] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showLeaderboard, setShowLeaderboard] = useState(false);
  const [selectedLeaderboardProblem, setSelectedLeaderboardProblem] = useState(null);
  const [leaderboardSolutions, setLeaderboardSolutions] = useState([]);
  const [loadingLeaderboard, setLoadingLeaderboard] = useState(false);
  const [showCoinAnimation, setShowCoinAnimation] = useState(false);

  const { isAuthenticated, user, refreshUser, updateUserData } = useAuth();
  const hasLoadedUserProgress = useRef(false);

  // Find the current topic based on the URL parameter
  const currentTopic = dsaTopics.find((topic) => topic.id === topicId);

  if (!currentTopic) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-white text-center">
          <h2 className="text-2xl font-bold mb-4">Topic Not Found</h2>
          <p>The requested DSA topic could not be found.</p>
        </div>
      </div>
    );
  }

  // Fetch dynamic problems from database
  useEffect(() => {
    const fetchDynamicProblems = async () => {
      try {
        setLoading(true);

        // Map topic IDs to appropriate tags for database queries
        const tagMapping = {
          'arrays': ['array'],
          'strings': ['string'],
          'dynamic-programming': ['dynamic programming'],
          'graphs': ['graph'],
          'trees': ['tree', 'binary tree', 'binary search tree'],
          'backtracking': ['backtracking'],
          'binary-search': ['binary search'],
          'linked-lists': ['linked list'],
          'stacks-and-queues': ['stack', 'queue']
        };

        const tags = tagMapping[topicId] || [topicId.replace('-', ' ')];
        const tagsQuery = tags.join(',');

        const response = await fetch(
          `${import.meta.env.VITE_API_URL}/api/v1/leetcode/questions/by-tags?tags=${encodeURIComponent(tagsQuery)}&limit=100`,
          {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
            },
          }
        );

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.questions && result.questions.length > 0) {
            setDynamicProblems(result.questions);
            setUseDynamicData(true);
          } else {
            console.log(`No dynamic questions found for ${currentTopic.title}, using static data`);
            setUseDynamicData(false);
          }
        } else {
          console.warn(`Failed to fetch dynamic questions for ${currentTopic.title}:`, response.status);
          setUseDynamicData(false);
        }
      } catch (error) {
        console.error(`Error fetching dynamic questions for ${currentTopic.title}:`, error);
        setUseDynamicData(false);
      } finally {
        setLoading(false);
      }
    };

    fetchDynamicProblems();
  }, [topicId, currentTopic.title]);

  // Get problems to display (dynamic or static)
  const problemsToDisplay = useDynamicData ? dynamicProblems : currentTopic.problems;

  // Filter problems based on search query and difficulty
  const filteredProblems = problemsToDisplay.filter((problem) => {
    const problemName = problem.name || problem.title;
    const matchesSearch = problemName
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesDifficulty = difficultyFilter
      ? problem.difficulty.toLowerCase() === difficultyFilter.toLowerCase()
      : true;
    return matchesSearch && matchesDifficulty;
  });

  // Fetch user completion status from database (similar to OnCampus)
  const fetchUserCompletionStatus = async () => {
    if (!isAuthenticated() || !user) return;

    try {
      const token = localStorage.getItem("token") || user?.token;
      const userId = user?._id || user?.id;

      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/v1/leetcode/sheet/user-questions`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
            Authorization: token ? `Bearer ${token}` : "",
            "X-User-ID": userId || "",
            "X-Auth-Token": token || "",
          },
          credentials: "include",
        }
      );

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.questions) {
          const userCompleted = new Set();

          // Match solved questions with current problems by title
          problemsToDisplay.forEach((problem) => {
            const problemTitle = problem.name || problem.title;
            const isSolved = result.questions.some(
              (solved) => solved.title === problemTitle
            );
            if (isSolved) {
              userCompleted.add(problem.id || problem._id);
            }
          });

          setCompletedProblems(userCompleted);

          // Update localStorage with user-specific data
          const userStorageKey = `topicCompleted_${topicId}_${user._id}`;
          localStorage.setItem(
            userStorageKey,
            JSON.stringify([...userCompleted])
          );
        } else {
          setCompletedProblems(new Set());
        }
      } else {
        console.error("Failed to fetch user completion status:", response.status);
        setCompletedProblems(new Set());
      }
    } catch (error) {
      console.error("Error fetching user completion status:", error);
      setCompletedProblems(new Set());
    }
  };

  // Watch for authentication changes to load/reset user progress
  useEffect(() => {
    if (!loading && problemsToDisplay.length > 0) {
      const currentUserId = user?._id;

      if (
        isAuthenticated() &&
        currentUserId &&
        !hasLoadedUserProgress.current
      ) {
        hasLoadedUserProgress.current = true;
        fetchUserCompletionStatus();
      } else if (!isAuthenticated() && hasLoadedUserProgress.current) {
        hasLoadedUserProgress.current = false;
        setCompletedProblems(new Set());
      }
    }
  }, [isAuthenticated, user?._id, loading, problemsToDisplay]);

  // Handle YouTube search URL generation (fallback when solutionLink is not available)
  const getYouTubeSearchUrl = (problemTitle) => {
    const searchQuery = `${problemTitle} leetcode solution explanation`;
    return `https://www.youtube.com/results?search_query=${encodeURIComponent(
      searchQuery
    )}`;
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case "easy":
        return "text-green-400 bg-green-400/10 border-green-400/20";
      case "medium":
        return "text-orange-400 bg-orange-400/10 border-orange-400/20";
      case "hard":
        return "text-red-400 bg-red-400/10 border-red-400/20";
      default:
        return "text-gray-400 bg-gray-400/10 border-gray-400/20";
    }
  };

  // Helper functions (similar to OnCampus component)
  const toggleProblemCompletion = (problem) => {
    if (!isAuthenticated()) {
      setShowLoginPrompt(true);
      return;
    }

    const problemId = problem.id || problem._id;
    const isCompleted = completedProblems.has(problemId);
    const newCompleted = new Set(completedProblems);

    if (isCompleted) {
      newCompleted.delete(problemId);
    } else {
      newCompleted.add(problemId);
    }

    setCompletedProblems(newCompleted);

    // Save to user-specific localStorage key
    if (user && user._id) {
      const userStorageKey = `topicCompleted_${topicId}_${user._id}`;
      localStorage.setItem(
        userStorageKey,
        JSON.stringify([...newCompleted])
      );
    }

    // TODO: Call backend API to toggle question status (similar to OnCampus)
    // This would require implementing the same backend functionality
  };

  const handleViewSolutions = async (problem) => {
    setSelectedLeaderboardProblem(problem);
    setLoadingLeaderboard(true);
    setShowLeaderboard(true);

    try {
      const problemTitle = problem.name || problem.title;
      // console.log("Fetching solutions for problem:", problemTitle);
      const response = await fetch(
        `${
          import.meta.env.VITE_API_URL
        }/api/v1/leetcode/sheet/problem-solutions/${encodeURIComponent(
          problemTitle
        )}`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
          },
          credentials: "include",
        }
      );

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // console.log(
          //   `Fetched ${result.count} solutions for ${problemTitle}:`,
          //   result.solutions
          // );
          setLeaderboardSolutions(result.solutions || []);
        } else {
          console.error("Failed to fetch solutions:", result.message);
          setLeaderboardSolutions([]);
        }
      } else {
        console.error("Failed to fetch solutions:", response.status);
        setLeaderboardSolutions([]);
      }
    } catch (error) {
      console.error("Error fetching solutions:", error);
      setLeaderboardSolutions([]);
    } finally {
      setLoadingLeaderboard(false);
    }
  };

  const handleWriteSolution = (problem) => {
    if (!isAuthenticated()) {
      setShowLoginPrompt(true);
      return;
    }

    // Convert problem data to challengeData format for modal
    const challengeData = convertProblemToChallengeData(problem);

    // Prevent background scrolling when modal opens
    document.body.style.overflow = "hidden";

    setSelectedProblem(challengeData);
    setIsUploadModalOpen(true);
    setSubmissionResult(null);
  };

  const convertProblemToChallengeData = (problem) => {
    const leetcodeUrl = problem.link || problem.questionLink;
    const titleSlug = leetcodeUrl ? leetcodeUrl.split("/problems/")[1]?.split("/")[0] || "unknown" : "unknown";
    const problemName = problem.name || problem.title;

    return {
      id: problem.id || problem._id,
      questionId: titleSlug,
      questionTitle: problemName,
      questionLink: leetcodeUrl,
      difficulty: problem.difficulty,
      topicTags: problem.tags || [],
      codeSnippets: [],
    };
  };

  const handleCloseModal = () => {
    if (window.submissionPollingTimeout) {
      clearTimeout(window.submissionPollingTimeout);
      window.submissionPollingTimeout = null;
    }

    // Restore background scrolling when modal closes
    document.body.style.overflow = "unset";

    setIsUploadModalOpen(false);
    setSelectedProblem(null);
    setSubmissionResult(null);
  };

  const handleCloseLeaderboard = () => {
    setShowLeaderboard(false);
    setSelectedLeaderboardProblem(null);
    setLeaderboardSolutions([]);
  };

  // Function to save solution to database (from OnCampus)
  const saveSolutionToDatabase = async ({
    submissionId,
    username,
    code,
    language,
    approach,
    data,
    challengeData,
  }) => {
    try {
      const titleSlug = challengeData.questionLink
        .split("/problems/")[1]
        ?.split("/")[0];

      const solutionData = {
        problemTitle: challengeData.questionTitle,
        problemSlug: titleSlug,
        difficulty: challengeData.difficulty,
        code,
        language,
        approach: approach || "",
        status: data.status_msg || "Accepted",
        topicTags: challengeData.topicTags || [],
        userId: user?._id,
        isSheetQuestion: true, // Flag to identify this as a sheet question
      };

      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/v1/leetcode/aftersubmit`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          credentials: "include",
          body: JSON.stringify(solutionData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to save solution");
      }

      const result = await response.json();
      // console.log("Solution saved to database:", result);

      if (result.isDuplicate) {
        return {
          ...result,
          warningMessage:
            result.warning ||
            "This solution appears to match an existing submission. Please try solving with your own unique approach for better learning.",
        };
      }

      return result;
    } catch (error) {
      console.error("Error saving solution to database:", error);
      throw error;
    }
  };

  // Handle submit solution (matching OnCampus functionality)
  const handleSubmitSolution = async ({
    username,
    code,
    language,
    approach,
  }) => {
    if (window.submissionPollingTimeout) {
      clearTimeout(window.submissionPollingTimeout);
      window.submissionPollingTimeout = null;
    }

    setIsSubmitting(true);
    setSubmissionResult({
      loading: true,
      message: "Submitting solution...",
      details: "Please wait while we submit your solution ",
    });

    try {
      const titleSlug = selectedProblem.questionLink
        .split("/problems/")[1]
        ?.split("/")[0];

      if (!titleSlug) {
        throw new Error("Could not determine problem slug from question link");
      }

      const payload = {
        lang: language,
        question_id:
          selectedProblem.realQuestionId || selectedProblem.questionId,
        typed_code: code,
      };

      setSubmissionResult({
        loading: true,
        message: "Submitting solution ...",
        details: "Your solution is being submitted to the judge.",
      });

      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/api/v1/leetcode/submit/${titleSlug}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to submit solution (${response.status})`);
      }

      const data = await response.json();

      // Extract submission ID from response
      const submissionId =
        data.submission_id ||
        data.submission?.id ||
        response.headers.get("x-submission-id");

      if (!submissionId) {
        throw new Error("No submission ID received from server");
      }

      // Start checking submission status
      setSubmissionResult({
        loading: true,
        message: "Checking submission status...",
      });

      checkSubmissionStatus(submissionId, username, code, language, approach);
    } catch (error) {
      console.error("Error submitting solution:", error);
      setSubmissionResult({
        success: false,
        message:
          error.message || "An error occurred while submitting your solution",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const checkSubmissionStatus = async (
    submissionId,
    username,
    code,
    language,
    approach,
    attempt = 1
  ) => {
    const maxAttempts = 10;
    const pollInterval = 2000;

    try {
      // Update loading state
      setSubmissionResult({
        loading: true,
        message: `Checking submission status... (attempt ${attempt}/${maxAttempts})`,
        details: "Please wait while we check your solution.",
      });

      // Check if we've exceeded max attempts
      if (attempt > maxAttempts) {
        setSubmissionResult({
          success: false,
          message: "Submission timeout",
          details:
            "The submission is taking longer than expected. Please check your account for the result.",
        });
        return;
      }

      // Check submission status
      const response = await fetch(
        `${
          import.meta.env.VITE_API_URL
        }/api/v1/leetcode/submissions/${submissionId}/check`,
        {
          headers: { Accept: "application/json" },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to check submission status (${response.status})`
        );
      }

      const data = await response.json();

      // Handle completed submission
      if (data.state === "SUCCESS") {
        clearTimeout(window.submissionPollingTimeout);

        const isAccepted =
          data.status_code === 10 ||
          (data.status_msg === "Accepted" && data.run_success === true);

        if (isAccepted) {
          // Format success result
          const resultDetails = [
            `Status: ${data.status_msg || "Accepted"}`,
            `Runtime: ${data.status_runtime || "N/A"}`,
            `Memory: ${data.status_memory || "N/A"}`,
            `Runtime Percentile: ${
              data.runtime_percentile?.toFixed(2) || "N/A"
            }%`,
            `Memory Percentile: ${
              data.memory_percentile?.toFixed(2) || "N/A"
            }%`,
            `Test Cases: ${data.total_correct || "N/A"}/${
              data.total_testcases || "N/A"
            }`,
          ].join("\n");

          setSubmissionResult({
            success: true,
            message:
              "Solution accepted! Your solution has been added to the community solutions.",
            details: resultDetails,
            fullData: data,
          });

          // Trigger coin animation for successful submission
          setShowCoinAnimation(true);
          setTimeout(() => setShowCoinAnimation(false), 3000);

          // Mark the current problem as completed
          if (selectedProblem && selectedProblem.id) {
            const newCompleted = new Set(completedProblems);
            newCompleted.add(selectedProblem.id);
            setCompletedProblems(newCompleted);

            // Save to user-specific localStorage key
            if (user && user._id) {
              const userStorageKey = `topicCompleted_${topicId}_${user._id}`;
              localStorage.setItem(
                userStorageKey,
                JSON.stringify([...newCompleted])
              );
            }
          }

          // Save to database if authenticated
          if (isAuthenticated()) {
            try {
              const saveResult = await saveSolutionToDatabase({
                submissionId,
                username,
                code,
                language,
                approach,
                data,
                challengeData: selectedProblem,
              });

              if (saveResult.warningMessage) {
                setSubmissionResult((prev) => ({
                  ...prev,
                  details: `${resultDetails}\n\n⚠️ ${saveResult.warningMessage}`,
                  warning: saveResult.warningMessage,
                }));
              }

              if (saveResult.updatedUser) {
                updateUserData(saveResult.updatedUser);
              }
              if (refreshUser) {
                await refreshUser();
              }
            } catch (error) {
              console.error("Error saving solution:", error);
            }
          }
        } else {
          // Format rejection result
          let errorDetails = `Error: ${data.status_msg}`;
          if (data.compile_error)
            errorDetails += `\n\nCompile Error:\n${data.compile_error}`;
          if (data.runtime_error)
            errorDetails += `\n\nRuntime Error:\n${data.runtime_error}`;
          if (data.last_testcase)
            errorDetails += `\n\nFailed Test Case:\n${data.last_testcase}`;
          if (data.total_correct && data.total_testcases) {
            errorDetails += `\n\nTest Cases: ${data.total_correct}/${data.total_testcases}`;
          }

          setSubmissionResult({
            success: false,
            message: "Solution rejected",
            details: errorDetails,
            fullData: data,
          });
        }
        return;
      }

      // Handle still processing states
      if (data.state === "PENDING" || data.state === "STARTED") {
        const stateMessages = {
          PENDING: "Solution is waiting to be evaluated...",
          STARTED: "Solution evaluation has started...",
        };

        setSubmissionResult({
          loading: true,
          message:
            stateMessages[data.state] || "Solution is being evaluated...",
          details: "Your solution is being processed by the judge.",
        });

        // Continue polling
        window.submissionPollingTimeout = setTimeout(
          () =>
            checkSubmissionStatus(
              submissionId,
              username,
              code,
              language,
              approach,
              attempt + 1
            ),
          pollInterval
        );
        return;
      }

      // Handle error state
      if (data.state === "ERROR") {
        setSubmissionResult({
          success: false,
          message: "Error evaluating solution",
          details:
            data.status_msg || "There was an error evaluating your solution.",
        });
        return;
      }

      // Handle unknown states - continue polling for minimal response objects
      if (Object.keys(data).length <= 2 || !data.state) {
        setSubmissionResult({
          loading: true,
          message: "Waiting for evaluation to begin...",
          details:
            "Your solution has been submitted and is waiting to be processed.",
        });

        window.submissionPollingTimeout = setTimeout(
          () =>
            checkSubmissionStatus(
              submissionId,
              username,
              code,
              language,
              approach,
              attempt + 1
            ),
          pollInterval
        );
        return;
      }

      // Handle completely unknown states
      setSubmissionResult({
        success: false,
        message: "Unknown submission state",
        details: `Received unknown state: ${data.state}. Please check your account for the result.`,
      });
    } catch (error) {
      console.error("Error checking submission status:", error);
      setSubmissionResult({
        success: false,
        message: "Error checking submission status",
        details: error.message || "Failed to check submission status",
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen py-16 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto mr-20 ml-20">
        <div className="flex items-center justify-center">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4"></div>
            <h2 className="text-xl font-bold mb-2">Loading {currentTopic.title} Problems</h2>
            <p>Fetching problems from database...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-16 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto mr-20 ml-20">
      {/* Back Button */}
      <div className="mb-6">
        <Link
          to="/"
          className="inline-flex items-center gap-2 text-purple-300 hover:text-white transition-all duration-200 bg-purple-900/20 hover:bg-purple-800/30 backdrop-blur-sm border border-purple-500/20 hover:border-purple-400/40 px-4 py-2 rounded-lg cursor-pointer"
        >
          <ArrowLeft size={20} />
          <span>Back to Home</span>
        </Link>
      </div>

      {/* Topic Header */}
      <div className="w-full mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-center text-white mb-2">
          {currentTopic.title}
        </h1>
        <div className="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-600 mx-auto mb-6"></div>

        {/* Data source indicator */}
        <div className="text-center mb-4">
          <span className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs ${
            useDynamicData
              ? 'bg-green-900/30 text-green-400 border border-green-400/20'
              : 'bg-yellow-900/30 text-yellow-400 border border-yellow-400/20'
          }`}>
            <div className={`w-2 h-2 rounded-full ${useDynamicData ? 'bg-green-400' : 'bg-yellow-400'} animate-pulse`}></div>
            {useDynamicData ? `${dynamicProblems.length} problems from database` : `${currentTopic.problems.length} static problems`}
          </span>
        </div>
      </div>

      {/* Topic Description with Image */}
      <div className="relative p-6 rounded-xl bg-gradient-to-br from-purple-900/30 to-transparent backdrop-blur-sm border border-purple-500/20 shadow-xl mb-10">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent rounded-xl blur-[40px]"></div>
        <div className="relative z-10">
          <h2 className="text-2xl font-bold mb-4 text-white">
            What is {currentTopic.title}?
          </h2>
          <p className="text-gray-300">{currentTopic.intro}</p>

          {/* Image Section */}
          {currentTopic.image && (
            <div className="flex justify-center mb-6 mt-6">
              <div className="relative">
                <img
                  src={currentTopic.image}
                  alt={`${currentTopic.title} visualization`}
                  className="max-w-full h-auto max-h-64 object-contain rounded-lg shadow-lg"
                  onError={(e) => {
                    e.target.style.display = "none";
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-purple-500/20 to-transparent rounded-lg pointer-events-none"></div>
              </div>
            </div>
          )}

          <p className="text-gray-300">{currentTopic.description}</p>

          {/* Key Features Section */}
          {currentTopic.keyFeatures && currentTopic.keyFeatures.length > 0 && (
            <div className="mb-6 mt-6">
              <h3 className="text-xl font-semibold text-white mb-2">
                Key Features
              </h3>
              <ul className="list-disc list-inside text-gray-300 space-y-1">
                {currentTopic.keyFeatures.map((feature, idx) => (
                  <li key={idx}>{feature}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Important Points Section */}
          {currentTopic.importantPoints &&
            currentTopic.importantPoints.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  Important Points
                </h3>
                <ul className="list-disc list-inside text-gray-300 space-y-1">
                  {currentTopic.importantPoints.map((point, idx) => (
                    <li key={idx}>{point}</li>
                  ))}
                </ul>
              </div>
            )}

           {/* <button className="mt-6 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg transition-colors duration-300 cursor-pointer">
              <a
                href={currentTopic.link}
              >
                Read More
              </a>
            </button>
            */}
        </div>
      </div>

      {/* Toggle between problem list and table view */}
      <div className="mb-6">
        <div className="flex justify-center">
          <div className="bg-gray-800/50 rounded-lg p-1 border border-purple-500/20">
            <button
              className="px-4 py-2 rounded-md text-sm font-medium transition-colors bg-purple-600 text-white"
            >
              Problem List
            </button>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row gap-4 mb-8">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search problems..."
            className="w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-purple-500/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Filter size={18} className="text-gray-400" />
          </div>
          <select
            className="pl-10 pr-8 py-2 bg-gray-800/50 border border-purple-500/20 rounded-lg text-white appearance-none focus:outline-none focus:ring-2 focus:ring-purple-500/50"
            value={difficultyFilter}
            onChange={(e) => setDifficultyFilter(e.target.value)}
          >
            <option value="">All Difficulties</option>
            <option value="Easy">Easy</option>
            <option value="Medium">Medium</option>
            <option value="Hard">Hard</option>
          </select>
        </div>
      </div>

      {/* Problems List (OnCampus style) */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-900/30 to-transparent backdrop-blur-sm border border-purple-500/20 shadow-xl">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent rounded-xl blur-[40px]"></div>
        <div className="relative z-10 p-4">
          <div className="space-y-3">
            {filteredProblems.length > 0 ? (
              filteredProblems.map((problem, index) => {
                const problemId = problem.id || problem._id || index;
                const problemName = problem.name || problem.title;
                const isCompleted = completedProblems.has(problemId);
                const questionLink = problem.link || problem.questionLink;
                const isLeetCodeProblem = questionLink && questionLink.includes('leetcode.com');

                return (
                  <div
                    key={problemId}
                    className={`p-4 rounded-lg border transition-all duration-200 ${
                      isCompleted
                        ? "bg-green-900/20 border-green-500/30"
                        : "bg-gray-800/50 border-gray-600/30 hover:border-purple-500/50"
                    }`}
                  >
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <button
                          onClick={() => toggleProblemCompletion(problem)}
                          className={`transition-colors duration-200 cursor-pointer flex-shrink-0 ${
                            isCompleted
                              ? "text-green-400"
                              : "text-gray-400 hover:text-purple-400"
                          }`}
                          title={
                            isCompleted
                              ? "Mark as incomplete"
                              : "Mark as complete"
                          }
                        >
                          {isCompleted ? (
                            <CheckCircle size={20} />
                          ) : (
                            <Circle size={20} />
                          )}
                        </button>

                        <div className="flex-1 min-w-0">
                          <h4
                            className={`font-medium text-base transition-colors duration-200 ${
                              isCompleted
                                ? "text-green-400 line-through"
                                : "text-white"
                            }`}
                          >
                            {problemName}
                          </h4>

                          <div className="flex items-center gap-1.5 mt-1 flex-wrap">
                            <span
                              className={`text-xs px-1.5 py-0.5 rounded border ${getDifficultyColor(
                                problem.difficulty
                              )}`}
                            >
                              {problem.difficulty}
                            </span>

                            {problem.tags && problem.tags.length > 0 && (
                              <div className="flex gap-1 flex-wrap">
                                {problem.tags
                                  .slice(0, 2)
                                  .map((tag, tagIndex) => (
                                    <span
                                      key={tagIndex}
                                      className="text-xs px-1.5 py-0.5 bg-purple-800/30 text-purple-300 rounded"
                                    >
                                      {tag}
                                    </span>
                                  ))}
                                {problem.tags.length > 2 && (
                                  <span className="text-xs px-1.5 py-0.5 bg-gray-700/50 text-gray-400 rounded">
                                    +{problem.tags.length - 2}
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2 w-full sm:w-auto overflow-x-auto">
                        {/* Problem Link */}
                        {questionLink && (
                          <a
                            href={questionLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-1.5 text-purple-400 hover:text-purple-300 bg-purple-400/10 hover:bg-purple-400/20 transition-all duration-200 px-3 py-2 rounded-lg border border-purple-400/20 hover:border-purple-400/40 text-sm font-medium whitespace-nowrap"
                            title="Open problem"
                          >
                            <ExternalLink size={16} />
                            <span className="hidden md:inline">Problem</span>
                          </a>
                        )}

                        {/* YouTube Link */}
                        <a
                          href={
                            problem.videoSolution ||
                            problem.solutionLink ||
                            getYouTubeSearchUrl(problemName)
                          }
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-1.5 text-red-400 hover:text-red-300 bg-red-400/10 hover:bg-red-400/20 transition-all duration-200 px-3 py-2 rounded-lg border border-red-400/20 hover:border-red-400/40 text-sm font-medium whitespace-nowrap"
                          title="Watch YouTube solution"
                        >
                          <Play size={16} />
                          <span className="hidden md:inline">YouTube</span>
                        </a>

                        {/* View Solutions */}
                        {isLeetCodeProblem ? (
                          <button
                            onClick={() => handleViewSolutions(problem)}
                            className="flex items-center gap-1.5 text-blue-400 hover:text-blue-300 bg-blue-400/10 hover:bg-blue-400/20 transition-all duration-200 px-3 py-2 rounded-lg border border-blue-400/20 hover:border-blue-400/40 text-sm font-medium cursor-pointer whitespace-nowrap"
                            title="View all solutions"
                          >
                            <Eye size={16} />
                            <span className="hidden sm:inline">Solutions</span>
                          </button>
                        ) : (
                          <button
                            className="flex items-center gap-1.5 text-gray-400 bg-gray-400/10 px-3 py-2 rounded-lg text-sm font-medium cursor-not-allowed whitespace-nowrap"
                            title="Solutions not available"
                            disabled
                          >
                            <Eye size={16} />
                            <span className="hidden sm:inline">Solutions</span>
                          </button>
                        )}

                        {/* Write Solution */}
                        {isLeetCodeProblem ? (
                          <button
                            onClick={() => handleWriteSolution(problem)}
                            className="flex items-center gap-1.5 text-green-400 hover:text-green-300 bg-green-400/10 hover:bg-green-400/20 transition-all duration-200 px-3 py-2 rounded-lg border border-green-400/20 hover:border-green-400/40 text-sm font-medium cursor-pointer whitespace-nowrap"
                            title="Write solution"
                          >
                            <Code size={16} />
                            <span className="hidden sm:inline">Write</span>
                          </button>
                        ) : (
                          <button
                            className="flex items-center gap-1.5 text-gray-400 bg-gray-400/10 px-3 py-2 rounded-lg text-sm font-medium cursor-not-allowed whitespace-nowrap"
                            title="Write solution not available"
                            disabled
                          >
                            <Code size={16} />
                            <span className="hidden sm:inline">Write</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-8 text-gray-400">
                No problems found matching your criteria.
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Upload Solution Modal */}
      {isUploadModalOpen && selectedProblem && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4">
          <div className="w-full h-full max-w-7xl max-h-[95vh] bg-gradient-to-br from-gray-900 to-purple-900/50 flex flex-col rounded-lg sm:rounded-xl overflow-hidden">
            <div className="flex-1 flex flex-col">
              <div className="flex justify-between items-center p-4 sm:p-6 border-b border-purple-500/30 flex-shrink-0">
                <div className="min-w-0 flex-1">
                  <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-1 sm:mb-2 truncate">
                    Submit Solution
                  </h2>
                  <p className="text-purple-300 text-sm sm:text-base lg:text-lg truncate">
                    {selectedProblem.questionTitle}
                  </p>
                </div>
                <button
                  onClick={handleCloseModal}
                  className="text-gray-400 hover:text-white transition-colors p-2 sm:p-3 hover:bg-gray-700/50 rounded-lg ml-4 flex-shrink-0"
                >
                  <svg
                    className="w-6 h-6 sm:w-8 sm:h-8"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              <div className="flex-1 overflow-hidden p-4 sm:p-6">
                {submissionResult ? (
                  <div className="h-full flex flex-col">
                    <div
                      className={`flex-grow flex flex-col items-center justify-center p-4 sm:p-8 ${
                        submissionResult.loading
                          ? "text-blue-200"
                          : submissionResult.success
                          ? "text-green-200"
                          : "text-red-200"
                      }`}
                    >
                      {submissionResult.loading && (
                        <div className="flex flex-col items-center justify-center w-full max-w-2xl">
                          <div className="flex items-center mb-4 sm:mb-6">
                            <div className="w-6 h-6 sm:w-8 sm:h-8 border-2 sm:border-3 border-t-transparent border-blue-500 rounded-full animate-spin mr-3"></div>
                            <span className="text-lg sm:text-xl font-medium">
                              {submissionResult.message}
                            </span>
                          </div>
                          {submissionResult.details && (
                            <div className="w-full p-3 sm:p-4 bg-gray-800/50 border border-gray-700 rounded-md">
                              <p className="text-sm sm:text-base text-gray-300">
                                {submissionResult.details}
                              </p>
                            </div>
                          )}
                        </div>
                      )}

                      {!submissionResult.loading && (
                        <div className="w-full max-w-3xl">
                          <div
                            className={`p-4 sm:p-6 rounded-lg shadow-lg mb-6 sm:mb-8 ${
                              submissionResult.success
                                ? "bg-green-900/30 border border-green-500"
                                : "bg-red-900/30 border border-red-500"
                            }`}
                          >
                            <h3 className="text-lg sm:text-xl lg:text-2xl font-bold mb-4 sm:mb-6 text-center">
                              {submissionResult.message}
                            </h3>

                            {submissionResult.details && (
                              <div className="bg-gray-800/50 rounded-md p-4 sm:p-6 font-mono text-sm sm:text-base">
                                <pre className="whitespace-pre-wrap">
                                  {submissionResult.details}
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    {!submissionResult.loading && (
                      <div className="px-4 sm:px-8 py-4 border-t border-gray-800">
                        <button
                          onClick={() => setSubmissionResult(null)}
                          className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-2 sm:py-3 rounded-md font-medium transition-colors"
                        >
                          Close
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="h-full">
                    <CodeEditorForm
                      onSubmit={handleSubmitSolution}
                      isLoading={isSubmitting}
                      user={user}
                      challengeData={selectedProblem}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Leaderboard Modal */}
      {showLeaderboard && selectedLeaderboardProblem && (
        <SheetProblemLeaderboardModal
          problem={selectedLeaderboardProblem}
          solutions={leaderboardSolutions}
          loading={loadingLeaderboard}
          onClose={handleCloseLeaderboard}
        />
      )}

      {/* Login Prompt Modal */}
      {showLoginPrompt && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-xl p-6 max-w-md w-full mx-4 border border-purple-500/20">
            <h3 className="text-xl font-bold text-white mb-4">Login Required</h3>
            <p className="text-gray-300 mb-6">
              Please log in to track your progress and submit solutions.
            </p>
            <div className="flex gap-3">
              <Link
                to="/signin"
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-center transition-colors"
              >
                Sign In
              </Link>
              <button
                onClick={() => setShowLoginPrompt(false)}
                className="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DSATopicPage;
