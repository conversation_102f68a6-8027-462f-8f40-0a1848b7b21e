import { useState, useCallback, useEffect } from 'react';
import React<PERSON><PERSON>, {
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  MarkerType,
  Panel,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { motion } from 'framer-motion';
import dagre from 'dagre';
import PropTypes from 'prop-types';

// Custom node components
const BasicNode = ({ data }) => {
  return (
    <motion.div
      className="px-4 py-2 rounded-md shadow-lg border border-blue-400/30
        bg-blue-600
        text-white font-medium text-center min-w-[150px]"
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.3 }}
      whileHover={{
        scale: 1.05,
        boxShadow: '0 0 15px rgba(59, 130, 246, 0.5)',
      }}
    >
      {data.label}
      <div className="w-full h-1 bg-blue-400/50 mt-1"></div>
    </motion.div>
  );
};

BasicNode.propTypes = {
  data: PropTypes.shape({
    label: PropTypes.string.isRequired,
  }).isRequired,
};

const MainTopicNode = ({ data }) => {
  return (
    <motion.div
      className="px-5 py-2 rounded-md shadow-lg border border-blue-400/30
        bg-blue-600
        text-white font-semibold text-center min-w-[180px]"
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.4 }}
      whileHover={{
        scale: 1.08,
        boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)',
      }}
    >
      {data.label}
      <div className="w-full h-1 bg-blue-400/50 mt-1"></div>
      {data.description && (
        <div className="text-xs mt-1 text-blue-200 font-normal">
          {data.description}
        </div>
      )}
    </motion.div>
  );
};

MainTopicNode.propTypes = {
  data: PropTypes.shape({
    label: PropTypes.string.isRequired,
    description: PropTypes.string,
  }).isRequired,
};

// Node types mapping
const nodeTypes = {
  basic: BasicNode,
  mainTopic: MainTopicNode,
};

// Initial nodes with improved positioning and data
const initialNodes = [
  {
    id: '1',
    position: { x: 0, y: 0 },
    data: {
      label: 'Arrays & Hashing',
      description: 'Foundation of DSA'
    },
    type: 'mainTopic',
  },
  {
    id: '2',
    position: { x: 0, y: 0 },
    data: { label: 'Two Pointers' },
    type: 'basic',
  },
  {
    id: '3',
    position: { x: 0, y: 0 },
    data: { label: 'Stack' },
    type: 'basic',
  },
  {
    id: '4',
    position: { x: 0, y: 0 },
    data: { label: 'Binary Search' },
    type: 'basic',
  },
  {
    id: '5',
    position: { x: 0, y: 0 },
    data: { label: 'Sliding Window' },
    type: 'basic',
  },
  {
    id: '6',
    position: { x: 0, y: 0 },
    data: { label: 'Linked List' },
    type: 'basic',
  },
  {
    id: '7',
    position: { x: 0, y: 0 },
    data: {
      label: 'Trees',
      description: 'Hierarchical data structures'
    },
    type: 'mainTopic',
  },
  {
    id: '8',
    position: { x: 0, y: 0 },
    data: { label: 'Tries' },
    type: 'basic',
  },
  {
    id: '9',
    position: { x: 0, y: 0 },
    data: { label: 'Heap / Priority Queue' },
    type: 'basic',
  },
  {
    id: '10',
    position: { x: 0, y: 0 },
    data: {
      label: 'Backtracking',
      description: 'Recursive problem solving'
    },
    type: 'mainTopic',
  },
  {
    id: '11',
    position: { x: 0, y: 0 },
    data: { label: 'Intervals' },
    type: 'basic',
  },
  {
    id: '12',
    position: { x: 0, y: 0 },
    data: { label: 'Greedy' },
    type: 'basic',
  },
  {
    id: '13',
    position: { x: 0, y: 0 },
    data: {
      label: 'Graphs',
      description: 'Network relationships'
    },
    type: 'mainTopic',
  },
  {
    id: '14',
    position: { x: 0, y: 0 },
    data: { label: 'Advanced Graphs' },
    type: 'basic',
  },
  {
    id: '15',
    position: { x: 0, y: 0 },
    data: { label: '1-D Dynamic Programming' },
    type: 'basic',
  },
  {
    id: '16',
    position: { x: 0, y: 0 },
    data: { label: '2-D Dynamic Programming' },
    type: 'basic',
  },
  {
    id: '17',
    position: { x: 0, y: 0 },
    data: { label: 'Bit Manipulation' },
    type: 'basic',
  },
  {
    id: '18',
    position: { x: 0, y: 0 },
    data: { label: 'Math & Geometry' },
    type: 'basic',
  },
];

// Initial edges with improved styling
const initialEdges = [
  {
    id: 'e1-2',
    source: '1',
    target: '2',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e1-3',
    source: '1',
    target: '3',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e2-4',
    source: '2',
    target: '4',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e2-5',
    source: '2',
    target: '5',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e3-6',
    source: '3',
    target: '6',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e4-7',
    source: '4',
    target: '7',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e5-7',
    source: '5',
    target: '7',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e6-7',
    source: '6',
    target: '7',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e7-8',
    source: '7',
    target: '8',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e7-9',
    source: '7',
    target: '9',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e7-10',
    source: '7',
    target: '10',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e9-11',
    source: '9',
    target: '11',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e9-12',
    source: '9',
    target: '12',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e10-13',
    source: '10',
    target: '13',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e13-14',
    source: '13',
    target: '14',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e13-15',
    source: '13',
    target: '15',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e13-16',
    source: '13',
    target: '16',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e13-17',
    source: '13',
    target: '17',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
  {
    id: 'e16-18',
    source: '16',
    target: '18',
    animated: true,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
    type: 'smoothstep',
  },
];

// Dagre layout configuration
const dagreGraph = new dagre.graphlib.Graph();
dagreGraph.setDefaultEdgeLabel(() => ({}));

const getLayoutedElements = (nodes, edges, direction = 'TB') => {
  const nodeWidth = 180;
  const nodeHeight = 60;

  dagreGraph.setGraph({
    rankdir: direction,
    nodesep: 80,  // Increase space between nodes horizontally
    ranksep: 100, // Increase space between nodes vertically
    edgesep: 50,  // Increase space between edges
  });

  // Add nodes to dagre graph
  nodes.forEach((node) => {
    dagreGraph.setNode(node.id, { width: nodeWidth, height: nodeHeight });
  });

  // Add edges to dagre graph
  edges.forEach((edge) => {
    dagreGraph.setEdge(edge.source, edge.target);
  });

  // Calculate layout
  dagre.layout(dagreGraph);

  // Apply layout to nodes
  const layoutedNodes = nodes.map((node) => {
    const nodeWithPosition = dagreGraph.node(node.id);

    return {
      ...node,
      position: {
        x: nodeWithPosition.x - nodeWidth / 2,
        y: nodeWithPosition.y - nodeHeight / 2,
      },
    };
  });

  // Ensure edges have proper styling
  const styledEdges = edges.map(edge => ({
    ...edge,
    style: { stroke: '#ffffff', strokeWidth: 2 },
    animated: true,
    type: 'smoothstep',
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#ffffff',
      width: 20,
      height: 20,
    },
  }));

  return { nodes: layoutedNodes, edges: styledEdges };
};

const DSARoadmap = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [layoutDirection, setLayoutDirection] = useState('TB'); // TB = top to bottom

  // Apply layout on initial render and when direction changes
  useEffect(() => {
    const { nodes: layoutedNodes, edges: layoutedEdges } = getLayoutedElements(
      initialNodes,
      initialEdges,
      layoutDirection
    );

    // Set a small timeout to ensure the component is fully mounted
    setTimeout(() => {
      setNodes(layoutedNodes);
      setEdges(layoutedEdges);
    }, 100);
  }, [layoutDirection, setNodes, setEdges]);

  // Force re-render of edges if they're not showing
  useEffect(() => {
    if (edges.length > 0) {
      const timer = setTimeout(() => {
        // Create a copy of edges with forced new styling
        const refreshedEdges = edges.map(edge => ({
          ...edge,
          style: { ...edge.style, stroke: '#ffffff', strokeWidth: 2.5 },
          animated: true,
          type: 'smoothstep',
        }));
        setEdges(refreshedEdges);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [edges, setEdges]);

  const onLayout = useCallback(
    (direction) => {
      setLayoutDirection(direction);
    },
    [setLayoutDirection]
  );

  return (
    <div className="w-full h-[800px] relative">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        nodeTypes={nodeTypes}
        fitView
        className="bg-gray-900 border border-gray-800 rounded-2xl shadow-xl"
        defaultEdgeOptions={{
          style: { strokeWidth: 3, stroke: '#ffffff' },
          animated: true,
          type: 'smoothstep',
          zIndex: 1000,
        }}
        edgesFocusable={true}
        edgesUpdatable={true}
        elementsSelectable={true}
        minZoom={0.2}
        maxZoom={1.5}
        attributionPosition="bottom-right"
        proOptions={{ hideAttribution: true }}
      >
        <Background
          color="#ffffff"
          variant="dots"
          gap={20}
          size={1}
          className="opacity-10"
        />
        <Controls
          className="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden"
          style={{ button: { width: '24px', height: '24px', borderRadius: '4px', color: 'white' } }}
        />
        <MiniMap
          className="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden"
          nodeColor={(node) => {
            return node.type === 'mainTopic' ? '#3b82f6' : '#2563eb';
          }}
          maskColor="rgba(17, 24, 39, 0.6)"
        />
        <Panel position="top-right" className="bg-gray-800 p-2 rounded-lg border border-gray-700">
          <div className="flex gap-2">
            <button
              onClick={() => onLayout('TB')}
              className={`px-3 py-1 text-xs rounded-md transition-colors ${
                layoutDirection === 'TB'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-200 hover:bg-gray-600'
              }`}
            >
              Top-Bottom
            </button>
            <button
              onClick={() => onLayout('LR')}
              className={`px-3 py-1 text-xs rounded-md transition-colors ${
                layoutDirection === 'LR'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-200 hover:bg-gray-600'
              }`}
            >
              Left-Right
            </button>
          </div>
        </Panel>
      </ReactFlow>
    </div>
  );
};

export default DSARoadmap;