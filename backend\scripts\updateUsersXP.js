/**
 * <PERSON><PERSON><PERSON> to calculate and update XP for all existing users
 * Run this script once to populate XP for users who have already solved questions
 */
import mongoose from 'mongoose';
import User from '../models/user.model.js';
import Question from '../models/question.model.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const updateAllUsersXP = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.DATABASE_URI);
    console.log('Connected to database');

    // Get all users
    const users = await User.find({});
    console.log(`Found ${users.length} users to update`);

    let updatedCount = 0;

    for (const user of users) {
      try {
        // Count questions where the user ID is in the userList array
        const problemsSolved = await Question.countDocuments({
          userList: { $in: [user._id] }
        });

        // Calculate XP (10 XP per question solved)
        const calculatedXP = problemsSolved * 10;

        // Update user's XP (don't touch streak or lastQuestionSolvedDate for existing users)
        await User.findByIdAndUpdate(
          user._id,
          { 
            xp: calculatedXP,
            // Only set streak and lastQuestionSolvedDate if they don't exist
            ...(user.streak === undefined && { streak: 0 }),
            ...(user.lastQuestionSolvedDate === undefined && { lastQuestionSolvedDate: null })
          },
          { new: true }
        );

        console.log(`Updated user ${user.username || user.fullName}: ${problemsSolved} problems solved, ${calculatedXP} XP`);
        updatedCount++;
      } catch (error) {
        console.error(`Error updating user ${user._id}:`, error.message);
      }
    }

    console.log(`\nUpdate complete! ${updatedCount}/${users.length} users updated successfully.`);
    
    // Show top 10 users by XP
    console.log('\nTop 10 users by XP:');
    const topUsers = await User.find({}, 'fullName username xp streak')
      .sort({ xp: -1, streak: -1 })
      .limit(10);
    
    topUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.username || user.fullName}: ${user.xp || 0} XP, ${user.streak || 0} streak`);
    });

  } catch (error) {
    console.error('Error updating users XP:', error);
  } finally {
    // Close database connection
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
};

// Run the script
updateAllUsersXP();
