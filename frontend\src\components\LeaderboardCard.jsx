import { useState } from 'react';
import PropTypes from 'prop-types';
import { Code, Clock, HardDrive, Trophy, Medal, Award } from 'lucide-react';
import FullScreenModal from './FullScreenModal';

const LeaderboardCard = ({ solutions }) => {
  const [selectedSolution, setSelectedSolution] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = (solution) => {
    setSelectedSolution(solution);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Format language for display
  const getLanguageDisplay = (lang) => {
    const langMap = {
      'python3': 'Python',
      'javascript': 'JavaScript',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C'
    };
    return langMap[lang] || lang;
  };

  // Get medal icon based on rank
  const getMedalIcon = (index) => {
    switch (index) {
      case 0:
        return <Trophy size={18} className="text-yellow-400 drop-shadow-sm" />;
      case 1:
        return <Medal size={18} className="text-gray-300 drop-shadow-sm" />;
      case 2:
        return <Award size={18} className="text-amber-500 drop-shadow-sm" />;
      default:
        return null;
    }
  };

  // Get background color based on rank
  const getRowBackground = (index) => {
    switch (index) {
      case 0:
        return 'bg-gradient-to-r from-yellow-500/15 to-yellow-400/10 border-l-4 border-yellow-400 shadow-sm';
      case 1:
        return 'bg-gradient-to-r from-gray-500/15 to-gray-400/10 border-l-4 border-gray-300 shadow-sm';
      case 2:
        return 'bg-gradient-to-r from-amber-500/15 to-amber-400/10 border-l-4 border-amber-500 shadow-sm';
      default:
        return 'hover:bg-purple-500/8 border-l-4 border-transparent';
    }
  };

  return (
    <div className="leaderboard-card bg-gray-900/60 backdrop-blur-sm rounded-xl overflow-hidden border border-purple-500/30 shadow-2xl mt-6 flex flex-col" style={{ height: '400px' }}>
      <div className="py-4 px-6 bg-gradient-to-r from-purple-600 to-pink-600 shadow-lg">
        <h3 className="text-xl font-bold text-white flex items-center gap-3">
          <Trophy size={22} className="text-yellow-300" />
          Community Leaderboard
        </h3>
      </div>

      <div className="overflow-hidden flex-1 flex flex-col">
        <div className="overflow-x-auto">
          <table className="w-full text-left table-fixed">
            <thead className="bg-gray-800/50 text-gray-300 text-sm">
              <tr>
                <th className="py-3 px-4 font-medium w-16 text-center">#</th>
                <th className="py-3 px-4 font-medium w-48">User</th>
                <th className="py-3 px-4 font-medium w-20 text-center">Lang</th>
                <th className="py-3 px-4 font-medium w-24 text-center">Runtime</th>
                <th className="py-3 px-4 font-medium w-24 text-center">Memory</th>
                <th className="py-3 px-4 font-medium w-16 text-center"></th>
              </tr>
            </thead>
          </table>
        </div>

        <div className="overflow-y-auto flex-1">
          <table className="w-full text-left table-fixed">
            <tbody className="text-gray-300 divide-y divide-gray-800/50">
              {solutions.length === 0 ? (
                <tr>
                  <td colSpan="6" className="py-8 text-center text-gray-400">
                    <div className="flex flex-col items-center gap-2">
                      <Trophy size={32} className="text-gray-600" />
                      <span>No solutions submitted yet. Be the first!</span>
                    </div>
                  </td>
                </tr>
              ) : (
                solutions.map((solution, index) => (
                  <tr
                    key={solution.id}
                    className={`${getRowBackground(index)} transition-all duration-200 cursor-pointer hover:bg-purple-500/10 hover:scale-[1.01]`}
                    onClick={() => openModal(solution)}
                  >
                    <td className="py-3 px-4 w-16">
                      <div className="flex items-center justify-center gap-1">
                        {getMedalIcon(index)}
                        <span className="font-medium text-sm">{index + 1}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4 w-48">
                      <div className="font-medium text-white truncate" title={solution.username}>
                        {solution.username}
                      </div>
                    </td>
                    <td className="py-3 px-4 w-20">
                      <div className="text-center">
                        <span className="inline-block bg-purple-500/20 text-purple-300 px-2 py-1 rounded text-xs font-medium">
                          {getLanguageDisplay(solution.language)}
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4 w-24">
                      <div className="text-center text-sm font-mono">
                        {solution.stats?.runtime || 'N/A'}
                      </div>
                    </td>
                    <td className="py-3 px-4 w-24">
                      <div className="text-center text-sm font-mono">
                        {solution.stats?.memory || 'N/A'}
                      </div>
                    </td>
                    <td className="py-3 px-4 w-16">
                      <div className="flex justify-center">
                        <button className="text-purple-400 hover:text-purple-300 transition-colors p-1 rounded hover:bg-purple-500/20">
                          <Code size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {selectedSolution && (
        <FullScreenModal
          isOpen={isModalOpen}
          onClose={closeModal}
          title={`${selectedSolution.username}'s Solution (${getLanguageDisplay(selectedSolution.language)})`}
        >
          <div className="space-y-8">
            {/* Stats Card */}
            <div className="bg-gray-800/50 p-6 rounded-xl border border-purple-500/20 shadow-xl">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center gap-4">
                  <div className="bg-purple-500/20 p-3 rounded-full">
                    <Clock size={24} className="text-purple-400" />
                  </div>
                  <div>
                    <div className="text-gray-400 text-sm">Runtime</div>
                    <div className="text-white text-lg font-medium">{selectedSolution.stats?.runtime || 'N/A'}</div>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="bg-purple-500/20 p-3 rounded-full">
                    <HardDrive size={24} className="text-purple-400" />
                  </div>
                  <div>
                    <div className="text-gray-400 text-sm">Memory</div>
                    <div className="text-white text-lg font-medium">{selectedSolution.stats?.memory || 'N/A'}</div>
                  </div>
                </div>
              </div>

              {selectedSolution.stats?.runtimePercentile && (
                <div className="mt-6">
                  <div className="flex justify-between text-sm text-gray-400 mb-2">
                    <span>Runtime Percentile</span>
                    <span>{selectedSolution.stats.runtimePercentile?.toFixed(2)}%</span>
                  </div>
                  <div className="h-2.5 w-full bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
                      style={{ width: `${selectedSolution.stats.runtimePercentile}%` }}
                    ></div>
                  </div>

                  {selectedSolution.stats?.memoryPercentile && (
                    <div className="mt-4">
                      <div className="flex justify-between text-sm text-gray-400 mb-2">
                        <span>Memory Percentile</span>
                        <span>{selectedSolution.stats.memoryPercentile?.toFixed(2)}%</span>
                      </div>
                      <div className="h-2.5 w-full bg-gray-700 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
                          style={{ width: `${selectedSolution.stats.memoryPercentile}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Approach Section */}
            {selectedSolution.approach && (
              <div className="bg-gray-800/50 p-6 rounded-xl border border-purple-500/20 shadow-xl">
                <div className="flex items-center gap-3 mb-6">
                  <Trophy size={24} className="text-purple-400" />
                  <h3 className="text-white text-xl font-semibold">Approach</h3>
                </div>
                <pre className="text-gray-300 text-base whitespace-pre-wrap bg-gray-900/70 p-6 rounded-lg">
                  {selectedSolution.approach}
                </pre>
              </div>
            )}

            {/* Solution Code Section */}
            <div className="bg-gray-800/50 p-6 rounded-xl border border-purple-500/20 shadow-xl">
              <div className="flex items-center gap-3 mb-6">
                <Code size={24} className="text-purple-400" />
                <h3 className="text-white text-xl font-semibold">Solution Code</h3>
              </div>
              <div className="overflow-auto bg-gray-900/70 p-6 rounded-lg" style={{ maxHeight: 'calc(100vh - 300px)' }}>
                <pre className="text-gray-300 text-base whitespace-pre-wrap">
                  <code>{selectedSolution.code}</code>
                </pre>
              </div>
            </div>
          </div>
        </FullScreenModal>
      )}
    </div>
  );
};

LeaderboardCard.propTypes = {
  solutions: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      username: PropTypes.string.isRequired,
      code: PropTypes.string.isRequired,
      language: PropTypes.string.isRequired,
      approach: PropTypes.string,
      stats: PropTypes.shape({
        runtime: PropTypes.string,
        memory: PropTypes.string,
        runtimePercentile: PropTypes.number,
        memoryPercentile: PropTypes.number
      })
    })
  ).isRequired
};

export default LeaderboardCard;
