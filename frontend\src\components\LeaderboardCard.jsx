import { useState } from 'react';
import PropTypes from 'prop-types';
import { Code, Clock, HardDrive, Trophy, Medal, Award } from 'lucide-react';
import FullScreenModal from './FullScreenModal';

const LeaderboardCard = ({ solutions }) => {
  const [selectedSolution, setSelectedSolution] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = (solution) => {
    setSelectedSolution(solution);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Format language for display
  const getLanguageDisplay = (lang) => {
    const langMap = {
      'python3': 'Python',
      'javascript': 'JavaScript',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C'
    };
    return langMap[lang] || lang;
  };

  // Get medal icon based on rank
  const getMedalIcon = (index) => {
    switch (index) {
      case 0:
        return <Trophy size={18} className="text-yellow-400 drop-shadow-sm" />;
      case 1:
        return <Medal size={18} className="text-gray-300 drop-shadow-sm" />;
      case 2:
        return <Award size={18} className="text-amber-500 drop-shadow-sm" />;
      default:
        return null;
    }
  };

  // Get background color based on rank
  const getRowBackground = (index) => {
    switch (index) {
      case 0:
        return 'bg-gradient-to-r from-yellow-500/15 to-yellow-400/10 border-l-4 border-yellow-400 shadow-sm';
      case 1:
        return 'bg-gradient-to-r from-gray-500/15 to-gray-400/10 border-l-4 border-gray-300 shadow-sm';
      case 2:
        return 'bg-gradient-to-r from-amber-500/15 to-amber-400/10 border-l-4 border-amber-500 shadow-sm';
      default:
        return 'hover:bg-purple-500/8 border-l-4 border-transparent';
    }
  };

  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'N/A';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // Show relative time if within last 7 days
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;

    // Show formatted date for older submissions
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="leaderboard-card bg-gray-900/60 backdrop-blur-sm rounded-xl overflow-hidden border border-purple-500/30 shadow-2xl flex flex-col h-[350px] xs:h-[380px] sm:h-[400px]">
      <div className="py-3 xs:py-4 px-4 xs:px-6 bg-gradient-to-r from-purple-600 to-pink-600 shadow-lg">
        <h3 className="text-lg xs:text-xl font-bold text-white flex items-center gap-2 xs:gap-3">
          <Trophy size={18} className="text-yellow-300 xs:hidden" />
          <Trophy size={22} className="text-yellow-300 hidden xs:block" />
          Daily Challenge Leaderboard
        </h3>
      </div>

      <div className="overflow-hidden flex-1 flex flex-col">
        <table className="w-full text-left table-fixed">
          <thead className="bg-gray-800/50 text-gray-300 text-xs xs:text-sm">
            <tr>
              <th className="py-2 xs:py-3 px-2 xs:px-4 font-medium w-10 xs:w-16 text-center">#</th>
              <th className="py-2 xs:py-3 px-2 xs:px-4 font-medium w-24 xs:w-32">Username</th>
              <th className="py-2 xs:py-3 px-2 xs:px-4 font-medium w-16 xs:w-20 text-center">Lang</th>
              <th className="py-2 xs:py-3 px-2 xs:px-4 font-medium w-24 xs:w-32 text-center">Submitted</th>
              <th className="py-2 xs:py-3 px-2 xs:px-4 font-medium w-10 xs:w-16 text-center">Code</th>
            </tr>
          </thead>

          <tbody className="text-gray-300 divide-y divide-gray-800/50" style={{ maxHeight: 'calc(100% - 80px)', overflowY: 'auto' }}>
            {solutions.length === 0 ? (
              <tr>
                <td colSpan="5" className="py-4 xs:py-8 text-center text-gray-400">
                  <div className="flex flex-col items-center gap-2">
                    <Trophy size={24} className="text-gray-600 xs:hidden" />
                    <Trophy size={32} className="text-gray-600 hidden xs:block" />
                    <span className="text-xs xs:text-sm">No solutions submitted yet. Be the first!</span>
                  </div>
                </td>
              </tr>
            ) : (
              solutions.map((solution, index) => (
                <tr
                  key={solution.id}
                  className={`${getRowBackground(index)} transition-all duration-200`}
                >
                  <td className="py-2 xs:py-3 px-2 xs:px-4 w-10 xs:w-16">
                    <div className="flex items-center justify-center gap-1">
                      {getMedalIcon(index)}
                      <span className="font-medium text-xs xs:text-sm">{index + 1}</span>
                    </div>
                  </td>
                  <td className="py-2 xs:py-3 px-2 xs:px-4 w-24 xs:w-32">
                    <div 
                      className="font-medium text-white truncate text-left w-full text-xs xs:text-sm"
                      title={solution.fullName || solution.username}
                    >
                      {solution.fullName || solution.username}
                    </div>
                  </td>
                  <td className="py-2 xs:py-3 px-2 xs:px-4 w-16 xs:w-20">
                    <div className="text-center">
                      <span className="inline-block bg-purple-500/20 text-purple-300 px-1 xs:px-2 py-0.5 xs:py-1 rounded text-[10px] xs:text-xs font-medium">
                        {getLanguageDisplay(solution.language)}
                      </span>
                    </div>
                  </td>
                  <td className="py-2 xs:py-3 px-2 xs:px-4 w-24 xs:w-32">
                    <div className="text-center text-xs xs:text-sm text-gray-300" title={new Date(solution.timestamp).toLocaleString()}>
                      {formatTimestamp(solution.timestamp)}
                    </div>
                  </td>
                  <td className="py-2 xs:py-3 px-2 xs:px-4 w-10 xs:w-16">
                    <div className="flex justify-center">
                      <button 
                        className="text-purple-400 hover:text-purple-300 transition-colors p-1 rounded hover:bg-purple-500/20 cursor-pointer"
                        onClick={() => openModal(solution)}
                      >
                        <Code size={14} className="xs:hidden" />
                        <Code size={16} className="hidden xs:block" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {selectedSolution && (
        <FullScreenModal
          isOpen={isModalOpen}
          onClose={closeModal}
          title={`${selectedSolution.fullName || selectedSolution.username}'s Solution (${getLanguageDisplay(selectedSolution.language)})`}
        >
          <div className="space-y-8">
            {/* Stats Card */}
            <div className="bg-gray-800/50 p-4 xs:p-6 rounded-xl border border-purple-500/20 shadow-xl">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 xs:gap-6">
                <div className="flex items-center gap-3 xs:gap-4">
                  <div className="bg-purple-500/20 p-2 xs:p-3 rounded-full">
                    <Clock size={18} className="text-purple-400 xs:hidden" />
                    <Clock size={24} className="text-purple-400 hidden xs:block" />
                  </div>
                  <div>
                    <div className="text-gray-400 text-xs xs:text-sm">Runtime</div>
                    <div className="text-white text-base xs:text-lg font-medium">{selectedSolution.stats?.runtime || 'N/A'}</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 xs:gap-4">
                  <div className="bg-purple-500/20 p-2 xs:p-3 rounded-full">
                    <HardDrive size={18} className="text-purple-400 xs:hidden" />
                    <HardDrive size={24} className="text-purple-400 hidden xs:block" />
                  </div>
                  <div>
                    <div className="text-gray-400 text-xs xs:text-sm">Memory</div>
                    <div className="text-white text-base xs:text-lg font-medium">{selectedSolution.stats?.memory || 'N/A'}</div>
                  </div>
                </div>
              </div>

              {selectedSolution.stats?.runtimePercentile && (
                <div className="mt-4 xs:mt-6">
                  <div className="flex justify-between text-xs xs:text-sm text-gray-400 mb-1 xs:mb-2">
                    <span>Runtime Percentile</span>
                    <span>{selectedSolution.stats.runtimePercentile?.toFixed(2)}%</span>
                  </div>
                  <div className="h-2.5 w-full bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
                      style={{ width: `${selectedSolution.stats.runtimePercentile}%` }}
                    ></div>
                  </div>

                  {selectedSolution.stats?.memoryPercentile && (
                    <div className="mt-3 xs:mt-4">
                      <div className="flex justify-between text-xs xs:text-sm text-gray-400 mb-1 xs:mb-2">
                        <span>Memory Percentile</span>
                        <span>{selectedSolution.stats.memoryPercentile?.toFixed(2)}%</span>
                      </div>
                      <div className="h-2.5 w-full bg-gray-700 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
                          style={{ width: `${selectedSolution.stats.memoryPercentile}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Approach Section */}
            {selectedSolution.approach && (
              <div className="bg-gray-800/50 p-4 xs:p-6 rounded-xl border border-purple-500/20 shadow-xl">
                <div className="flex items-center gap-2 xs:gap-3 mb-4 xs:mb-6">
                  <Trophy size={18} className="text-purple-400 xs:hidden" />
                  <Trophy size={24} className="text-purple-400 hidden xs:block" />
                  <h3 className="text-white text-lg xs:text-xl font-semibold">Approach</h3>
                </div>
                <pre className="text-gray-300 text-sm xs:text-base whitespace-pre-wrap bg-gray-900/70 p-4 xs:p-6 rounded-lg">
                  {selectedSolution.approach}
                </pre>
              </div>
            )}

            {/* Solution Code Section */}
            <div className="bg-gray-800/50 p-4 xs:p-6 rounded-xl border border-purple-500/20 shadow-xl">
              <div className="flex items-center gap-2 xs:gap-3 mb-4 xs:mb-6">
                <Code size={18} className="text-purple-400 xs:hidden" />
                <Code size={24} className="text-purple-400 hidden xs:block" />
                <h3 className="text-white text-lg xs:text-xl font-semibold">Solution Code</h3>
              </div>
              <div className="overflow-auto bg-gray-900/70 p-4 xs:p-6 rounded-lg" style={{ maxHeight: 'calc(100vh - 300px)' }}>
                <pre className="text-gray-300 text-sm xs:text-base whitespace-pre-wrap">
                  <code>{selectedSolution.code}</code>
                </pre>
              </div>
            </div>
          </div>
        </FullScreenModal>
      )}
    </div>
  );
};

LeaderboardCard.propTypes = {
  solutions: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      username: PropTypes.string.isRequired,
      fullName: PropTypes.string,
      code: PropTypes.string.isRequired,
      language: PropTypes.string.isRequired,
      approach: PropTypes.string,
      stats: PropTypes.shape({
        runtime: PropTypes.string,
        memory: PropTypes.string,
        runtimePercentile: PropTypes.number,
        memoryPercentile: PropTypes.number
      })
    })
  ).isRequired
};

export default LeaderboardCard;
