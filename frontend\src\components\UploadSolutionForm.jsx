import { useState } from 'react';
import PropTypes from 'prop-types';

const UploadSolutionForm = ({ onSubmit, isLoading }) => {
  const [username, setUsername] = useState('');
  const [file, setFile] = useState(null);
  const [language, setLanguage] = useState('python3');
  const [error, setError] = useState('');

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    setFile(selectedFile);
    
    // Try to detect language from file extension
    if (selectedFile) {
      const extension = selectedFile.name.split('.').pop().toLowerCase();
      if (extension === 'py') setLanguage('python3');
      else if (extension === 'js') setLanguage('javascript');
      else if (extension === 'java') setLanguage('java');
      else if (extension === 'cpp' || extension === 'cc') setLanguage('cpp');
      else if (extension === 'c') setLanguage('c');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!username.trim()) {
      setError('Please enter your username');
      return;
    }

    if (!file) {
      setError('Please upload a solution file');
      return;
    }

    try {
      const fileContent = await readFileContent(file);
      onSubmit({ username, code: fileContent, language });
    } catch (err) {
      setError('Error reading file: ' + err.message);
    }
  };

  const readFileContent = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => resolve(event.target.result);
      reader.onerror = (error) => reject(error);
      reader.readAsText(file);
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-2 rounded-md">
          {error}
        </div>
      )}
      
      <div>
        <label htmlFor="username" className="block text-gray-300 mb-1">
          Your Name
        </label>
        <input
          type="text"
          id="username"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          className="w-full bg-gray-800 border border-gray-700 rounded-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          placeholder="Enter your name"
          required
        />
      </div>
      
      <div>
        <label htmlFor="language" className="block text-gray-300 mb-1">
          Language
        </label>
        <select
          id="language"
          value={language}
          onChange={(e) => setLanguage(e.target.value)}
          className="w-full bg-gray-800 border border-gray-700 rounded-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          <option value="python3">Python</option>
          <option value="javascript">JavaScript</option>
          <option value="java">Java</option>
          <option value="cpp">C++</option>
          <option value="c">C</option>
        </select>
      </div>
      
      <div>
        <label htmlFor="solution" className="block text-gray-300 mb-1">
          Solution File
        </label>
        <input
          type="file"
          id="solution"
          onChange={handleFileChange}
          className="w-full bg-gray-800 border border-gray-700 rounded-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-purple-600 file:text-white hover:file:bg-purple-500"
          required
        />
        <p className="text-gray-400 text-xs mt-1">
          Upload your solution code file
        </p>
      </div>
      
      <div className="pt-2">
        <button
          type="submit"
          disabled={isLoading}
          className={`w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-2 rounded-md font-medium transition-all duration-200 ${
            isLoading 
              ? 'opacity-70 cursor-not-allowed' 
              : 'hover:from-purple-700 hover:to-pink-700 hover:shadow-lg'
          }`}
        >
          {isLoading ? 'Submitting...' : 'Submit Solution'}
        </button>
      </div>
    </form>
  );
};

UploadSolutionForm.propTypes = {
  onSubmit: PropTypes.func.isRequired,
  isLoading: PropTypes.bool
};

UploadSolutionForm.defaultProps = {
  isLoading: false
};

export default UploadSolutionForm;
