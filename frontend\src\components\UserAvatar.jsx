import { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { User, LogOut, ChevronDown } from 'lucide-react';
import PropTypes from 'prop-types';

const UserAvatar = ({ user, onLogout }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0 });
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();

  // Calculate dropdown position based on button position
  const updateDropdownPosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + 8, // 8px below the button
        right: window.innerWidth - rect.right // Distance from right edge
      });
    }
  };

  // Get first letter of username or full name for avatar
  const getAvatarLetter = () => {
    if (user?.username) {
      return user.username.charAt(0).toUpperCase();
    }
    if (user?.fullName) {
      return user.fullName.charAt(0).toUpperCase();
    }
    return 'U';
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Check if click is outside both the button and dropdown
      const isClickOutsideButton = buttonRef.current && !buttonRef.current.contains(event.target);
      const isClickOutsideDropdown = dropdownRef.current && !dropdownRef.current.contains(event.target);

      if (isDropdownOpen && isClickOutsideButton && isClickOutsideDropdown) {
        setIsDropdownOpen(false);
      }
    };

    const handleResize = () => {
      if (isDropdownOpen) {
        updateDropdownPosition();
      }
    };

    const handleScroll = () => {
      if (isDropdownOpen) {
        updateDropdownPosition();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isDropdownOpen]);

  // Update position when dropdown opens
  useEffect(() => {
    if (isDropdownOpen) {
      updateDropdownPosition();
    }
  }, [isDropdownOpen]);

  const handleLogout = async () => {
    console.log('Logout clicked'); // Debug log
    setIsDropdownOpen(false);

    // Call the logout function
    await onLogout();

    // If we're on the profile page, navigate to home
    if (location.pathname === '/profile') {
      navigate('/', { replace: true });
    }
  };

  return (
    <div className="relative">
      {/* Avatar Button */}
      <button
        ref={buttonRef}
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center gap-1.5 px-2 py-1.5 rounded-full hover:bg-purple-800/50 transition-all duration-200 group"
      >
        {/* Avatar Circle */}
        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-semibold text-sm shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-105">
          {getAvatarLetter()}
        </div>
        
        {/* Full Name (hidden on mobile) */}
        <span className="hidden md:block text-white font-medium text-sm">
          {user?.fullName || user?.username || 'User'}
        </span>
        
        {/* Dropdown Arrow */}
        <ChevronDown 
          size={14} 
          className={`text-white transition-transform duration-200 ${
            isDropdownOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {/* Dropdown Menu - Rendered in Portal */}
      {isDropdownOpen && createPortal(
        <div 
          ref={dropdownRef} 
          className="fixed w-56 bg-gray-900/95 backdrop-blur-lg rounded-xl border border-purple-500/20 shadow-2xl overflow-hidden"
          style={{ 
            top: `${dropdownPosition.top}px`,
            right: `${dropdownPosition.right}px`,
            zIndex: 999999 
          }}
        >
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-purple-500/20 bg-gradient-to-r from-purple-600/20 to-pink-600/20">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                {getAvatarLetter()}
              </div>
              <div>
                <div className="text-white font-medium text-sm">
                  {user?.fullName || 'User'}
                </div>
                <div className="text-gray-400 text-xs">
                  {user?.email || '<EMAIL>'}
                </div>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-2">
            <Link
              to="/profile"
              className="flex items-center gap-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-purple-500/20 transition-all duration-200"
              onClick={() => {
                console.log('Profile clicked'); // Debug log
                setIsDropdownOpen(false);
              }}
            >
              <User size={18} />
              <span>Profile</span>
            </Link>


            <div className="border-t border-purple-500/20 my-2"></div>

            <button
              onClick={handleLogout}
              className="flex items-center gap-3 px-4 py-3 text-red-400 hover:text-red-300 hover:bg-red-500/20 transition-all duration-200 w-full text-left cursor-pointer"
            >
              <LogOut size={18} />
              <span>Logout</span>
            </button>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

UserAvatar.propTypes = {
  user: PropTypes.shape({
    username: PropTypes.string,
    fullName: PropTypes.string,
    email: PropTypes.string
  }).isRequired,
  onLogout: PropTypes.func.isRequired
};

export default UserAvatar;
