@import "tailwindcss";

/* Prevent horizontal scroll on small devices */
html, body {
  overflow-x: hidden;
  max-width: 100%;
}

/* Ensure all elements respect the viewport width */
* {
  box-sizing: border-box;
  max-width: 100%;
}

/* Prevent any child elements from causing overflow */
.overflow-x-hidden {
  overflow-x: hidden !important;
}

/* Ensure flex containers don't overflow */
.flex, .grid {
  min-width: 0;
}

/* Responsive container utility */
.responsive-container {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Roadmap scroll container */
.scroll-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
  cursor: grab;
}

.scroll-container::-webkit-scrollbar {
  display: none;
}

.scroll-container:active {
  cursor: grabbing;
}

/* Global Custom Scrollbar - Applied to entire website */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(147, 51, 234, 0.6) rgba(17, 24, 39, 0.8);
}

*::-webkit-scrollbar {
  width: 12px;
  height: 12px;
  background: rgba(17, 24, 39, 0.8);
}

*::-webkit-scrollbar-track {
  background: rgba(17, 24, 39, 0.8);
  border-radius: 10px;
  margin: 2px;
}

*::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.8), rgba(168, 85, 247, 0.7), rgba(236, 72, 153, 0.6));
  border-radius: 10px;
  border: 1px solid rgba(147, 51, 234, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

*::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.9), rgba(168, 85, 247, 0.8), rgba(236, 72, 153, 0.7));
  box-shadow: 0 0 12px rgba(147, 51, 234, 0.5);
  border-color: rgba(147, 51, 234, 0.5);
}

*::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(147, 51, 234, 1), rgba(168, 85, 247, 0.9), rgba(236, 72, 153, 0.8));
  box-shadow: 0 0 16px rgba(147, 51, 234, 0.6);
}

*::-webkit-scrollbar-corner {
  background: rgba(17, 24, 39, 0.8);
}

/* Enhanced scrollbar for small containers */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(147, 51, 234, 0.5) rgba(17, 24, 39, 0.8);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  background: rgba(17, 24, 39, 0.8);
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(17, 24, 39, 0.8);
  border-radius: 10px;
  margin: 4px 0;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(147, 51, 234, 0.8), rgba(168, 85, 247, 0.6));
  border-radius: 10px;
  border: 1px solid rgba(147, 51, 234, 0.2);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(147, 51, 234, 0.9), rgba(168, 85, 247, 0.7));
  box-shadow: 0 0 10px rgba(147, 51, 234, 0.4);
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg, rgba(147, 51, 234, 1), rgba(168, 85, 247, 0.8));
}

/* Custom Scrollbar for Blue Theme (Off Campus) */
.custom-scrollbar-blue {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(17, 24, 39, 0.8);
}

.custom-scrollbar-blue::-webkit-scrollbar {
  width: 8px;
  background: rgba(17, 24, 39, 0.8);
}

.custom-scrollbar-blue::-webkit-scrollbar-track {
  background: rgba(17, 24, 39, 0.8);
  border-radius: 10px;
  margin: 4px 0;
}

.custom-scrollbar-blue::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.8), rgba(34, 211, 238, 0.6));
  border-radius: 10px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.custom-scrollbar-blue::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.9), rgba(34, 211, 238, 0.7));
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.4);
}

.custom-scrollbar-blue::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg, rgba(59, 130, 246, 1), rgba(34, 211, 238, 0.8));
}

/* Custom animations for loading states */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes loadingPulse {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* Fade out animation for roadmap instructions */
@keyframes fadeOut {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
}

.animate-loading-pulse {
  animation: loadingPulse 2s ease-in-out infinite;
}
