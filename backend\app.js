import { config } from "dotenv";
config();
import express from "express";
import cookieParser from "cookie-parser";
import morgan from "morgan";
// import bodyParser from "body-parser";
// import helmet from "helmet";
import errorMiddleware from "./middlewares/error.middleware.js";
import { leetcodeError<PERSON>andler } from "./middlewares/leetcode.errorHandler.js";
import { configureLeetcodeCors, configureLeetcodeHelmet } from "./middlewares/leetcode.security.js";

// Import routes
import userRoutes from "./routes/user.routes.js";
import solutionRoutes from "./routes/solution.routes.js";
// Import LeetCode routes
import leetcodeRoutes from "./routes/leetcode.routes.js";
import problemRoutes from "./routes/problem.routes.js";
import submissionRoutes from "./routes/submission.routes.js";
import questionsRoutes from "./routes/questions.routes.js";
import leetcodeAuthRoutes from "./routes/leetcode.auth.routes.js";
import cors from "cors";
import questionRoutes from "./routes/qs.routes.js";
import queryRoutes from "./routes/query.routes.js";
import sheetRoutes from "./routes/sheet.routes.js";

// Define comprehensive allowed origins list for all environments
const allowedOrigins = [
  process.env.FRONTEND_URL,
  'http://localhost:5173',
  'http://localhost:3000',
  'http://localhost:5174',
  'http://localhost:5175',
  'http://127.0.0.1:5173',
  'http://127.0.0.1:3000',
  'https://big-o-web-staging.onrender.com',
  'https://big-o-web-staging.onrender.com/',
  'https://bigo-solveit.onrender.com',
  'https://bigo-solveit-backend.onrender.com'
];

// Single CORS configuration to be used throughout the app
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps, curl, or same-origin requests)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      // In development, allow all origins by commenting the line below
      // and uncommenting the next line
      // callback(new Error('Not allowed by CORS'));
      callback(null, true); // Allow all origins for easier local development
    }
  },
  credentials: true, // Required for cookies, authorization headers
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-csrftoken', 'cookie', 'Set-Cookie', 'Accept', 
                  'X-User-ID', 'X-Auth-Token', 'x-user-id', 'x-auth-token'],
  exposedHeaders: ['Content-Type', 'Authorization', 'Set-Cookie']
};

const app = express();
// Apply CORS configuration once - applying it multiple times causes issues
app.use(cors(corsOptions));

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(morgan("dev")); // log every request to the console for debugging purposes
app.use(cookieParser());

// Handle preflight requests for all routes
app.options('*', cors(corsOptions));

// Security middleware for LeetCode routes - only apply helmet, not duplicate CORS
app.use('/api/v1/leetcode', configureLeetcodeHelmet());


app.use('/ping', (req, res) => {
    res.send('pong');
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'healthy', service: 'BigO Backend with LeetCode API' });
});

//  routes middleware

// Original BigO routes
app.use('/api/v1/user', userRoutes);
app.use('/api/v1/solutions', solutionRoutes);
app.use('/api/v1/query',queryRoutes)

// LeetCode API routes
app.use('/api/v1/leetcode', leetcodeRoutes);
app.use('/api/v1/leetcode/problems', problemRoutes);
app.use('/api/v1/leetcode', submissionRoutes);
app.use('/api/v1/leetcode/questions', questionsRoutes);
app.use('/api/v1/leetcode', leetcodeAuthRoutes);
app.use('/api/v1/question', questionRoutes)
app.use('/api/v1/leetcode/sheet', sheetRoutes);
app.all('*', (req, res, next) => {
    res.status(404).send('OOPS!! 404 page Not Found')
});

// Error handling middleware
app.use('/api/v1/leetcode', leetcodeErrorHandler);
app.use(errorMiddleware);

export default app;