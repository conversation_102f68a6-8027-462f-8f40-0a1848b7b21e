import { Schema, model } from "mongoose"
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import crypto from 'crypto'
import { stat } from "fs"
import { type } from "os"

const sheetQuestionSchema = new Schema({
    topic: {
        type: String,
        enum: [
            'Array', 'String', 'Stack', 'Queue', 'Binary Search', 'Sliding Window / Two Pointers',
            'Recursion', 'Linked List', 'Tree', 'Binary Search Tree', 'Dynamic Programming',
        ]
    },
    title: {
        type: String,
        required:true,
        trim: true,
        maxlength: [100, 'Title cannot exceed 100 characters']
    },
   questionLink:{
        type: String,
        required: true,
        trim: true,
        match: [
            /^(https?:\/\/)?([\w-]+(\.[\w-]+)+)(\/[\w- .\/?%&=]*)?$/,
            'Please fill in a valid URL',
        ]
    },
    solutionLink: {
        type: String,
        required: false,
        trim: true,
        match: [
            /^(https?:\/\/)?([\w-]+(\.[\w-]+)+)(\/[\w- .\/?%&=]*)?$/,
            'Please fill in a valid URL',
        ]
    },
    difficulty: {
        type: String,
        required: [true, 'Please specify the difficulty level'],
        enum: ['Easy', 'Medium', 'Hard'],
        default: 'medium'
    },
    tags: {
        type: [String]
    },
    userList:{
        type: [Schema.Types.ObjectId],
        ref: 'User',
        required: false,
        default: []
    }
}, {
    timestamps: true
})


const SheetQuestion = model('SheetQuestion', sheetQuestionSchema)
export default SheetQuestion