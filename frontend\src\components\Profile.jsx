import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { User, Mail, Calendar, Trophy, Code, Target } from 'lucide-react';
import UserSolutions from './UserSolutions';

const Profile = () => {
  const { user } = useAuth();
  const [showSolutions, setShowSolutions] = useState(false);
  const [userStats, setUserStats] = useState({
    problemsSolved: 0,
    successRate: '100%',
    rank: 'Beginner'
  });

  // Fetch user statistics on component mount
  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        console.log('Fetching user stats for user:', user);

        // If we have a user ID, use the user-specific endpoint
        if (user && user._id) {
          // console.log('Using user-specific endpoint with ID:', user._id);
          
          const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/stats/${user._id}`, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          });

          // console.log('User-specific stats response status:', response.status);

          if (response.ok) {
            const data = await response.json();
            // console.log('Stats data received:', data);
            if (data.success && data.stats) {
              setUserStats(data.stats);
              // console.log('User stats set successfully:', data.stats);
            }
            return;
          }
        }

        // Fallback: Try authenticated endpoint first
        let response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/stats-auth`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });

        // console.log('Authenticated stats response status:', response.status);

        // If authentication fails, try the non-authenticated endpoint
        if (!response.ok && response.status === 401) {
          // console.log('Authentication failed, trying non-authenticated endpoint');
          response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/user/stats`, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            },
            credentials: 'include'
          });
          // console.log('Non-authenticated stats response status:', response.status);
        }

        if (response.ok) {
          const data = await response.json();
          console.log('Stats data received:', data);
          if (data.success && data.stats) {
            setUserStats(data.stats);
            // console.log('User stats set successfully:', data.stats);
          }
        } else {
          console.error('Failed to fetch user stats:', response.status);
          setUserStats({
            problemsSolved: 0,
            successRate: '100%',
            rank: 'Beginner'
          });
        }
      } catch (error) {
        console.error('Error fetching user stats:', error);
        setUserStats({
          problemsSolved: 0,
          successRate: '100%',
          rank: 'Beginner'
        });
      }
    };

    fetchUserStats();
  }, [user]);

  // Get first letter of username or full name for avatar
  const getAvatarLetter = () => {
    if (user?.username) {
      return user.username.charAt(0).toUpperCase();
    }
    if (user?.fullName) {
      return user.fullName.charAt(0).toUpperCase();
    }
    return 'U';
  };

  const handleViewSolutions = () => {
    setShowSolutions(true);
  };

  const handleBackToProfile = () => {
    setShowSolutions(false);
  };

  // If showing solutions, render the UserSolutions component
  if (showSolutions) {
    return <UserSolutions onBack={handleBackToProfile} />;
  }

  return (
    <div className="min-h-screen pt-20 pb-8">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-600/10 rounded-full blur-[80px] animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-72 h-72 bg-pink-600/10 rounded-full blur-[80px] animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-purple-500/5 rounded-full blur-[120px]"></div>
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-purple-400 via-pink-500 to-purple-600 text-transparent bg-clip-text mb-4">
            User Profile
          </h1>
          <p className="text-gray-300 text-lg">Manage your account and view your coding progress</p>
        </div>

        {/* Main Profile Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Card - Left Column */}
          <div className="lg:col-span-1">
            <div className="bg-gray-900/40 backdrop-blur-xl rounded-3xl border border-purple-500/20 shadow-2xl p-8 h-fit">
              {/* Profile Avatar & Info */}
              <div className="text-center mb-8">
                <div className="relative inline-block mb-6">
                  <div className="w-32 h-32 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-4xl shadow-2xl">
                    {getAvatarLetter()}
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-xl"></div>
                </div>
                <h2 className="text-2xl font-bold text-white mb-2">
                  {user?.fullName || 'User'}
                </h2>
                <p className="text-purple-300 font-medium">
                  @{user?.username || 'username'}
                </p>
                <div className="inline-flex items-center gap-2 mt-4 px-4 py-2 bg-purple-500/20 rounded-full">
                  <Trophy size={16} className="text-yellow-400" />
                  <span className="text-white font-medium">{userStats.rank}</span>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="text-center p-4 bg-purple-500/10 rounded-2xl border border-purple-500/20">
                  <div className="text-3xl font-bold text-white mb-1">{userStats.problemsSolved}</div>
                  <div className="text-sm text-gray-400">Problems</div>
                </div>
                <div className="text-center p-4 bg-purple-500/10 rounded-2xl border border-purple-500/20">
                  <div className="text-3xl font-bold text-green-400 mb-1">{userStats.successRate}</div>
                  <div className="text-sm text-gray-400">Success</div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  onClick={handleViewSolutions}
                  className="cursor-pointer w-full bg-gray-700/50 backdrop-blur-sm text-white py-3 px-6 rounded-xl font-medium hover:bg-gray-600/50 transition-all duration-300 border border-gray-600/30"
                >
                  View Solutions
                </button>
              </div>
            </div>
          </div>

          {/* Details Section - Right Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* Personal Information Card */}
            <div className="bg-gray-900/40 backdrop-blur-xl rounded-3xl border border-purple-500/20 shadow-2xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <User size={24} className="text-purple-400" />
                </div>
                Personal Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-400 uppercase tracking-wide">Full Name</label>
                  <div className="p-4 bg-purple-500/10 rounded-xl border border-purple-500/20">
                    <div className="text-white font-medium">{user?.fullName || 'Not provided'}</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-400 uppercase tracking-wide">Username</label>
                  <div className="p-4 bg-purple-500/10 rounded-xl border border-purple-500/20">
                    <div className="text-white font-medium">@{user?.username || 'Not provided'}</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-400 uppercase tracking-wide">Email Address</label>
                  <div className="p-4 bg-purple-500/10 rounded-xl border border-purple-500/20">
                    <div className="text-white font-medium">{user?.email || 'Not provided'}</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-400 uppercase tracking-wide">Member Since</label>
                  <div className="p-4 bg-purple-500/10 rounded-xl border border-purple-500/20">
                    <div className="text-white font-medium">
                      {user?.createdAt ? new Date(user.createdAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      }) : 'Unknown'}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics Card */}
            <div className="bg-gray-900/40 backdrop-blur-xl rounded-3xl border border-purple-500/20 shadow-2xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <Trophy size={24} className="text-purple-400" />
                </div>
                Coding Statistics
              </h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-2xl border border-purple-500/20">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-purple-500/20 rounded-xl">
                      <Code size={24} className="text-purple-400" />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-white mb-2">{userStats.problemsSolved}</div>
                  <div className="text-gray-400 font-medium">Problems Solved</div>
                </div>
                
                <div className="text-center p-6 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-2xl border border-green-500/20">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-green-500/20 rounded-xl">
                      <Target size={24} className="text-green-400" />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-green-400 mb-2">{userStats.successRate}</div>
                  <div className="text-gray-400 font-medium">Success Rate</div>
                </div>
                
                <div className="text-center p-6 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-2xl border border-yellow-500/20">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-yellow-500/20 rounded-xl">
                      <Trophy size={24} className="text-yellow-400" />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-yellow-400 mb-2">{userStats.rank}</div>
                  <div className="text-gray-400 font-medium">Current Rank</div>
                </div>
              </div>
            </div>

            {/* Additional Stats Card */}
            <div className="bg-gray-900/40 backdrop-blur-xl rounded-3xl border border-purple-500/20 shadow-2xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6">Progress Overview</h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="p-6 bg-purple-500/10 rounded-2xl border border-purple-500/20">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                      <span className="text-purple-400 font-bold">XP</span>
                    </div>
                    <div>
                      <div className="text-white font-semibold">Experience Points</div>
                      <div className="text-gray-400 text-sm">Total earned</div>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-purple-400">{user?.xp || 0}</div>
                </div>
                
                <div className="p-6 bg-orange-500/10 rounded-2xl border border-orange-500/20">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center">
                      <span className="text-orange-400 font-bold">🔥</span>
                    </div>
                    <div>
                      <div className="text-white font-semibold">Current Streak</div>
                      <div className="text-gray-400 text-sm">Daily solving</div>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-orange-400">{user?.streak || 0} days</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
