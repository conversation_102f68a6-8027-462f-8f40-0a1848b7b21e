import { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, ExternalLink, CheckCircle, Circle, ArrowLeft, Youtube, Eye, Code } from 'lucide-react';
import { Link } from 'react-router-dom';
import sheetsData from '../data/sheetsData.json';
import CodeEditorForm from './CodeEditorForm';
import { useAuth } from '../contexts/AuthContext';

const OffCampus = () => {
  // Commented out for Coming Soon page
  // const [openAccordions, setOpenAccordions] = useState(new Set());
  // const [completedProblems, setCompletedProblems] = useState(new Set());
  // const [data, setData] = useState(null);
  // const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  // const [selectedProblem, setSelectedProblem] = useState(null);
  // const [isSubmitting, setIsSubmitting] = useState(false);
  // const [submissionResult, setSubmissionResult] = useState(null);
  // const [acceptedSolutions, setAcceptedSolutions] = useState([]);
  // const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  // const { isAuthenticated, user, refreshUser, updateUserData } = useAuth();

  // useEffect(() => {
  //   setData(sheetsData.offCampus);
  //   // Load completed problems from localStorage
  //   const saved = localStorage.getItem('offCampusCompleted');
  //   if (saved) {
  //     setCompletedProblems(new Set(JSON.parse(saved)));
  //   }
    
  //   // Load user's sheet questions from backend if authenticated
  //   if (isAuthenticated()) {
  //     loadUserSheetQuestions();
  //   }
  // }, [isAuthenticated]);

  // // Load user's completed sheet questions from backend
  // const loadUserSheetQuestions = async () => {
  //   try {
  //     const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/leetcode/sheet/user-questions`, {
  //       method: 'GET',
  //       headers: {
  //         'Accept': 'application/json'
  //       },
  //       credentials: 'include'
  //     });

  //     if (response.ok) {
  //       const result = await response.json();
  //       if (result.success) {
  //         // Map backend questions to problem IDs and update completed state
  //         const backendCompleted = new Set();
          
  //         result.questions.forEach(question => {
  //           // Find matching problem in sheetsData by title
  //           if (data?.topics) {
  //             data.topics.forEach(topic => {
  //               topic.problems.forEach(problem => {
  //                 if (problem.title === question.title) {
  //                   backendCompleted.add(problem.id);
  //                 }
  //               });
  //             });
  //           }
  //         });

  //         // Merge with localStorage data
  //         const localCompleted = localStorage.getItem('offCampusCompleted');
  //         if (localCompleted) {
  //           const localSet = new Set(JSON.parse(localCompleted));
  //           backendCompleted.forEach(id => localSet.add(id));
  //           setCompletedProblems(localSet);
  //           localStorage.setItem('offCampusCompleted', JSON.stringify([...localSet]));
  //         } else {
  //           setCompletedProblems(backendCompleted);
  //           localStorage.setItem('offCampusCompleted', JSON.stringify([...backendCompleted]));
  //         }
  //       }
  //     }
  //   } catch (error) {
  //     console.error('Error loading user sheet questions:', error);
  //     // Continue with localStorage data if backend fails
  //   }
  // };

  // // Cleanup effect to restore scrolling when component unmounts or modal closes
  // useEffect(() => {
  //   return () => {
  //     // Restore scrolling when component unmounts
  //     document.body.style.overflow = 'unset';
  //   };
  // }, []);

  // // Handle escape key to close modal
  // useEffect(() => {
  //   const handleEscapeKey = (event) => {
  //     if (event.key === 'Escape' && isUploadModalOpen) {
  //       handleCloseModal();
  //     }
  //   };

  //   if (isUploadModalOpen) {
  //     document.addEventListener('keydown', handleEscapeKey);
  //     return () => {
  //       document.removeEventListener('keydown', handleEscapeKey);
  //     };
  //   }
  // }, [isUploadModalOpen]);

  // const toggleAccordion = (topicId) => {
  //   const newOpen = new Set(openAccordions);
  //   if (newOpen.has(topicId)) {
  //     newOpen.delete(topicId);
  //   } else {
  //     newOpen.add(topicId);
  //   }
  //   setOpenAccordions(newOpen);
  // };

  // const toggleProblemCompletion = async (problem) => {
  //   // Check if user is authenticated
  //   if (!isAuthenticated()) {
  //     setShowLoginPrompt(true);
  //     return;
  //   }

  //   try {
  //     // Debug logging
  //     console.log('=== FRONTEND DEBUG ===');
  //     console.log('Document cookies:', document.cookie);
  //     console.log('User object:', user);
  //     console.log('Problem data:', problem);
      
  //     // Call backend API to toggle question status
  //     const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/leetcode/sheet/toggleq`, {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //         'Accept': 'application/json'
  //       },
  //       credentials: 'include', // Include cookies for authentication
  //       body: JSON.stringify({
  //         userId: user?._id, // Add user ID as fallback
  //         title: problem.title,
  //         questionLink: problem.link,
  //         difficulty: problem.difficulty,
  //         tags: problem.tags || []
  //       })
  //     });

  //     if (!response.ok) {
  //       const errorData = await response.json();
  //       throw new Error(errorData.message || 'Failed to update question status');
  //     }

  //     const result = await response.json();
      
  //     // Update local state based on backend response
  //     const newCompleted = new Set(completedProblems);
  //     if (result.solved) {
  //       newCompleted.add(problem.id);
  //     } else {
  //       newCompleted.delete(problem.id);
  //     }
      
  //     setCompletedProblems(newCompleted);
  //     // Save to localStorage
  //     localStorage.setItem('offCampusCompleted', JSON.stringify([...newCompleted]));
      
  //     console.log(`Question ${result.solved ? 'marked as solved' : 'unmarked'}:`, problem.title);
      
  //   } catch (error) {
  //     console.error('Error toggling problem completion:', error);
  //     // Show error message to user (you can implement a toast notification here)
  //     alert(error.message || 'Failed to update question status. Please try again.');
  //   }
  // };

  // const getDifficultyColor = (difficulty) => {
  //   switch (difficulty.toLowerCase()) {
  //     case 'easy':
  //       return 'text-green-400 bg-green-400/10 border-green-400/20';
  //     case 'medium':
  //       return 'text-orange-400 bg-orange-400/10 border-orange-400/20';
  //     case 'hard':
  //       return 'text-red-400 bg-red-400/10 border-red-400/20';
  //     default:
  //       return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
  //   }
  // };

  // const getCompletedCount = (topicProblems) => {
  //   return topicProblems.filter(problem => completedProblems.has(problem.id)).length;
  // };

  // const getProgressPercentage = (topicProblems) => {
  //   const completed = getCompletedCount(topicProblems);
  //   const total = topicProblems.length;
  //   return total > 0 ? (completed / total) * 100 : 0;
  // };

  // // Convert problem data to challengeData format for modal
  // const convertProblemToChallengeData = (problem) => {
  //   const leetcodeUrl = problem.link;
  //   // Use title slug as questionId temporarily - this will be updated when problemDetails are fetched
  //   const titleSlug = leetcodeUrl.split('/problems/')[1]?.split('/')[0] || "unknown";
    
  //   return {
  //     id: problem.id, // Preserve original problem ID for completion tracking
  //     questionId: titleSlug, // This will be updated in CodeEditorForm when problemDetails are fetched
  //     questionTitle: problem.title,
  //     questionLink: leetcodeUrl,
  //     difficulty: problem.difficulty,
  //     topicTags: problem.tags || [],
  //     codeSnippets: [] // Will be populated by the modal if needed
  //   };
  // };

  // // Handle write solution button click
  // const handleWriteSolution = (problem) => {
  //   if (!isAuthenticated()) {
  //     setShowLoginPrompt(true);
  //     return;
  //   }

  //   // Prevent background scrolling when modal opens
  //   document.body.style.overflow = 'hidden';

  //   setSelectedProblem(convertProblemToChallengeData(problem));
  //   setIsUploadModalOpen(true);
  //   setSubmissionResult(null);
  // };

  // // Handle close modal
  // const handleCloseModal = () => {
  //   if (window.submissionPollingTimeout) {
  //     clearTimeout(window.submissionPollingTimeout);
  //     window.submissionPollingTimeout = null;
  //   }

  //   // Restore background scrolling when modal closes
  //   document.body.style.overflow = 'unset';

  //   setIsUploadModalOpen(false);
  //   setSelectedProblem(null);
  //   setSubmissionResult(null);
  // };

  // // YouTube search URL generator
  // const getYouTubeSearchUrl = (problemTitle) => {
  //   const searchQuery = `${problemTitle} leetcode solution explanation`;
  //   return `https://www.youtube.com/results?search_query=${encodeURIComponent(searchQuery)}`;
  // };

  // // View solutions - redirect to a solutions page (you can implement this later)
  // const handleViewSolutions = (problem) => {
  //   // For now, open LeetCode discuss section
  //   const discussUrl = problem.link.replace('/problems/', '/problems/').replace('/', '/discuss/');
  //   window.open(discussUrl, '_blank');
  // };

  // // Function to save solution to database
  // const saveSolutionToDatabase = async ({ submissionId, username, code, language, approach, data, challengeData }) => {
  //   try {
  //     const titleSlug = challengeData.questionLink.split('/problems/')[1]?.split('/')[0];

  //     const solutionData = {
  //       problemTitle: challengeData.questionTitle,
  //       problemSlug: titleSlug,
  //       difficulty: challengeData.difficulty,
  //       code,
  //       language,
  //       approach: approach || '',
  //       status: data.status_msg || 'Accepted',
  //       topicTags: challengeData.topicTags || [],
  //       userId: user?._id,
  //       isSheetQuestion: true // Flag to identify this as a sheet question
  //     };

  //     const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/leetcode/aftersubmit`, {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //         'Accept': 'application/json'
  //       },
  //       credentials: 'include',
  //       body: JSON.stringify(solutionData)
  //     });

  //     if (!response.ok) {
  //       const errorData = await response.json();
  //       throw new Error(errorData.message || 'Failed to save solution');
  //     }

  //     const result = await response.json();
  //     console.log('Solution saved to database:', result);

  //     if (result.isDuplicate) {
  //       return {
  //         ...result,
  //         warningMessage: result.warning || 'This solution appears to match an existing submission. Please try solving with your own unique approach for better learning.'
  //       };
  //     }

  //     return result;
  //   } catch (error) {
  //     console.error('Error saving solution to database:', error);
  //     throw error;
  //   }
  // };

  // // Handle submit solution (simplified version from DailyChallengeCard)
  // const handleSubmitSolution = async ({ username, code, language, approach }) => {
  //   if (window.submissionPollingTimeout) {
  //     clearTimeout(window.submissionPollingTimeout);
  //     window.submissionPollingTimeout = null;
  //   }

  //   setIsSubmitting(true);
  //   setSubmissionResult({
  //     loading: true,
  //     message: 'Submitting solution...',
  //     details: 'Please wait while we submit your solution to LeetCode.'
  //   });

  //   try {
  //     const titleSlug = selectedProblem.questionLink.split('/problems/')[1]?.split('/')[0];

  //     if (!titleSlug) {
  //       throw new Error('Could not determine problem slug from question link');
  //     }

  //     const payload = {
  //       lang: language,
  //       question_id: selectedProblem.realQuestionId || selectedProblem.questionId, // Use real questionId if available
  //       typed_code: code
  //     };

  //     setSubmissionResult({
  //       loading: true,
  //       message: 'Submitting solution to LeetCode...',
  //       details: 'This may take a few moments.'
  //     });

  //     const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/leetcode/submit/${titleSlug}`, {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //         'Accept': 'application/json'
  //       },
  //       body: JSON.stringify(payload)
  //     });

  //     const contentType = response.headers.get('content-type');
  //     let data;

  //     if (contentType && contentType.includes('application/json')) {
  //       const text = await response.text();
  //       try {
  //         data = text ? JSON.parse(text) : {};
  //       } catch (parseError) {
  //         console.error('Error parsing JSON response:', parseError, 'Response text:', text);
  //         throw new Error('Invalid response from server: ' + (text || 'Empty response'));
  //       }
  //     } else {
  //       const text = await response.text();
  //       console.error('Non-JSON response:', text);
  //       throw new Error('Server returned non-JSON response');
  //     }

  //     if (!response.ok) {
  //       throw new Error(data.error || `Failed to submit solution (${response.status})`);
  //     }

  //     // For demonstration, show a success message after a short delay
  //     setTimeout(() => {
  //       setSubmissionResult({
  //         success: true,
  //         message: 'Solution submitted successfully!',
  //         fullData: { status_msg: 'Accepted', total_correct: 'N/A', total_testcases: 'N/A' }
  //       });

  //       // Mark the current problem as completed
  //       if (selectedProblem && selectedProblem.id) {
  //         const newCompleted = new Set(completedProblems);
  //         newCompleted.add(selectedProblem.id);
  //         setCompletedProblems(newCompleted);
  //         localStorage.setItem('offCampusCompleted', JSON.stringify([...newCompleted]));
  //       }

  //       // Save to database if authenticated
  //       if (isAuthenticated()) {
  //         saveSolutionToDatabase({
  //           submissionId: Date.now(),
  //           username,
  //           code,
  //           language,
  //           approach,
  //           data: { status_msg: 'Accepted' },
  //           challengeData: selectedProblem
  //         }).catch(console.error);
  //       }
  //     }, 2000);

  //   } catch (error) {
  //     console.error('Error submitting solution:', error);
  //     setSubmissionResult({
  //       success: false,
  //       message: error.message || 'An error occurred while submitting your solution'
  //     });
  //   } finally {
  //     setIsSubmitting(false);
  //   }
  // };

  return (
    <div className="min-h-screen ml-20 mr-20 bg-gradient-to-r  px-4 sm:px-8 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <Link
            to="/sheets"
            className="inline-flex items-center gap-2 text-purple-300 hover:text-white transition-all duration-200 mb-6 bg-purple-900/20 hover:bg-purple-800/30 backdrop-blur-sm border border-purple-500/20 hover:border-purple-400/40 px-4 py-2 rounded-lg cursor-pointer"
          >
            <ArrowLeft size={20} />
            Back to Sheets
          </Link>
          
          <h1 className="text-4xl sm:text-5xl font-bold text-white mb-4">
            Off Campus Sheet
          </h1>
        </div>

        {/* Coming Soon Section */}
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center max-w-2xl mx-auto">
            {/* Animated clock icon */}
            <div className="mb-8 flex justify-center">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-violet-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
                <div className="relative bg-gradient-to-br from-purple-900/30 to-transparent backdrop-blur-sm border border-purple-500/20 rounded-full p-8">
                  <svg 
                    className="w-24 h-24 text-purple-400 animate-pulse" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <circle cx="12" cy="12" r="10" strokeWidth="2"/>
                    <polyline points="12,6 12,12 16,14" strokeWidth="2"/>
                  </svg>
                </div>
              </div>
            </div>

            {/* Coming Soon Text */}
            <div className="relative p-8 rounded-2xl bg-gradient-to-br from-purple-900/30 to-transparent backdrop-blur-sm border border-purple-500/20">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent rounded-2xl blur-[40px]"></div>
              <div className="relative z-10">
                <h2 className="text-5xl sm:text-6xl font-bold mb-6 bg-gradient-to-r from-purple-400 to-violet-400 bg-clip-text text-transparent">
                  Coming Soon
                </h2>
                <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                  We're working hard to bring you the most comprehensive Off Campus preparation sheet. 
                  This will include curated problems specifically designed for off-campus placements, 
                  company-wise question patterns, and strategic preparation guides.
                </p>
                
                {/* Features preview */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                  <div className="bg-purple-800/20 backdrop-blur-sm rounded-xl p-6 border border-purple-500/20">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-violet-500 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-white">Company-wise Questions</h3>
                    </div>
                    <p className="text-gray-400 text-sm">
                      Problems categorized by companies like Google, Amazon, Microsoft, and more.
                    </p>
                  </div>
                  
                  <div className="bg-purple-800/20 backdrop-blur-sm rounded-xl p-6 border border-purple-500/20">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-violet-500 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-white">Strategic Preparation</h3>
                    </div>
                    <p className="text-gray-400 text-sm">
                      Optimized study plans and problem-solving strategies for off-campus success.
                    </p>
                  </div>
                  
                  <div className="bg-purple-800/20 backdrop-blur-sm rounded-xl p-6 border border-purple-500/20">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-violet-500 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-white">Progress Tracking</h3>
                    </div>
                    <p className="text-gray-400 text-sm">
                      Advanced analytics to track your preparation progress and identify weak areas.
                    </p>
                  </div>
                  
                  <div className="bg-purple-800/20 backdrop-blur-sm rounded-xl p-6 border border-purple-500/20">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-violet-500 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-white">Study Resources</h3>
                    </div>
                    <p className="text-gray-400 text-sm">
                      Comprehensive guides, tutorials, and interview preparation materials.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OffCampus;
