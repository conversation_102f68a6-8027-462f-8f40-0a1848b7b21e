import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Code, Calendar, ExternalLink, ArrowLeft, Trophy } from 'lucide-react';
import FullScreenModal from './FullScreenModal';

const UserSolutions = ({ onBack }) => {
  const { user } = useAuth();
  const [solutions, setSolutions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSolution, setSelectedSolution] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const fetchUserSolutions = async () => {
      const userId = user?.id || user?._id;
      if (!userId) {
        setError('User not found');
        setLoading(false);
        return;
      }

      try {
        const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/solutions/user/${userId}`, {
          headers: {
            'Accept': 'application/json'
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error('Failed to fetch solutions');
        }

        const data = await response.json();
        setSolutions(data.solutions || []);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUserSolutions();
  }, [user]);

  const openModal = (solution) => {
    setSelectedSolution(solution);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setSelectedSolution(null);
    setIsModalOpen(false);
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'text-green-400 bg-green-400/20';
      case 'medium':
        return 'text-yellow-400 bg-yellow-400/20';
      case 'hard':
        return 'text-red-400 bg-red-400/20';
      default:
        return 'text-gray-400 bg-gray-400/20';
    }
  };

  const getLanguageDisplay = (lang) => {
    const langMap = {
      'python3': 'Python',
      'javascript': 'JavaScript',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C',
      'csharp': 'C#',
      'ruby': 'Ruby',
      'go': 'Go',
      'php': 'PHP'
    };
    return langMap[lang] || lang;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen pt-20 pb-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col items-center justify-center h-64">
            {/* Loading Animation */}
            <div className="relative mb-8">
              <div className="w-20 h-20 mx-auto relative">
                <div className="absolute inset-0 rounded-full border-4 border-purple-800/30"></div>
                <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-purple-400 animate-spin"></div>
                <div className="absolute inset-2 rounded-full border-2 border-transparent border-t-pink-400 animate-spin animation-delay-150"></div>
                <div className="absolute inset-3 rounded-full border-2 border-transparent border-t-purple-300 animate-spin animation-delay-300"></div>
              </div>
            </div>

            {/* Loading Text */}
            <h2 className="text-2xl font-bold text-white mb-4">
              Loading Your Solutions
            </h2>
            <p className="text-purple-200 mb-8 text-center">
              Fetching your coding achievements...
            </p>

            {/* Animated Progress Bar */}
            <div className="w-full max-w-md bg-gray-800/50 rounded-full h-2 mb-4 overflow-hidden">
              <div className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse loading-bar"></div>
            </div>

            {/* Loading Steps */}
            <div className="text-sm text-gray-400 space-y-2">
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <span>Connecting to database</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse animation-delay-300"></div>
                <span>Retrieving your solutions</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse animation-delay-600"></div>
                <span>Organizing by date</span>
              </div>
            </div>
          </div>

          <style jsx>{`
            .loading-bar {
              animation: loading-progress 2s ease-in-out infinite;
            }

            @keyframes loading-progress {
              0% {
                width: 0%;
                opacity: 0.5;
              }
              50% {
                width: 70%;
                opacity: 1;
              }
              100% {
                width: 100%;
                opacity: 0.8;
              }
            }

            .animation-delay-150 {
              animation-delay: 150ms;
            }

            .animation-delay-300 {
              animation-delay: 300ms;
            }

            .animation-delay-600 {
              animation-delay: 600ms;
            }
          `}</style>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen pt-20 pb-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-red-400 text-lg">Error: {error}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 pb-8">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-600/10 rounded-full blur-[80px] animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-72 h-72 bg-pink-600/10 rounded-full blur-[80px] animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-purple-500/5 rounded-full blur-[120px]"></div>
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8 sm:mb-12">
          <button
            onClick={onBack}
            className="flex items-center gap-1 sm:gap-2 bg-gray-700/50 backdrop-blur-sm text-white px-3 sm:px-4 py-1.5 sm:py-2 rounded-xl font-medium hover:bg-gray-600/50 transition-all duration-300 border border-gray-600/30 cursor-pointer hover:transform hover:-translate-y-0.5 shadow-lg hover:shadow-xl text-sm sm:text-base"
          >
            <ArrowLeft size={16} className="sm:hidden" />
            <ArrowLeft size={20} className="hidden sm:block" />
            Back to Profile
          </button>
        </div>

        <div className="text-center mb-8 sm:mb-12">
          <h1 className="text-3xl xs:text-4xl sm:text-5xl font-bold bg-gradient-to-r from-purple-400 via-pink-500 to-purple-600 text-transparent bg-clip-text mb-2 sm:mb-4">
            My Solutions
          </h1>
          <p className="text-gray-300 text-base sm:text-lg">
            {solutions.length} solution{solutions.length !== 1 ? 's' : ''} submitted
          </p>
        </div>

        {/* Solutions Grid */}
        {solutions.length === 0 ? (
          <div className="bg-gray-900/40 backdrop-blur-xl rounded-3xl border border-purple-500/20 shadow-2xl p-6 sm:p-8 md:p-12 text-center">
            <Trophy size={48} className="text-gray-600 mx-auto mb-3 sm:mb-4 sm:hidden" />
            <Trophy size={64} className="text-gray-600 mx-auto mb-3 sm:mb-4 hidden sm:block" />
            <h3 className="text-xl font-bold text-white mb-2">No Solutions Yet</h3>
            <p className="text-gray-400">Start solving problems to see your solutions here!</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {solutions.map((solution) => (
              <div
                key={solution.id}
                className="bg-gray-900/40 backdrop-blur-xl rounded-2xl border border-purple-500/20 shadow-2xl p-4 sm:p-6 hover:border-purple-500/50 transition-all duration-300 cursor-pointer hover:transform hover:scale-105"
                onClick={() => openModal(solution)}
              >
                <div className="flex items-start justify-between mb-3 sm:mb-4">
                  <h3 className="text-base sm:text-lg font-semibold text-white truncate flex-1 mr-2">
                    {solution.problemTitle}
                  </h3>
                  {solution.problemLink && (
                    <a
                      href={solution.problemLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-purple-400 hover:text-purple-300 transition-colors flex-shrink-0"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <ExternalLink size={14} className="sm:hidden" />
                      <ExternalLink size={16} className="hidden sm:block" />
                    </a>
                  )}
                </div>

                <div className="flex items-center gap-1.5 sm:gap-2 mb-2 sm:mb-3">
                  <span className={`px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-[10px] sm:text-xs font-medium ${getDifficultyColor(solution.difficulty)}`}>
                    {solution.difficulty?.charAt(0).toUpperCase() + solution.difficulty?.slice(1)}
                  </span>
                  <span className="bg-purple-500/20 text-purple-300 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded text-[10px] sm:text-xs font-medium">
                    {getLanguageDisplay(solution.language)}
                  </span>
                </div>

                <div className="flex items-center gap-1.5 sm:gap-2 text-gray-400 text-xs sm:text-sm">
                  <Calendar size={12} className="sm:hidden" />
                  <Calendar size={14} className="hidden sm:block" />
                  <span>{formatDate(solution.timestamp)}</span>
                </div>

                {solution.approach && (
                  <div className="mt-2 sm:mt-3 text-gray-300 text-xs sm:text-sm line-clamp-2">
                    <strong>Approach:</strong> {solution.approach}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Solution Modal */}
        {selectedSolution && (
          <FullScreenModal
            isOpen={isModalOpen}
            onClose={closeModal}
            title={`${selectedSolution.problemTitle} (${getLanguageDisplay(selectedSolution.language)})`}
          >
            <div className="space-y-6">
              {/* Problem Info */}
              <div className="bg-gray-800/50 p-4 sm:p-6 rounded-xl border border-purple-500/20">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-3 sm:mb-4 gap-2 sm:gap-0">
                  <h3 className="text-lg sm:text-xl font-semibold text-white">{selectedSolution.problemTitle}</h3>
                  {selectedSolution.problemLink && (
                    <a
                      href={selectedSolution.problemLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1.5 sm:gap-2 text-purple-400 hover:text-purple-300 transition-colors text-sm sm:text-base"
                    >
                      <ExternalLink size={14} className="sm:hidden" />
                      <ExternalLink size={16} className="hidden sm:block" />
                      View Problem
                    </a>
                  )}
                </div>
                
                <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm">
                  <span className={`px-2 sm:px-3 py-0.5 sm:py-1 rounded-full font-medium ${getDifficultyColor(selectedSolution.difficulty)}`}>
                    {selectedSolution.difficulty?.charAt(0).toUpperCase() + selectedSolution.difficulty?.slice(1)}
                  </span>
                  <span className="bg-purple-500/20 text-purple-300 px-2 sm:px-3 py-0.5 sm:py-1 rounded font-medium">
                    {getLanguageDisplay(selectedSolution.language)}
                  </span>
                  <span className="text-gray-400">
                    Solved on {formatDate(selectedSolution.timestamp)}
                  </span>
                </div>
              </div>

              {/* Approach */}
              {selectedSolution.approach && (
                <div className="bg-gray-800/50 p-4 sm:p-6 rounded-xl border border-purple-500/20">
                  <h4 className="text-base sm:text-lg font-semibold text-white mb-2 sm:mb-3">Approach</h4>
                  <p className="text-gray-300 text-sm sm:text-base">{selectedSolution.approach}</p>
                </div>
              )}

              {/* Code */}
              <div className="bg-gray-800/50 p-4 sm:p-6 rounded-xl border border-purple-500/20">
                <div className="flex items-center gap-1.5 sm:gap-2 mb-3 sm:mb-4">
                  <Code size={16} className="text-purple-400 sm:hidden" />
                  <Code size={20} className="text-purple-400 hidden sm:block" />
                  <h4 className="text-base sm:text-lg font-semibold text-white">Solution Code</h4>
                </div>
                <div className="bg-gray-900 p-3 sm:p-4 rounded-lg overflow-auto max-h-72 sm:max-h-96">
                  <pre className="text-gray-300 text-xs sm:text-sm whitespace-pre-wrap">
                    <code>{selectedSolution.code}</code>
                  </pre>
                </div>
              </div>
            </div>
          </FullScreenModal>
        )}
      </div>
    </div>
  );
};

export default UserSolutions;
